<?php
if (!defined('ABSPATH')) {
    exit;
}

class Woo_Custom_Status_Integration {
    private $db;
    private $dashicon_map = [
        'dashicons-admin-post' => 'f109',
        'dashicons-media-text' => 'f123',
        'dashicons-media-document' => 'f497',
        'dashicons-media-spreadsheet' => 'f495',
        'dashicons-media-interactive' => 'f496',
        'dashicons-admin-page' => 'f105',
        'dashicons-admin-comments' => 'f101',
        'dashicons-admin-appearance' => 'f100',
        'dashicons-cart' => 'f174',
        'dashicons-products' => 'f312',
        'dashicons-awards' => 'f313',
        'dashicons-forms' => 'f314',
        'dashicons-testimonial' => 'f473',
        'dashicons-portfolio' => 'f322',
        'dashicons-book' => 'f330',
        'dashicons-download' => 'f316',
        'dashicons-upload' => 'f317',
        'dashicons-backup' => 'f321',
        'dashicons-clock' => 'f469',
        'dashicons-lightbulb' => 'f339',
        'dashicons-microphone' => 'f482',
        'dashicons-camera' => 'f306',
        'dashicons-images-alt' => 'f232',
        'dashicons-images-alt2' => 'f233',
        'dashicons-video-alt' => 'f234',
        'dashicons-video-alt2' => 'f235',
        'dashicons-video-alt3' => 'f236',
        'dashicons-hammer' => 'f308',
        'dashicons-art' => 'f309',
        'dashicons-migrate' => 'f310',
        'dashicons-performance' => 'f311',
        'dashicons-universal-access' => 'f483',
        'dashicons-tickets' => 'f486',
        'dashicons-nametag' => 'f484',
        'dashicons-clipboard' => 'f481',
        'dashicons-heart' => 'f487',
        'dashicons-megaphone' => 'f488',
        'dashicons-schedule' => 'f489',
        'dashicons-wordpress' => 'f120',
        'dashicons-wordpress-alt' => 'f324',
        'dashicons-pressthis' => 'f157',
        'dashicons-update' => 'f463',
        'dashicons-screenoptions' => 'f180',
        'dashicons-info' => 'f348'
    ];

    public function __construct() {
        $this->db = new Woo_Custom_Status_DB();
        
        // Aggiungi gli status custom al menu a tendina degli ordini
        add_filter('wc_order_statuses', [$this, 'add_custom_statuses_to_dropdown']);
        add_filter('woocommerce_register_shop_order_post_statuses', [$this, 'register_custom_statuses']);

        // Aggiungi i pulsanti azione nella lista ordini
        add_filter('woocommerce_admin_order_actions', [$this, 'add_custom_action_buttons'], 100, 2);
        add_action('admin_head', [$this, 'add_custom_action_button_styles']);

        // Personalizza lo stile degli status
        add_action('admin_head', [$this, 'add_status_styles']);

        // Gestisci il cambio di status - priorità più alta per assicurarci che venga eseguito dopo il cambio effettivo
        add_action('woocommerce_order_status_changed', [$this, 'handle_status_change'], 999, 4);
        
        // Aggiungi supporto per il cambio status tramite bulk actions e singolo ordine
        add_action('woocommerce_order_edit_status', [$this, 'handle_manual_status_change'], 10, 2);
    }

    /**
     * Registra gli status custom nel sistema
     */
    public function register_custom_statuses($order_statuses) {
        $custom_statuses = $this->db->get_all_statuses();
        
        foreach ($custom_statuses as $status) {
            $status_key = 'wc-' . $status->status_slug;
            $order_statuses[$status_key] = [
                'label' => $status->status_name,
                'public' => false,
                'exclude_from_search' => false,
                'show_in_admin_all_list' => true,
                'show_in_admin_status_list' => true,
                'label_count' => _n_noop($status->status_name . ' <span class="count">(%s)</span>', 
                                       $status->status_name . ' <span class="count">(%s)</span>')
            ];
        }
        
        return $order_statuses;
    }

    /**
     * Aggiungi gli status custom al menu a tendina
     */
    public function add_custom_statuses_to_dropdown($order_statuses) {
        $custom_statuses = $this->db->get_all_statuses();
        
        foreach ($custom_statuses as $status) {
            $status_key = 'wc-' . $status->status_slug;
            $order_statuses[$status_key] = $status->status_name;
        }
        
        return $order_statuses;
    }

    /**
     * Aggiungi i pulsanti azione nella lista ordini
     */
    public function add_custom_action_buttons($actions, $order) {
        $custom_statuses = $this->db->get_all_statuses();
        $current_status = str_replace('wc-', '', $order->get_status());
        
        foreach ($custom_statuses as $status) {
            if (!$status->show_action_button) {
                continue;
            }

            $status_key = $status->status_slug;
            
            // Non mostrare il pulsante se l'ordine è già in questo stato
            if ('wc-' . $status_key === $order->get_status()) {
                continue;
            }

            // Controlla se il pulsante deve essere mostrato per lo status corrente
            if (!empty($status->allowed_statuses)) {
                $allowed_statuses = explode(',', $status->allowed_statuses);
                if (!in_array($current_status, $allowed_statuses)) {
                    continue;
                }
            }

            $action_slug = 'custom_status_' . $status_key;
            
            $actions[$action_slug] = [
                'url' => wp_nonce_url(admin_url('admin-ajax.php?action=woocommerce_mark_order_status&status=' . $status_key . '&order_id=' . $order->get_id()), 'woocommerce-mark-order-status'),
                'name' => $status->status_name,
                'action' => $action_slug,
            ];
        }
        
        return $actions;
    }

    /**
     * Aggiungi gli stili CSS per i pulsanti azione
     */
    public function add_custom_action_button_styles() {
        $custom_statuses = $this->db->get_all_statuses();
        $styles = '<style>';
        
        foreach ($custom_statuses as $status) {
            if (!$status->show_action_button || !$status->action_icon) {
                continue;
            }

            $action_slug = 'custom_status_' . $status->status_slug;
            $icon_code = $status->action_icon;
            $styles .= ".wc-action-button-{$action_slug}::after { font-family: dashicons !important; content: '\\{$icon_code}' !important; }\n";
        }
        
        $styles .= '</style>';
        echo $styles;
    }

    /**
     * Aggiungi gli stili CSS per le etichette degli status
     */
    public function add_status_styles() {
        $custom_statuses = $this->db->get_all_statuses();
        $styles = '<style>';
        
        foreach ($custom_statuses as $status) {
            $status_key = 'wc-' . $status->status_slug;
            $styles .= ".order-status.status-{$status->status_slug} { 
                background-color: {$status->background_color} !important; 
                color: {$status->text_color} !important; 
            }\n";
            
            // Stile anche per il menu a tendina
            $styles .= "select.order-status-selector option[value='{$status_key}'] { 
                background-color: {$status->background_color} !important; 
                color: {$status->text_color} !important; 
            }\n";
        }
        
        $styles .= '</style>';
        echo $styles;
    }

    /**
     * Gestisce il cambio status manuale (dalla scheda ordine o bulk actions)
     */
    public function handle_manual_status_change($order_id, $new_status) {
        error_log('WCST: Cambio status manuale - Order ID: ' . $order_id . ', Nuovo status: ' . $new_status);
        $order = wc_get_order($order_id);
        if ($order) {
            $this->handle_status_change($order_id, '', $new_status, $order);
        }
    }

    /**
     * Gestisce l'invio delle email al cambio di status
     */
    public function handle_status_change($order_id, $old_status, $new_status, $order) {
        static $processed_orders = [];
        
        // Previeni il doppio invio controllando se l'ordine è già stato processato
        $order_key = $order_id . '_' . $new_status;
        if (in_array($order_key, $processed_orders)) {
            error_log('WCST: Ordine già processato, skip - Order ID: ' . $order_id . ', Status: ' . $new_status);
            return;
        }
        
        // Aggiungi l'ordine alla lista dei processati
        $processed_orders[] = $order_key;
        
        error_log('WCST: Gestione cambio status - Order ID: ' . $order_id . ', Da: ' . $old_status . ', A: ' . $new_status);
        
        // Rimuovi il prefisso wc- dallo status
        $new_status = str_replace('wc-', '', $new_status);
        
        // Ottieni lo status custom dal database
        $custom_statuses = $this->db->get_all_statuses();
        $current_status = null;
        
        foreach ($custom_statuses as $status) {
            if ($status->status_slug === $new_status) {
                $current_status = $status;
                break;
            }
        }
        
        if (!$current_status) {
            error_log('WCST: Status non trovato nel database: ' . $new_status);
            return;
        }
        
        // Controlla se esiste una configurazione email per questo status
        $email_config = $this->db->get_status_email($current_status->id);
        
        if (!$email_config) {
            error_log('WCST: Nessuna configurazione email trovata per lo status: ' . $new_status);
            return;
        }

        if (!$email_config->is_enabled) {
            error_log('WCST: Email disabilitata per lo status: ' . $new_status);
            return;
        }
        
        error_log('WCST: Preparazione invio email per status: ' . $new_status);
        
        // Prepara i dati dell'ordine per il template
        $order_data = [
            'order_id' => $order->get_id(),
            'order_number' => $order->get_order_number(),
            'billing_first_name' => $order->get_billing_first_name(),
            'billing_last_name' => $order->get_billing_last_name(),
            'order_total' => $order->get_total(),
            'order_currency' => $order->get_currency(),
            'order_date' => $order->get_date_created()->format('d/m/Y H:i:s'),
            'order_status' => $current_status->status_name
        ];

        // Determina l'indirizzo email di destinazione
        $to_email = !empty($email_config->email_to) ? $email_config->email_to : $order->get_billing_email();
        
        if (empty($to_email)) {
            error_log('WCST: Nessun indirizzo email valido trovato per l\'invio');
            return;
        }

        error_log('WCST: Email destinazione finale: ' . $to_email);
        
        // Sostituisci i placeholder nel contenuto dell'email
        $email_content = $email_config->email_content;
        foreach ($order_data as $key => $value) {
            $email_content = str_replace('{' . $key . '}', $value, $email_content);
        }

        // Ottieni il mailer di WooCommerce
        $mailer = WC()->mailer();
        
        // Formatta l'email usando il template di WooCommerce
        $formatted_email = $mailer->wrap_message(
            $email_config->email_subject,
            $email_content
        );
        
        // Prepara l'header per l'email HTML
        $headers = [
            'Content-Type: text/html; charset=UTF-8',
            sprintf('From: %s <%s>', get_bloginfo('name'), get_option('admin_email'))
        ];
        
        error_log('WCST: Tentativo invio email a: ' . $to_email);
        
        // Invia l'email
        $sent = wp_mail(
            $to_email,
            $email_config->email_subject,
            $formatted_email,
            $headers
        );

        error_log('WCST: Risultato invio email: ' . ($sent ? 'Successo' : 'Fallito'));
    }
} 