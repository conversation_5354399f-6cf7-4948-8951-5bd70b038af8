/* WooCommerce PC Quote Manager Styles */

/* Frontend Form Styles */
.wc-pc-quote-form-container {
    max-width: 600px;
    margin: 20px auto;
    padding: 30px;
    background: #ffffff;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border: 1px solid #e1e1e1;
}

.wc-pc-quote-form-container h3 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-size: 24px;
    font-weight: 600;
}

.wc-pc-quote-success {
    background: #d4edda;
    color: #155724;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #c3e6cb;
    margin-bottom: 20px;
    text-align: center;
    font-weight: 500;
}

.wc-pc-quote-errors {
    background: #f8d7da;
    color: #721c24;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #f5c6cb;
    margin-bottom: 20px;
}

.wc-pc-quote-errors ul {
    margin: 0;
    padding-left: 20px;
}

.wc-pc-quote-errors li {
    margin-bottom: 5px;
}

.wc-pc-quote-form {
    width: 100%;
}

.wc-pc-quote-field {
    margin-bottom: 25px;
}

.wc-pc-quote-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.wc-pc-quote-field input[type="text"],
.wc-pc-quote-field input[type="email"],
.wc-pc-quote-field select,
.wc-pc-quote-field textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e1e1;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.wc-pc-quote-field input[type="text"]:focus,
.wc-pc-quote-field input[type="email"]:focus,
.wc-pc-quote-field select:focus,
.wc-pc-quote-field textarea:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
}

.wc-pc-quote-field textarea {
    resize: vertical;
    min-height: 100px;
    line-height: 1.5;
}

.wc-pc-quote-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.wc-pc-quote-checkboxes label {
    display: flex;
    align-items: center;
    font-weight: normal;
    margin-bottom: 0;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.wc-pc-quote-checkboxes label:hover {
    background-color: #f8f9fa;
}

.wc-pc-quote-checkboxes input[type="checkbox"] {
    width: auto;
    margin-right: 10px;
    transform: scale(1.1);
}

.wc-pc-quote-submit {
    text-align: center;
    margin-top: 35px;
}

.wc-pc-quote-submit input[type="submit"] {
    background: linear-gradient(135deg, #0073aa 0%, #005a87 100%);
    color: white;
    padding: 15px 40px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.wc-pc-quote-submit input[type="submit"]:hover {
    background: linear-gradient(135deg, #005a87 0%, #004666 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 115, 170, 0.3);
}

/* Admin Styles */
.wc-pc-quote-details {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
}

.wc-pc-quote-details h2 {
    color: #0073aa;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.wc-pc-quote-details .form-table th {
    font-weight: 600;
    color: #333;
    width: 200px;
}

.wc-pc-quote-details .form-table td {
    padding: 10px 0;
}

/* Status Styles */
.status-in-attesa-di-risposta {
    background: #fff3cd;
    color: #856404;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-inviato {
    background: #d4edda;
    color: #155724;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-risposta-cliente {
    background: #fff3cd;
    color: #856404;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-chiuso {
    background: #f8d7da;
    color: #721c24;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

/* Admin Table Styles */
.wp-list-table.wc-pc-quotes-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.wp-list-table.wc-pc-quotes-table th {
    background: #f8f9fa;
    font-weight: 600;
}

.wp-list-table.wc-pc-quotes-table td {
    vertical-align: middle;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wc-pc-quote-form-container {
        margin: 10px;
        padding: 20px;
    }
    
    .wc-pc-quote-checkboxes {
        grid-template-columns: 1fr;
    }
    
    .wc-pc-quote-field input[type="text"],
    .wc-pc-quote-field input[type="email"],
    .wc-pc-quote-field select,
    .wc-pc-quote-field textarea {
        padding: 10px 12px;
    }
    
    .wc-pc-quote-submit input[type="submit"] {
        padding: 12px 30px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .wc-pc-quote-form-container {
        padding: 15px;
    }
    
    .wc-pc-quote-form-container h3 {
        font-size: 20px;
    }
    
    .wc-pc-quote-checkboxes {
        gap: 10px;
    }
    
    .wc-pc-quote-checkboxes label {
        padding: 6px;
    }
}

/* Admin Form Styles */
.wc-pc-quote-admin-form {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
}

.wc-pc-quote-admin-form textarea {
    width: 100%;
    min-height: 150px;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
}

.wc-pc-quote-admin-form textarea:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
}

/* Button Styles */
.button.wc-pc-quote-button {
    background: #0073aa;
    border-color: #0073aa;
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.button.wc-pc-quote-button:hover {
    background: #005a87;
    border-color: #005a87;
    color: white;
}

/* Loading States */
.wc-pc-quote-loading {
    opacity: 0.6;
    pointer-events: none;
}

.wc-pc-quote-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: wc-pc-quote-spin 1s linear infinite;
}

@keyframes wc-pc-quote-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Conversation Styles */
.wc-pc-quote-conversation {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.wc-pc-quote-conversation h3 {
    margin-top: 0;
    color: #0073aa;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

.conversation-message {
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 8px;
    position: relative;
}

.conversation-message.admin-message {
    background: #e3f2fd;
    border-left: 4px solid #0073aa;
    margin-right: 50px;
}

.conversation-message.customer-message {
    background: #f1f8e9;
    border-left: 4px solid #4caf50;
    margin-left: 50px;
}

.message-header {
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.message-sender {
    color: #333;
}

.message-time {
    font-size: 12px;
    color: #666;
    font-weight: normal;
}

.message-content {
    line-height: 1.6;
    color: #333;
}

/* Close Quote Button */
.wc-pc-quote-close-button {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
    margin-left: 10px;
}

.wc-pc-quote-close-button:hover {
    background: #c82333;
    border-color: #bd2130;
    color: white;
}

/* Responsive conversation styles */
@media (max-width: 768px) {
    .conversation-message.admin-message {
        margin-right: 20px;
    }

    .conversation-message.customer-message {
        margin-left: 20px;
    }

    .message-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .message-time {
        margin-top: 5px;
    }
}
