# WooCommerce PC Quote Manager - Troubleshooting Guide

## 🚨 Form Submission Error Fix (v2.1.0)

This guide addresses the "Errore durante il salvataggio. Riprova più tardi." error and provides comprehensive debugging tools.

### ✅ **What Was Fixed**

#### **1. Enhanced Error Handling**
- **Before**: Generic error message with no details
- **After**: Specific error messages based on the actual problem
- **Improvement**: Users now see meaningful feedback about what went wrong

#### **2. Database Verification**
- **Added**: Automatic table existence check before form submission
- **Added**: Column structure validation
- **Added**: Database connection testing
- **Improvement**: Prevents errors from missing or corrupted database tables

#### **3. Improved Form Validation**
- **Added**: Field length validation (prevents "Data too long" errors)
- **Added**: Better array handling for checkboxes
- **Added**: Exception handling for sanitization
- **Improvement**: Catches validation issues before database operations

#### **4. Comprehensive Logging**
- **Added**: Debug logging system with detailed information
- **Added**: Custom log file in `/wp-content/uploads/wc-pc-quote-debug.log`
- **Added**: User IP and timestamp tracking
- **Improvement**: Administrators can now see exactly what's happening

#### **5. Diagnostics Dashboard**
- **Added**: Admin diagnostics page at WooCommerce → Diagnostics PC Quote
- **Added**: Database testing tools
- **Added**: System information display
- **Added**: Real-time error analysis
- **Improvement**: Easy troubleshooting without technical knowledge

---

## 🔍 **Root Cause Analysis**

### **Common Causes of Form Submission Errors**

1. **Database Table Missing**
   - Plugin not properly activated
   - Database creation failed during activation
   - Manual database modifications

2. **Database Permission Issues**
   - WordPress user lacks INSERT permissions
   - Database connection problems
   - Server resource limitations

3. **Data Validation Failures**
   - Fields exceeding maximum length
   - Invalid characters in form data
   - Missing required fields

4. **Server Configuration Issues**
   - PHP memory limits
   - Execution time limits
   - Session handling problems

---

## 🛠️ **How to Diagnose Issues**

### **Step 1: Use the Diagnostics Dashboard**

1. Go to **WooCommerce → Diagnostics PC Quote**
2. Click **"Testa Database"** to run automatic tests
3. Review the results:
   - ✅ Green checkmarks = OK
   - ❌ Red X marks = Problems found
   - ⚠️ Orange warnings = Minor issues

### **Step 2: Check Debug Logs**

1. Enable WordPress debugging by adding to `wp-config.php`:
   ```php
   define('WP_DEBUG', true);
   define('WP_DEBUG_LOG', true);
   ```

2. Check the debug log in the diagnostics dashboard
3. Look for entries starting with `WC_PC_Quote_Debug:`

### **Step 3: Test Form Submission**

1. Fill out the quote form with test data
2. Submit the form
3. If errors occur, check:
   - Error message displayed to user
   - Debug log entries
   - Database diagnostics results

---

## 🔧 **Specific Error Solutions**

### **Error: "Errore di configurazione database"**
**Cause**: Database table doesn't exist or is corrupted
**Solution**:
1. Deactivate and reactivate the plugin
2. Check diagnostics dashboard
3. If table still missing, manually run activation function

### **Error: "Uno dei campi contiene troppo testo"**
**Cause**: Form field exceeds database column limits
**Solution**:
1. Check which field is too long (see debug log)
2. Reduce text length in that field
3. Consider increasing database column size if needed

### **Error: "Alcuni campi obbligatori sono mancanti"**
**Cause**: Required fields not properly submitted
**Solution**:
1. Check form HTML for missing `name` attributes
2. Verify JavaScript isn't interfering
3. Test with different browsers

### **Error: "Sembra che tu abbia già inviato questo preventivo"**
**Cause**: Duplicate entry in database
**Solution**:
1. Check if quote was actually saved
2. Clear browser cache and cookies
3. Try with different email address

---

## 🧪 **Testing Procedures**

### **Test 1: Database Connectivity**
```sql
-- Run in phpMyAdmin or database tool
SELECT COUNT(*) FROM wp_wc_pc_quotes;
```
**Expected**: Number of existing quotes (0 or more)
**If fails**: Database connection or table issues

### **Test 2: Form Validation**
1. Submit form with empty required fields
2. Submit form with very long text (>1000 chars)
3. Submit form with invalid email format
**Expected**: Specific error messages for each case

### **Test 3: Successful Submission**
1. Fill all required fields correctly
2. Submit form
3. Check for success message
4. Verify quote appears in admin panel
**Expected**: Success message and quote saved

### **Test 4: Email Notifications**
1. Submit valid quote
2. Check admin email for notification
3. Verify email contains quote details
**Expected**: Email received with correct information

---

## 📊 **Monitoring and Maintenance**

### **Regular Checks**
- Monitor debug log file size (rotate if >10MB)
- Check diagnostics dashboard monthly
- Verify database table integrity
- Test form submission after WordPress/plugin updates

### **Performance Optimization**
- Clear debug logs regularly
- Monitor database query performance
- Check server resource usage during peak times

### **Security Considerations**
- Debug logs contain user data - protect access
- Regularly update plugin to latest version
- Monitor for suspicious form submissions

---

## 🆘 **Emergency Recovery**

### **If Plugin Completely Broken**
1. Deactivate plugin via WordPress admin
2. Check error logs in cPanel/hosting panel
3. Reactivate plugin to trigger database recreation
4. Run diagnostics to verify functionality

### **If Database Corrupted**
1. Backup existing data
2. Drop and recreate tables using activation function
3. Restore data from backup if needed
4. Test all functionality

### **If Form Still Not Working**
1. Switch to default WordPress theme temporarily
2. Deactivate other plugins to test conflicts
3. Check server error logs
4. Contact hosting provider if server issues suspected

---

## 📞 **Getting Help**

### **Information to Provide When Seeking Support**
1. WordPress version
2. WooCommerce version
3. Plugin version
4. PHP version
5. Error messages (exact text)
6. Debug log entries
7. Diagnostics dashboard results
8. Steps to reproduce the issue

### **Useful Commands for Developers**
```bash
# Check PHP error log
tail -f /path/to/php/error.log

# Check WordPress debug log
tail -f /wp-content/debug.log

# Check plugin debug log
tail -f /wp-content/uploads/wc-pc-quote-debug.log
```

---

## 🎯 **Prevention Tips**

1. **Regular Backups**: Always backup before updates
2. **Staging Environment**: Test changes on staging first
3. **Monitor Logs**: Check logs regularly for early warning signs
4. **Update Regularly**: Keep WordPress, WooCommerce, and plugin updated
5. **Resource Monitoring**: Ensure adequate server resources
6. **User Training**: Train users on proper form completion

---

This troubleshooting guide should resolve most form submission issues. The enhanced error handling and diagnostics tools provide clear visibility into any problems that occur.
