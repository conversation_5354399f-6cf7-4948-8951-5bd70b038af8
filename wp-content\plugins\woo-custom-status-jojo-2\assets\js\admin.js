jQuery(document).ready(function($) {
    // Inizializza i colorpicker
    $('.wcst-color-picker').wpColorPicker();

    // Gestione generazione automatica slug
    var statusNameInput = $('#status_name');
    var statusSlugInput = $('#status_slug');
    var slugTimeout;

    statusNameInput.on('input', function() {
        clearTimeout(slugTimeout);
        var statusName = $(this).val();

        // Se il campo è vuoto, pulisci lo slug
        if (!statusName) {
            statusSlugInput.val('');
            return;
        }

        // Attendi che l'utente finisca di scrivere
        slugTimeout = setTimeout(function() {
            $.ajax({
                url: wcst_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wcst_generate_slug',
                    status_name: statusName,
                    nonce: wcst_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        statusSlugInput.val(response.data.slug);
                    }
                }
            });
        }, 500);
    });

    // Anteprima etichetta status in tempo reale
    var previewLabel = $('<span>', {
        'class': 'order-status-preview',
        text: statusNameInput.val() || 'Anteprima Status',
        css: {
            display: 'inline-block',
            padding: '3px 8px',
            borderRadius: '3px',
            fontSize: '12px',
            lineHeight: '1.4',
            marginTop: '10px'
        }
    }).insertAfter('#text_color');

    function updatePreview() {
        var backgroundColor = $('#background_color').val() || '#e5e5e5';
        var textColor = $('#text_color').val() || '#000000';
        var statusName = statusNameInput.val() || 'Anteprima Status';

        previewLabel.css({
            backgroundColor: backgroundColor,
            color: textColor
        }).text(statusName);
    }

    $('#background_color, #text_color').wpColorPicker({
        change: updatePreview
    });

    statusNameInput.on('input', updatePreview);
    updatePreview();
}); 