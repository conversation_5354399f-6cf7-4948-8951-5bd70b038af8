<?php
/**
 * Uninstall script per WooCommerce Email Action Buttons
 * 
 * Questo file viene eseguito quando il plugin viene eliminato tramite l'admin di WordPress.
 * Rimuove tutti i dati del plugin dal database.
 */

// Se uninstall non è chiamato da WordPress, esci
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Verifica che l'utente abbia i permessi necessari
if (!current_user_can('activate_plugins')) {
    return;
}

// Carica la classe del database
require_once plugin_dir_path(__FILE__) . 'includes/class-email-action-buttons-db.php';

/**
 * Rimuove tutte le tabelle del plugin
 */
function weab_uninstall_remove_tables() {
    $db = new Email_Action_Buttons_DB();
    $db->drop_tables();
}

/**
 * Rimuove tutte le opzioni del plugin
 */
function weab_uninstall_remove_options() {
    // Rimuovi eventuali opzioni del plugin (se ne aggiungiamo in futuro)
    delete_option('weab_plugin_version');
    delete_option('weab_db_version');
    
    // Rimuovi opzioni transient
    delete_transient('weab_admin_notices');
    
    // Pulisci la cache degli oggetti
    wp_cache_flush();
}

/**
 * Rimuove i file di log del plugin (se esistenti)
 */
function weab_uninstall_remove_logs() {
    $upload_dir = wp_upload_dir();
    $log_dir = $upload_dir['basedir'] . '/woo-email-action-buttons-logs/';
    
    if (is_dir($log_dir)) {
        $files = glob($log_dir . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        rmdir($log_dir);
    }
}

/**
 * Pulisce i metadati degli ordini relativi al plugin
 */
function weab_uninstall_clean_order_meta() {
    global $wpdb;
    
    // Rimuovi eventuali meta degli ordini creati dal plugin
    $wpdb->delete(
        $wpdb->postmeta,
        array(
            'meta_key' => '_weab_email_sent'
        )
    );
    
    $wpdb->delete(
        $wpdb->postmeta,
        array(
            'meta_key' => '_weab_last_email_button'
        )
    );
}

/**
 * Rimuove i ruoli e capacità personalizzati (se aggiunti)
 */
function weab_uninstall_remove_capabilities() {
    // Al momento non abbiamo capacità personalizzate, ma questa funzione
    // è pronta per future implementazioni
    
    $roles = array('administrator', 'shop_manager');
    
    foreach ($roles as $role_name) {
        $role = get_role($role_name);
        if ($role) {
            // Rimuovi eventuali capacità personalizzate
            $role->remove_cap('manage_email_action_buttons');
        }
    }
}

/**
 * Pulisce le email in coda (se utilizzassimo una coda personalizzata)
 */
function weab_uninstall_clear_email_queue() {
    // Rimuovi eventuali email in coda del plugin
    wp_clear_scheduled_hook('weab_send_queued_emails');
    
    // Rimuovi eventuali cron job personalizzati
    wp_clear_scheduled_hook('weab_cleanup_old_logs');
}

/**
 * Funzione principale di disinstallazione
 */
function weab_uninstall() {
    // Log dell'inizio della disinstallazione
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('[WooCommerce Email Action Buttons] Inizio disinstallazione plugin');
    }
    
    try {
        // Rimuovi le tabelle del database
        weab_uninstall_remove_tables();
        
        // Rimuovi le opzioni
        weab_uninstall_remove_options();
        
        // Rimuovi i file di log
        weab_uninstall_remove_logs();
        
        // Pulisci i metadati degli ordini
        weab_uninstall_clean_order_meta();
        
        // Rimuovi capacità personalizzate
        weab_uninstall_remove_capabilities();
        
        // Pulisci la coda email
        weab_uninstall_clear_email_queue();
        
        // Flush delle regole di rewrite (nel caso fossero state modificate)
        flush_rewrite_rules();
        
        // Log del completamento
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('[WooCommerce Email Action Buttons] Disinstallazione completata con successo');
        }
        
    } catch (Exception $e) {
        // Log dell'errore
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('[WooCommerce Email Action Buttons] Errore durante la disinstallazione: ' . $e->getMessage());
        }
    }
}

// Esegui la disinstallazione
weab_uninstall();
