/* WooCommerce Order Status Buttons - Admin Styles */

/* Form Container */
.wosb-form-container {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin-top: 20px;
}

.wosb-form-container h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

/* Form Table Styling */
.wosb-button-form .form-table {
    margin-top: 20px;
}

.wosb-button-form .form-table th {
    width: 200px;
    vertical-align: top;
    padding-top: 15px;
    font-weight: 600;
}

.wosb-button-form .form-table td {
    padding-top: 10px;
    padding-bottom: 15px;
}

/* Fieldset for checkboxes */
.wosb-button-form fieldset {
    border: 1px solid #ddd;
    padding: 15px;
    border-radius: 4px;
    background: #f9f9f9;
}

.wosb-button-form fieldset label {
    display: block;
    margin-bottom: 8px;
    font-weight: normal;
}

.wosb-button-form fieldset input[type="checkbox"] {
    margin-right: 8px;
}

/* Icon Preview */
.wosb-icon-preview {
    margin-left: 10px;
    font-size: 20px;
    color: #0073aa;
    vertical-align: middle;
}

/* Buttons List Table */
.wosb-buttons-list {
    margin-top: 20px;
}

.wosb-buttons-list .wp-list-table {
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

/* Status List in Table */
.wosb-status-list {
    font-size: 12px;
    line-height: 1.4;
    color: #666;
}

/* Target Status Badge */
.wosb-target-status {
    display: inline-block;
    padding: 3px 8px;
    background: #0073aa;
    color: white;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

/* Preview Icon in Table */
.wosb-preview-icon {
    margin-right: 8px;
    color: #0073aa;
    font-size: 16px;
}

/* Column Widths */
.column-name {
    width: 20%;
}

.column-description {
    width: 25%;
}

.column-order-statuses {
    width: 20%;
}

.column-target-status {
    width: 15%;
}

.column-icon {
    width: 10%;
}

.column-created {
    width: 10%;
}

.column-actions {
    width: 15%;
}

/* Order Actions in WooCommerce Orders List */
.wosb-order-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;
}

.wosb-action-button {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    background: #0073aa;
    color: white !important;
    text-decoration: none !important;
    border-radius: 3px;
    font-size: 12px;
    line-height: 1;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.wosb-action-button:hover {
    background: #005a87;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.wosb-action-button:focus {
    outline: 2px solid #005a87;
    outline-offset: 2px;
}

.wosb-action-button .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    margin-right: 4px;
    line-height: 1;
}

.wosb-button-text {
    white-space: nowrap;
    font-weight: 500;
}

/* Inline Buttons for Actions Column */
.wosb-inline-actions {
    display: inline-block;
    margin-left: 8px;
}

.wosb-inline-button {
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    background: #0073aa;
    color: white !important;
    text-decoration: none !important;
    border-radius: 3px;
    font-size: 11px;
    line-height: 1.2;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    margin-left: 4px;
    vertical-align: middle;
}

.wosb-inline-button:hover {
    background: #005a87;
    color: white !important;
}

.wosb-inline-button:focus {
    outline: 1px solid #005a87;
    outline-offset: 1px;
}

.wosb-inline-button .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
    margin-right: 3px;
    line-height: 1;
}

.wosb-inline-button .wosb-button-text {
    font-size: 11px;
    font-weight: 500;
}

/* Placeholder Help */
.wosb-placeholder-help {
    background: #f9f9f9;
    border: 1px solid #ddd;
    padding: 15px;
    margin-top: 10px;
    border-radius: 4px;
    font-size: 13px;
}

.wosb-placeholder-help strong {
    display: block;
    margin-bottom: 8px;
    color: #333;
}

.wosb-placeholder-help code {
    background: #fff;
    padding: 2px 6px;
    border-radius: 2px;
    margin-right: 8px;
    font-family: Consolas, Monaco, monospace;
    font-size: 12px;
    border: 1px solid #ddd;
}

/* Success/Error Messages */
.wosb-message {
    padding: 12px;
    margin: 15px 0;
    border-radius: 4px;
    border-left: 4px solid;
}

.wosb-message.success {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.wosb-message.error {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

/* Loading States */
.wosb-loading {
    opacity: 0.6;
    pointer-events: none;
}

.wosb-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: wosb-spin 1s linear infinite;
}

@keyframes wosb-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .wosb-order-actions {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .wosb-action-button {
        width: 100%;
        justify-content: center;
        margin-bottom: 2px;
    }
}

@media (max-width: 782px) {
    /* Hide less important columns on mobile */
    .column-description,
    .column-order-statuses,
    .column-icon,
    .column-created {
        display: none;
    }
    
    .column-name {
        width: 50%;
    }
    
    .column-target-status {
        width: 25%;
    }
    
    .column-actions {
        width: 25%;
    }
    
    /* Mobile button styling */
    .wosb-action-button .wosb-button-text {
        display: none;
    }
    
    .wosb-action-button .dashicons {
        margin-right: 0;
    }
    
    .wosb-action-button {
        padding: 6px;
        min-width: 28px;
        justify-content: center;
    }
    
    /* Form adjustments */
    .wosb-form-container {
        padding: 15px;
    }
    
    .wosb-button-form .form-table th {
        width: auto;
        display: block;
        padding-bottom: 5px;
    }
    
    .wosb-button-form .form-table td {
        display: block;
        padding-left: 0;
    }
}

/* Print Styles */
@media print {
    .wosb-action-button,
    .column-actions {
        display: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .wosb-action-button {
        border: 2px solid #000;
    }
    
    .wosb-target-status {
        border: 1px solid #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .wosb-action-button {
        transition: none;
    }
    
    .wosb-action-button:hover {
        transform: none;
    }
    
    @keyframes wosb-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(0deg); }
    }
}
