# Woo Custom Status Jojo

Plugin WordPress per la creazione e gestione di stati ordine personalizzati in WooCommerce.

## Descrizione

Questo plugin permette di creare e gestire stati ordine personalizzati per WooCommerce, con la possibilità di:

- Creare stati ordine con nome e slug personalizzati
- Personalizzare il colore dello sfondo e del testo dell'etichetta dello stato
- Aggiungere pulsanti di azione rapida nella lista ordini
- Configurare email automatiche da inviare al cliente quando un ordine passa a uno stato specifico

## Requisiti

- WordPress 5.8 o superiore
- WooCommerce 6.0 o superiore
- PHP 8.0 o superiore

## Installazione

1. Scarica il plugin in formato zip
2. Vai nel pannello di amministrazione di WordPress
3. Vai in Plugin > Aggiungi nuovo > Carica plugin
4. Seleziona il file zip del plugin e clicca su "Installa ora"
5. Attiva il plugin

## Utilizzo

### Creazione di un nuovo stato ordine

1. Vai in WooCommerce > Status Ordini
2. Clicca su "Aggiungi Nuovo Status"
3. Compila il form con:
   - Nome dello status
   - Lo slug verrà generato automaticamente
   - Scegli i colori per l'etichetta
   - Opzionalmente, abilita il pulsante di azione rapida e scegli un'icona

### Configurazione email

1. Vai in WooCommerce > Status Ordini > Email Status
2. Seleziona lo status per cui vuoi configurare l'email
3. Abilita l'invio dell'email
4. Configura:
   - Indirizzo email (lascia vuoto per usare l'email del cliente)
   - Oggetto dell'email
   - Contenuto dell'email

### Variabili disponibili per le email

Puoi utilizzare le seguenti variabili nel contenuto dell'email:

- `{order_number}` - Numero dell'ordine
- `{customer_name}` - Nome del cliente
- `{order_date}` - Data dell'ordine
- `{order_total}` - Totale dell'ordine
- `{billing_address}` - Indirizzo di fatturazione
- `{shipping_address}` - Indirizzo di spedizione
- `{site_title}` - Nome del sito
- `{site_url}` - URL del sito

## Supporto

Per segnalare bug o richiedere nuove funzionalità, apri una issue su GitHub.

## Licenza

GPL v2 o successiva 