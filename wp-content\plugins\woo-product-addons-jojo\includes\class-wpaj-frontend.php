<?php
/**
 * Classe per la gestione della parte frontend del plugin
 *
 * @package Woo_Product_Addons_Jojo
 * @since 1.0.0
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe WPAJ_Frontend
 */
class WPAJ_Frontend {

    /**
     * Istanza della classe dei gruppi
     *
     * @var WPAJ_Groups
     */
    private $groups;

    /**
     * Costruttore
     *
     * @param WPAJ_Groups $groups Istanza della classe dei gruppi
     */
    public function __construct($groups = null) {
        // Inizializza l'istanza dei gruppi
        $this->groups = $groups ?: new WPAJ_Groups();
        
        // Aggiungi gli accessori nella pagina del prodotto dopo la short description
        add_action('woocommerce_before_add_to_cart_form', array($this, 'display_product_accessories'), 15);
        
        // Carica gli script e gli stili per il frontend
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        
        // Gestisci l'aggiunta degli accessori al carrello
        add_action('wp_ajax_wpaj_add_accessories_to_cart', array($this, 'ajax_add_accessories_to_cart'));
        add_action('wp_ajax_nopriv_wpaj_add_accessories_to_cart', array($this, 'ajax_add_accessories_to_cart'));
        
        // Modifica il comportamento del pulsante "Aggiungi al carrello"
        add_filter('woocommerce_add_to_cart_form_action', array($this, 'modify_add_to_cart_form_action'));
        
        // Aggiungi filtri per la gestione degli ordini
        add_filter('woocommerce_checkout_create_order_line_item', array($this, 'add_accessory_data_to_order_items'), 10, 4);
        
        // Compatibilità con modalità di archiviazione ad alte prestazioni (HPOS)
        add_action('before_woocommerce_init', array($this, 'declare_hpos_compatibility'));
    }

    /**
     * Carica gli script e gli stili per il frontend
     */
    public function enqueue_frontend_scripts() {
        // Carica gli script solo nella pagina del prodotto
        if (!is_product()) {
            return;
        }
        
        // Registra e carica lo stile CSS
        wp_register_style(
            'wpaj-frontend-style',
            WPAJ_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            WPAJ_PLUGIN_VERSION
        );
        wp_enqueue_style('wpaj-frontend-style');
        
        // Registra e carica lo script JS
        wp_register_script(
            'wpaj-frontend-script',
            WPAJ_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            WPAJ_PLUGIN_VERSION,
            true
        );
        
        // Passa i dati allo script
        wp_localize_script(
            'wpaj-frontend-script',
            'wpaj_frontend_params',
            array(
                'ajax_url'   => admin_url('admin-ajax.php'),
                'nonce'      => wp_create_nonce('wpaj_add_to_cart_nonce'),
                'redirect_to_cart' => $this->groups->is_redirect_to_cart_enabled(),
                'i18n'       => array(
                    'error'   => __('Si è verificato un errore. Riprova.', 'woo-product-addons-jojo'),
                    'success' => __('Prodotti aggiunti al carrello!', 'woo-product-addons-jojo'),
                ),
            )
        );
        
        wp_enqueue_script('wpaj-frontend-script');
    }

    /**
     * Mostra gli accessori del prodotto nella pagina del prodotto
     */
    public function display_product_accessories() {
        global $product;
        
        if (!$product) {
            return;
        }
        
        // Ottieni gli accessori specifici del prodotto (quelli selezionati direttamente)
        $product_accessories_ids = get_post_meta($product->get_id(), '_product_accessories', true);
        $product_accessories_ids = !empty($product_accessories_ids) ? explode(',', $product_accessories_ids) : array();
        
        // Ottieni gli accessori basati sulle categorie, già raggruppati
        $accessories_by_group = $this->get_category_accessories_for_product($product->get_id());
        
        $has_accessories = !empty($product_accessories_ids) || !empty($accessories_by_group);
        
        if (!$has_accessories) {
            return;
        }
        
        // Inizia il contenitore degli accessori
        echo '<div class="product-accessories">';
        echo '<h2>' . __('Accessori consigliati', 'woo-product-addons-jojo') . '</h2>';
        
        // Mostra prima gli accessori specifici del prodotto, se presenti
        if (!empty($product_accessories_ids)) {
            echo '<div class="accessories-group">';
            echo '<h3>' . __('Accessori per questo prodotto', 'woo-product-addons-jojo') . '</h3>';
            echo '<div class="accessories-list">';
            
            foreach ($product_accessories_ids as $product_id) {
                $accessory = wc_get_product($product_id);
                
                if (!$accessory || !$accessory->is_purchasable() || !$accessory->is_in_stock()) {
                    continue;
                }
                
                $this->render_accessory_product($accessory);
            }
            
            echo '</div>'; // .accessories-list
            echo '</div>'; // .accessories-group
        }
        
        // Mostra gli accessori raggruppati
        if (!empty($accessories_by_group)) {
            foreach ($accessories_by_group as $group_id => $group) {
                echo '<div class="accessories-group" data-group-id="' . esc_attr($group_id) . '">';
                echo '<h3>' . esc_html($group['name']) . '</h3>';
                
                if (!empty($group['description'])) {
                    echo '<div class="group-description">' . esc_html($group['description']) . '</div>';
                }
                
                echo '<div class="accessories-list">';
                
                foreach ($group['accessories'] as $product_id) {
                    $accessory = wc_get_product($product_id);
                    
                    if (!$accessory || !$accessory->is_purchasable() || !$accessory->is_in_stock()) {
                        continue;
                    }
                    
                    $this->render_accessory_product($accessory);
                }
                
                echo '</div>'; // .accessories-list
                echo '</div>'; // .accessories-group
            }
        }
        
        echo '</div>'; // .product-accessories
    }

    /**
     * Ottiene gli accessori basati sulle categorie per un prodotto
     *
     * @param int $product_id ID del prodotto
     * @return array Array di accessori organizzati per gruppo
     */
    private function get_category_accessories_for_product($product_id) {
        return $this->groups->get_accessories_for_product($product_id);
    }

    /**
     * Renderizza un prodotto accessorio
     *
     * @param WC_Product $accessory Prodotto accessorio da renderizzare
     */
    private function render_accessory_product($accessory) {
        $product_id = $accessory->get_id();
        $product_name = $accessory->get_name();
        $product_price = wc_price($accessory->get_price());
        $product_image = $accessory->get_image(array(100, 100));
        
        echo '<div class="accessory-product" data-product-id="' . esc_attr($product_id) . '">';
        echo '<input type="checkbox" name="accessory_products[]" value="' . esc_attr($product_id) . '" id="accessory_' . esc_attr($product_id) . '" class="accessory-checkbox-input visuallyhidden">';
        
        echo '<div class="accessory-image">';
        echo $product_image;
        echo '</div>';
        
        echo '<div class="accessory-info">';
        echo '<h3 class="accessory-name">' . esc_html($product_name) . '</h3>';
        echo '<div class="accessory-price">' . $product_price . '</div>';
        echo '</div>';
        
        echo '<div class="accessory-indicator"></div>';
        
        echo '</div>';
    }

    /**
     * Modifica l'azione del form di aggiunta al carrello
     *
     * @param string $action URL dell'azione del form
     * @return string URL modificato
     */
    public function modify_add_to_cart_form_action($action) {
        global $product;
        
        if (!$product) {
            return $action;
        }
        
        // Ottieni gli accessori specifici del prodotto
        $product_accessories_ids = get_post_meta($product->get_id(), '_product_accessories', true);
        
        // Ottieni gli accessori basati sulle categorie
        $accessories_by_group = $this->get_category_accessories_for_product($product->get_id());
        
        // Verifica se ci sono accessori
        if (empty($product_accessories_ids) && empty($accessories_by_group)) {
            return $action;
        }
        
        // Imposta l'azione a javascript:void(0) per gestire l'aggiunta al carrello via AJAX
        return 'javascript:void(0)';
    }

    /**
     * Gestisce l'aggiunta degli accessori al carrello via AJAX
     */
    public function ajax_add_accessories_to_cart() {
        // Verifica il nonce
        check_ajax_referer('wpaj_add_to_cart_nonce', 'nonce');
        
        // Ottieni i dati dalla richiesta
        $product_id = isset($_POST['product_id']) ? absint($_POST['product_id']) : 0;
        $quantity = isset($_POST['quantity']) ? absint($_POST['quantity']) : 1;
        $accessories = isset($_POST['accessories']) ? (array) $_POST['accessories'] : array();
        
        // Converti gli ID degli accessori in interi
        $accessories = array_map('absint', $accessories);
        
        // Prepara la risposta
        $response = array(
            'success' => false,
            'message' => __('Si è verificato un errore. Riprova.', 'woo-product-addons-jojo'),
        );
        
        // Verifica che il prodotto principale sia valido
        if (!$product_id) {
            $response['message'] = __('Prodotto non valido.', 'woo-product-addons-jojo');
            wp_send_json($response);
            return;
        }
        
        // Verifica che il prodotto esista
        $product = wc_get_product($product_id);
        if (!$product) {
            $response['message'] = __('Prodotto non trovato.', 'woo-product-addons-jojo');
            wp_send_json($response);
            return;
        }
        
        // Verifica che il carrello WooCommerce sia disponibile
        if (!function_exists('WC') || !WC()->cart) {
            $response['message'] = __('Il carrello WooCommerce non è disponibile.', 'woo-product-addons-jojo');
            wp_send_json($response);
            return;
        }
        
        try {
            // Aggiungi il prodotto principale al carrello
            $cart_item_data = array(
                'wpaj_main_product' => 'yes', // Segna come prodotto principale
            );
            
            $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity, 0, array(), $cart_item_data);
            
            if (!$cart_item_key) {
                throw new Exception(__('Impossibile aggiungere il prodotto al carrello.', 'woo-product-addons-jojo'));
            }
            
            // Aggiungi gli accessori al carrello
            $accessories_added = true;
            $accessories_data = array();
            
            foreach ($accessories as $accessory_id) {
                if (!$accessory_id) {
                    continue;
                }
                
                // Verifica che l'accessorio esista
                $accessory = wc_get_product($accessory_id);
                if (!$accessory) {
                    continue;
                }
                
                // Dati aggiuntivi per associare l'accessorio al prodotto principale
                $accessory_cart_data = array(
                    'wpaj_accessory_for' => $cart_item_key,
                    'wpaj_accessory' => 'yes'
                );
                
                // Aggiungi l'accessorio al carrello
                $added = WC()->cart->add_to_cart($accessory_id, 1, 0, array(), $accessory_cart_data);
                
                if (!$added) {
                    $accessories_added = false;
                } else {
                    $accessories_data[] = array(
                        'id' => $accessory_id,
                        'key' => $added
                    );
                }
            }
            
            // Prepara la risposta di successo
            $response = array(
                'success' => true,
                'message' => $accessories_added 
                    ? __('Prodotti aggiunti al carrello!', 'woo-product-addons-jojo') 
                    : __('Prodotto principale aggiunto al carrello, ma alcuni accessori non sono stati aggiunti.', 'woo-product-addons-jojo'),
                'redirect' => $this->groups->is_redirect_to_cart_enabled() ? wc_get_cart_url() : '',
                'cart_item_key' => $cart_item_key,
                'accessories' => $accessories_data
            );
            
        } catch (Exception $e) {
            $response['message'] = $e->getMessage();
        }
        
        // Invia la risposta
        wp_send_json($response);
    }

    /**
     * Dichiara la compatibilità con la modalità di archiviazione degli ordini ad alte prestazioni (HPOS)
     */
    public function declare_hpos_compatibility() {
        if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
            // Dichiara la compatibilità con Custom Order Tables (COT)
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility(
                'custom_order_tables',
                plugin_basename(WPAJ_PLUGIN_DIR . 'woo-product-addons-jojo.php'),
                true
            );
            
            // Dichiara la compatibilità con il Carting API
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility(
                'cart_checkout_blocks',
                plugin_basename(WPAJ_PLUGIN_DIR . 'woo-product-addons-jojo.php'),
                true
            );
        }
    }
    
    /**
     * Aggiunge i dati degli accessori agli elementi dell'ordine
     *
     * @param WC_Order_Item_Product $item Elemento dell'ordine
     * @param string $cart_item_key Chiave dell'elemento nel carrello
     * @param array $values Valori dell'elemento nel carrello
     * @param WC_Order $order Oggetto ordine
     */
    public function add_accessory_data_to_order_items($item, $cart_item_key, $values, $order) {
        // Aggiungi metadati che indicano se questo è un prodotto principale o un accessorio
        if (isset($values['wpaj_main_product']) && $values['wpaj_main_product'] === 'yes') {
            $item->add_meta_data('_wpaj_main_product', 'yes', true);
        }
        
        if (isset($values['wpaj_accessory']) && $values['wpaj_accessory'] === 'yes') {
            $item->add_meta_data('_wpaj_accessory', 'yes', true);
            
            // Se c'è un riferimento al prodotto principale, salvalo
            if (isset($values['wpaj_accessory_for'])) {
                $item->add_meta_data('_wpaj_accessory_for', $values['wpaj_accessory_for'], true);
            }
        }
        
        return $item;
    }
} 