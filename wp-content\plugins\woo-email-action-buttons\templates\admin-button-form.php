<?php
if (!defined('ABSPATH')) {
    exit;
}

$is_edit = $button->id > 0;
$page_title = $is_edit ? 'Modifica Pulsante Email' : 'Aggiungi Pulsante Email';
$submit_text = $is_edit ? 'Aggiorna Pulsante' : 'Crea Pulsante';
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php echo esc_html($page_title); ?></h1>
    
    <a href="<?php echo esc_url(admin_url('admin.php?page=woo-email-action-buttons')); ?>" class="page-title-action">
        ← Torna alla Lista
    </a>
    
    <hr class="wp-header-end">
    
    <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>" id="weab-button-form">
        <?php wp_nonce_field('weab_save_button', 'weab_nonce'); ?>
        <input type="hidden" name="action" value="weab_save_button">
        <?php if ($is_edit): ?>
            <input type="hidden" name="button_id" value="<?php echo esc_attr($button->id); ?>">
        <?php endif; ?>
        
        <table class="form-table" role="presentation">
            <tbody>
                <!-- Nome Pulsante -->
                <tr>
                    <th scope="row">
                        <label for="button_name">Nome Pulsante <span class="required">*</span></label>
                    </th>
                    <td>
                        <input type="text"
                               id="button_name"
                               name="button_name"
                               value="<?php echo esc_attr($button->button_name); ?>"
                               class="regular-text"
                               required>
                        <p class="description">
                            Il nome che apparirà sul pulsante nella lista ordini.
                        </p>
                    </td>
                </tr>
                
                <!-- Descrizione -->
                <tr>
                    <th scope="row">
                        <label for="button_description">Descrizione</label>
                    </th>
                    <td>
                        <textarea id="button_description"
                                  name="button_description"
                                  rows="3"
                                  class="large-text"><?php echo esc_textarea($button->button_description); ?></textarea>
                        <p class="description">
                            Descrizione interna del pulsante (opzionale).
                        </p>
                    </td>
                </tr>
                
                <!-- Stati Ordine -->
                <tr>
                    <th scope="row">
                        <label>Stati Ordine <span class="required">*</span></label>
                    </th>
                    <td>
                        <fieldset>
                            <legend class="screen-reader-text">
                                Seleziona gli stati ordine per cui mostrare questo pulsante
                            </legend>

                            <div style="margin-bottom: 15px; padding: 10px; background: #f0f8ff; border: 1px solid #0073aa; border-radius: 4px;">
                                <label style="font-weight: bold;">
                                    <input type="checkbox"
                                           id="show_on_all_statuses"
                                           name="show_on_all_statuses"
                                           value="1"
                                           <?php checked(count((array)$button->order_statuses) >= count($order_statuses)); ?>>
                                    🌟 Mostra su TUTTI gli stati ordine (inclusi fallito, annullato, rimborsato)
                                </label>
                                <p style="margin: 5px 0 0 25px; font-size: 12px; color: #666;">
                                    Seleziona questa opzione per mostrare il pulsante su qualsiasi stato ordine,
                                    inclusi quelli "negativi" come fallito, annullato, rimborsato.
                                </p>
                            </div>

                            <div id="individual_statuses">
                                <?php foreach ($order_statuses as $status_key => $status_name): ?>
                                    <label>
                                        <input type="checkbox"
                                               name="order_statuses[]"
                                               value="<?php echo esc_attr($status_key); ?>"
                                               <?php checked(in_array($status_key, (array)$button->order_statuses)); ?>>
                                        <?php echo esc_html($status_name); ?>
                                    </label><br>
                                <?php endforeach; ?>
                            </div>

                            <p class="description">
                                Seleziona gli stati ordine specifici per cui questo pulsante sarà visibile,
                                oppure usa l'opzione "Mostra su TUTTI gli stati" per includerli tutti.
                            </p>
                        </fieldset>
                    </td>
                </tr>
                
                <!-- Codice Unicode Dashicon -->
                <tr>
                    <th scope="row">
                        <label for="dashicon_code">Icona Pulsante (Unicode)</label>
                    </th>
                    <td>
                        <div class="weab-unicode-selector">
                            <input type="text"
                                   id="dashicon_code"
                                   name="dashicon_code"
                                   value="<?php echo esc_attr($button->dashicon_code); ?>"
                                   class="regular-text"
                                   placeholder="\f465">

                            <div class="unicode-preview" style="margin: 10px 0;">
                                <span class="unicode-icon" style="font-family: Dashicons; font-size: 24px; margin-right: 10px;" data-unicode="<?php echo esc_attr($button->dashicon_code); ?>"></span>
                                <span class="preview-text">Anteprima icona</span>
                                <span class="unicode-display" style="margin-left: 10px; color: #666; font-family: monospace;"></span>
                            </div>

                            <div class="weab-unicode-grid" style="margin-top: 15px;">
                                <h4 style="margin-bottom: 10px;">Codici Unicode più comuni:</h4>
                                <div class="unicode-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)); gap: 10px; max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9;">
                                    <?php
                                    $common_unicodes = array(
                                        'f465' => 'Email',
                                        'f466' => 'Email Alt',
                                        'f147' => 'Conferma',
                                        'f158' => 'Rifiuta',
                                        'f534' => 'Avviso',
                                        'f348' => 'Info',
                                        'f16d' => 'Notifica',
                                        'f174' => 'Carrello',
                                        'f526' => 'Pagamento',
                                        'f316' => 'Scarica',
                                        'f317' => 'Carica',
                                        'f237' => 'Condividi',
                                        'f155' => 'Stella',
                                        'f227' => 'Importante',
                                        'f323' => 'Tag',
                                        'f145' => 'Data',
                                        'f469' => 'Tempo',
                                        'f546' => 'Allegato',
                                        'f504' => 'Esterno',
                                        'f117' => 'Commenti',
                                        'f132' => 'Più',
                                        'f460' => 'Meno',
                                        'f464' => 'Modifica',
                                        'f182' => 'Elimina'
                                    );

                                    foreach ($common_unicodes as $unicode => $icon_name) {
                                        echo '<div class="unicode-option" style="text-align: center; cursor: pointer; padding: 8px; border: 1px solid #ddd; border-radius: 3px; background: white;" data-unicode="' . esc_attr($unicode) . '">';
                                        echo '<span style="font-family: Dashicons; font-size: 20px; display: block; margin-bottom: 5px;">&#x' . esc_attr($unicode) . ';</span>';
                                        echo '<small>' . esc_html($unicode) . '<br>' . esc_html($icon_name) . '</small>';
                                        echo '</div>';
                                    }
                                    ?>
                                </div>
                            </div>

                            <p class="description" style="margin-top: 15px;">
                                Seleziona un codice Unicode dalla griglia sopra o inserisci manualmente (es. \f465).
                                <a href="https://developer.wordpress.org/resource/dashicons/" target="_blank">
                                    Visualizza tutte le icone Dashicons disponibili
                                </a>
                            </p>
                        </div>
                    </td>
                </tr>
                
                <!-- Contenuto Email -->
                <tr>
                    <th scope="row">
                        <label for="email_content">Contenuto Email <span class="required">*</span></label>
                    </th>
                    <td>
                        <?php
                        wp_editor($button->email_content, 'email_content', array(
                            'textarea_name' => 'email_content',
                            'textarea_rows' => 10,
                            'media_buttons' => false,
                            'teeny' => false,
                            'quicktags' => true,
                            'tinymce' => array(
                                'toolbar1' => 'bold,italic,underline,strikethrough,|,bullist,numlist,|,link,unlink,|,undo,redo',
                                'toolbar2' => ''
                            )
                        ));
                        ?>
                        
                        <div class="weab-placeholders-help" style="margin-top: 15px; padding: 10px; background: #f9f9f9; border: 1px solid #ddd; border-radius: 4px;">
                            <h4 style="margin-top: 0; margin-bottom: 15px; color: #0073aa;">Placeholder disponibili (clicca per inserire):</h4>
                            <div class="placeholder-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 12px;">
                                <?php foreach (Email_Action_Buttons_Email::get_available_placeholders() as $placeholder => $description): ?>
                                    <div class="placeholder-item" style="background: white; padding: 10px; border-radius: 4px; border: 1px solid #ddd;">
                                        <code class="clickable-placeholder"
                                              style="background: #0073aa; color: white; padding: 4px 8px; border-radius: 3px; cursor: pointer; display: inline-block; margin-bottom: 5px; font-weight: bold; transition: all 0.2s ease;"
                                              data-placeholder="<?php echo esc_attr($placeholder); ?>"
                                              title="Clicca per inserire nel contenuto email">
                                            <?php echo esc_html($placeholder); ?>
                                        </code>
                                        <br>
                                        <small style="color: #666; line-height: 1.3;"><?php echo esc_html($description); ?></small>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <p style="margin-top: 15px; margin-bottom: 0; font-style: italic; color: #666;">
                                💡 <strong>Suggerimento:</strong> Clicca su qualsiasi placeholder sopra per inserirlo automaticamente nella posizione del cursore nell'editor email.
                            </p>
                        </div>
                        
                        <p class="description">
                            Il contenuto dell'email che verrà inviata al cliente. Puoi utilizzare i placeholder sopra per includere informazioni dinamiche dell'ordine.
                        </p>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <?php submit_button($submit_text, 'primary', 'submit', false); ?>
        
        <a href="<?php echo esc_url(admin_url('admin.php?page=woo-email-action-buttons')); ?>" class="button button-secondary" style="margin-left: 10px;">
            Annulla
        </a>
    </form>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Aggiorna anteprima icona Unicode quando cambia il codice
    $('#dashicon_code').on('input', function() {
        var unicodeInput = $(this).val() || 'f465';
        updateUnicodePreview(unicodeInput);
        updateUnicodeSelection(unicodeInput);
    });

    // Gestisci selezione Unicode dalla griglia
    $('.unicode-option').on('click', function() {
        var unicode = $(this).data('unicode');
        var unicodeWithBackslash = '\\' + unicode; // Aggiungi backslash automaticamente
        $('#dashicon_code').val(unicodeWithBackslash);
        updateUnicodePreview(unicodeWithBackslash);
        updateUnicodeSelection(unicode); // Per la selezione visiva usa senza backslash
    });

    // Funzione per aggiornare l'anteprima Unicode
    function updateUnicodePreview(unicodeInput) {
        // Rimuovi tutti i backslash iniziali (singoli o doppi)
        var cleanUnicode = unicodeInput.replace(/^\\+/, '');

        if (/^f[0-9a-fA-F]{3,4}$/i.test(cleanUnicode)) {
            $('.unicode-icon').html('&#x' + cleanUnicode + ';');
            $('.unicode-display').text('Unicode: ' + cleanUnicode);
        } else if (/^[0-9a-fA-F]{3,4}$/i.test(cleanUnicode)) {
            // Se è solo hex senza 'f', aggiungilo
            var fullUnicode = 'f' + cleanUnicode;
            $('.unicode-icon').html('&#x' + fullUnicode + ';');
            $('.unicode-display').text('Unicode: ' + fullUnicode);
        } else {
            // Prova a mostrare comunque l'input
            $('.unicode-icon').html('&#x' + cleanUnicode + ';');
            $('.unicode-display').text('Input: ' + unicodeInput);
        }
    }

    // Funzione per aggiornare la selezione visiva Unicode
    function updateUnicodeSelection(unicode) {
        // Rimuovi tutti i backslash per il confronto con data-unicode
        var cleanUnicode = unicode.replace(/^\\+/, '');

        // Se inizia con 'f', rimuovilo per il confronto
        if (cleanUnicode.toLowerCase().startsWith('f')) {
            cleanUnicode = cleanUnicode.substring(1);
        }

        $('.unicode-option').removeClass('selected').css({
            'border-color': '#ddd',
            'background': 'white'
        });

        // Cerca sia con che senza 'f' iniziale
        var selector = '.unicode-option[data-unicode="f' + cleanUnicode + '"], .unicode-option[data-unicode="' + cleanUnicode + '"]';
        $(selector).addClass('selected').css({
            'border-color': '#0073aa',
            'background': '#e1f5fe'
        });
    }

    // Inizializza l'anteprima corrente
    var currentUnicode = $('#dashicon_code').val();
    if (currentUnicode) {
        // Non modificare il valore esistente, usalo così com'è
        updateUnicodePreview(currentUnicode);
        updateUnicodeSelection(currentUnicode);
    } else {
        // Valore di default solo per nuovi pulsanti
        $('#dashicon_code').val('\\f465');
        updateUnicodePreview('\\f465');
        updateUnicodeSelection('\\f465');
    }

    // Gestisci il checkbox "Mostra su tutti gli stati"
    $('#show_on_all_statuses').on('change', function() {
        if ($(this).is(':checked')) {
            // Seleziona tutti gli stati
            $('#individual_statuses input[type="checkbox"]').prop('checked', true);
            $('#individual_statuses').css('opacity', '0.5');
        } else {
            // Deseleziona tutti gli stati
            $('#individual_statuses input[type="checkbox"]').prop('checked', false);
            $('#individual_statuses').css('opacity', '1');
        }
    });

    // Se vengono deselezionati manualmente alcuni stati, deseleziona "tutti gli stati"
    $('#individual_statuses input[type="checkbox"]').on('change', function() {
        var totalStatuses = $('#individual_statuses input[type="checkbox"]').length;
        var checkedStatuses = $('#individual_statuses input[type="checkbox"]:checked').length;

        if (checkedStatuses < totalStatuses) {
            $('#show_on_all_statuses').prop('checked', false);
            $('#individual_statuses').css('opacity', '1');
        } else if (checkedStatuses === totalStatuses) {
            $('#show_on_all_statuses').prop('checked', true);
            $('#individual_statuses').css('opacity', '0.5');
        }
    });

    // Inizializza lo stato visivo
    var totalStatuses = $('#individual_statuses input[type="checkbox"]').length;
    var checkedStatuses = $('#individual_statuses input[type="checkbox"]:checked').length;
    if (checkedStatuses === totalStatuses && totalStatuses > 0) {
        $('#show_on_all_statuses').prop('checked', true);
        $('#individual_statuses').css('opacity', '0.5');
    }

    // Gestisci click sui placeholder per inserimento automatico
    $('.clickable-placeholder').on('click', function() {
        var placeholder = $(this).data('placeholder');
        insertPlaceholderInEditor(placeholder);

        // Feedback visivo
        $(this).css('background', '#00a32a').text('Inserito!');
        setTimeout(() => {
            $(this).css('background', '#0073aa').text(placeholder);
        }, 1000);
    });

    // Hover effects per placeholder
    $('.clickable-placeholder').hover(
        function() {
            $(this).css({
                'background': '#005a87',
                'transform': 'translateY(-1px)',
                'box-shadow': '0 2px 4px rgba(0,0,0,0.2)'
            });
        },
        function() {
            $(this).css({
                'background': '#0073aa',
                'transform': 'translateY(0)',
                'box-shadow': 'none'
            });
        }
    );

    // Validazione form
    $('#weab-button-form').on('submit', function(e) {
        var buttonName = $('#button_name').val().trim();
        var emailContent = '';

        // Ottieni contenuto dall'editor
        if (typeof tinyMCE !== 'undefined' && tinyMCE.get('email_content')) {
            emailContent = tinyMCE.get('email_content').getContent();
        } else {
            emailContent = $('#email_content').val();
        }

        var selectedStatuses = $('input[name="order_statuses[]"]:checked').length;

        if (!buttonName) {
            alert('Il nome del pulsante è obbligatorio.');
            e.preventDefault();
            return false;
        }

        if (!emailContent.trim()) {
            alert('Il contenuto dell\'email è obbligatorio.');
            e.preventDefault();
            return false;
        }

        if (selectedStatuses === 0) {
            alert('Seleziona almeno uno stato ordine.');
            e.preventDefault();
            return false;
        }
    });
});

// Funzione migliorata per inserire placeholder nell'editor
function insertPlaceholderInEditor(placeholder) {
    // Prova prima con TinyMCE (editor visuale)
    if (typeof tinyMCE !== 'undefined' && tinyMCE.get('email_content') && !tinyMCE.get('email_content').isHidden()) {
        var editor = tinyMCE.get('email_content');
        editor.focus();
        editor.execCommand('mceInsertContent', false, placeholder);
        return;
    }

    // Fallback per textarea (modalità testo)
    var textarea = document.getElementById('email_content');
    if (textarea) {
        textarea.focus();

        var startPos = textarea.selectionStart;
        var endPos = textarea.selectionEnd;
        var textBefore = textarea.value.substring(0, startPos);
        var textAfter = textarea.value.substring(endPos);

        textarea.value = textBefore + placeholder + textAfter;

        // Posiziona il cursore dopo il placeholder inserito
        var newPos = startPos + placeholder.length;
        textarea.selectionStart = newPos;
        textarea.selectionEnd = newPos;

        // Trigger change event per aggiornare eventuali listener
        textarea.dispatchEvent(new Event('change', { bubbles: true }));
    }
}

// Funzione legacy per compatibilità
function insertPlaceholder(placeholder) {
    insertPlaceholderInEditor(placeholder);
}
</script>

<style>
.required {
    color: #d63638;
}

.placeholder-item {
    margin-bottom: 5px;
}

.placeholder-item code:hover {
    background: #e1f5fe !important;
}

.dashicon-preview {
    display: flex;
    align-items: center;
    gap: 10px;
}

.preview-text {
    color: #666;
    font-style: italic;
}

.weab-placeholders-help {
    border-radius: 4px;
}

.weab-placeholders-help h4 {
    margin-top: 0;
    margin-bottom: 10px;
}
</style>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Aggiorna anteprima icona quando cambia il codice
    $('#dashicon_code').on('input', function() {
        var iconCode = $(this).val() || 'dashicons-email';
        $('.dashicon-preview .dashicons').attr('class', 'dashicons ' + iconCode);
    });
    
    // Validazione form
    $('#weab-button-form').on('submit', function(e) {
        var buttonName = $('#button_name').val().trim();
        var emailContent = '';
        
        // Ottieni contenuto dall'editor
        if (typeof tinyMCE !== 'undefined' && tinyMCE.get('email_content')) {
            emailContent = tinyMCE.get('email_content').getContent();
        } else {
            emailContent = $('#email_content').val();
        }
        
        var selectedStatuses = $('input[name="order_statuses[]"]:checked').length;
        
        if (!buttonName) {
            alert('Il nome del pulsante è obbligatorio.');
            e.preventDefault();
            return false;
        }
        
        if (!emailContent.trim()) {
            alert('Il contenuto dell\'email è obbligatorio.');
            e.preventDefault();
            return false;
        }
        
        if (selectedStatuses === 0) {
            alert('Seleziona almeno uno stato ordine.');
            e.preventDefault();
            return false;
        }
    });
});
</script>

<style>
.required {
    color: #d63638;
}

.placeholder-item {
    margin-bottom: 5px;
}

.placeholder-item code:hover {
    background: #e1f5fe !important;
}

.dashicon-preview {
    display: flex;
    align-items: center;
    gap: 10px;
}

.preview-text {
    color: #666;
    font-style: italic;
}

.weab-placeholders-help {
    border-radius: 4px;
}

.weab-placeholders-help h4 {
    margin-top: 0;
    margin-bottom: 10px;
}
</style>
