<?php
if (!defined('ABSPATH')) {
    exit;
}

// Mostra eventuali messaggi di errore/successo
settings_errors('wcst_messages');
?>

<div class="wrap">
    <div class="tablenav top">
        <div class="alignleft actions">
            <a href="?page=woo-custom-status&action=new" class="button button-primary">
                <?php esc_html_e('Aggiungi Nuovo Status', 'woo-custom-status-jojo'); ?>
            </a>
        </div>
        <br class="clear">
    </div>

    <table class="wp-list-table widefat fixed striped">
        <thead>
            <tr>
                <th scope="col"><?php esc_html_e('Nome Status', 'woo-custom-status-jojo'); ?></th>
                <th scope="col"><?php esc_html_e('Slug', 'woo-custom-status-jojo'); ?></th>
                <th scope="col"><?php esc_html_e('Anteprima', 'woo-custom-status-jojo'); ?></th>
                <th scope="col"><?php esc_html_e('Pulsante Azione', 'woo-custom-status-jojo'); ?></th>
                <th scope="col"><?php esc_html_e('Azioni', 'woo-custom-status-jojo'); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($statuses)) : ?>
                <tr>
                    <td colspan="5"><?php esc_html_e('Nessuno status ordine custom trovato.', 'woo-custom-status-jojo'); ?></td>
                </tr>
            <?php else : ?>
                <?php foreach ($statuses as $status) : ?>
                    <tr>
                        <td><?php echo esc_html($status->status_name); ?></td>
                        <td><?php echo esc_html($status->status_slug); ?></td>
                        <td>
                            <span class="order-status" style="background-color: <?php echo esc_attr($status->background_color); ?>; color: <?php echo esc_attr($status->text_color); ?>;">
                                <?php echo esc_html($status->status_name); ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($status->show_action_button) : ?>
                                <span class="dashicons" style="font-family: dashicons !important; content: '\\<?php echo esc_attr($status->action_icon); ?>' !important;">&#x<?php echo esc_attr($status->action_icon); ?>;</span>
                            <?php else : ?>
                                <?php esc_html_e('No', 'woo-custom-status-jojo'); ?>
                            <?php endif; ?>
                        </td>
                        <td>
                            <a href="?page=woo-custom-status&action=edit&id=<?php echo esc_attr($status->id); ?>" 
                               class="button button-small">
                                <?php esc_html_e('Modifica', 'woo-custom-status-jojo'); ?>
                            </a>
                            <a href="<?php echo wp_nonce_url('?page=woo-custom-status&action=delete&id=' . esc_attr($status->id), 'delete_status'); ?>" 
                               class="button button-small button-link-delete" 
                               onclick="return confirm('<?php esc_attr_e('Sei sicuro di voler eliminare questo status?', 'woo-custom-status-jojo'); ?>');">
                                <?php esc_html_e('Elimina', 'woo-custom-status-jojo'); ?>
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<style>
.order-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    line-height: 1.4;
}
</style> 