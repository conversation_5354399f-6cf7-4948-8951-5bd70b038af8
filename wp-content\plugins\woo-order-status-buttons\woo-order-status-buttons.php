<?php
/**
 * Plugin Name: WooCommerce Pulsanti Status Ordine
 * Plugin URI: 
 * Description: Aggiunge pulsanti di azione personalizzati alla lista ordini di WooCommerce per aggiornare lo status e inviare email ai clienti.
 * Version: 1.0.0
 * Author: 
 * Text Domain: woo-order-status-buttons
 * Domain Path: /languages
 * Requires at least: 5.8
 * Requires PHP: 8.0
 * WC requires at least: 6.0
 * WC tested up to: 8.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Verifica che WooCommerce sia attivo
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins'))) && !function_exists('WC')) {
    add_action('admin_notices', function() {
        echo '<div class="error"><p>WooCommerce Pulsanti Status Ordine richiede WooCommerce per funzionare.</p></div>';
    });
    return;
}

// Definizione costanti
define('WOSB_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WOSB_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WOSB_VERSION', '1.0.0');

// Caricamento file delle classi
require_once WOSB_PLUGIN_DIR . 'includes/class-order-status-buttons-db.php';
require_once WOSB_PLUGIN_DIR . 'includes/class-order-status-buttons-admin.php';
require_once WOSB_PLUGIN_DIR . 'includes/class-order-status-buttons-email.php';
require_once WOSB_PLUGIN_DIR . 'includes/class-order-status-buttons-integration.php';

/**
 * Inizializzazione del plugin
 */
function wosb_init() {
    // Verifica che WooCommerce sia completamente caricato
    if (!class_exists('WooCommerce') && !function_exists('WC')) {
        // Se WooCommerce non è ancora caricato, riprova più tardi
        add_action('woocommerce_init', 'wosb_init');
        return;
    }

    // Inizializza le classi solo se siamo nell'admin
    if (is_admin()) {
        new Order_Status_Buttons_Admin();
        new Order_Status_Buttons_Integration();
    }

    // Inizializza sempre la classe email per gestire le richieste
    new Order_Status_Buttons_Email();
}
add_action('plugins_loaded', 'wosb_init');

/**
 * Attivazione del plugin
 */
register_activation_hook(__FILE__, 'wosb_activate');
function wosb_activate() {
    // Verifica che WooCommerce sia attivo
    if (!class_exists('WooCommerce')) {
        deactivate_plugins(plugin_basename(__FILE__));
        wp_die('Questo plugin richiede WooCommerce per funzionare.');
    }
    
    // Crea le tabelle del database
    $db = new Order_Status_Buttons_DB();
    $db->create_tables();
    
    // Flush rewrite rules
    flush_rewrite_rules();
}

/**
 * Disattivazione del plugin
 */
register_deactivation_hook(__FILE__, 'wosb_deactivate');
function wosb_deactivate() {
    // Flush rewrite rules
    flush_rewrite_rules();
}

/**
 * Funzione helper per ottenere l'istanza del database
 */
function wosb_get_db() {
    return new Order_Status_Buttons_DB();
}

/**
 * Funzione helper per il logging degli errori
 */
function wosb_log($message, $level = 'info') {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('[WooCommerce Pulsanti Status Ordine] ' . $message);
    }
}

/**
 * Funzione helper per verificare le capacità dell'utente
 */
function wosb_user_can_manage() {
    return current_user_can('manage_woocommerce');
}

/**
 * Funzione helper per sanitizzare l'input
 */
function wosb_sanitize_input($input, $type = 'text') {
    switch ($type) {
        case 'text':
            return sanitize_text_field($input);
        case 'textarea':
            return sanitize_textarea_field($input);
        case 'email':
            return sanitize_email($input);
        case 'url':
            return esc_url_raw($input);
        case 'html':
            return wp_kses_post($input);
        case 'array':
            return is_array($input) ? array_map('sanitize_text_field', $input) : array();
        default:
            return sanitize_text_field($input);
    }
}

/**
 * Funzione helper per ottenere tutti gli stati ordine WooCommerce
 */
function wosb_get_order_statuses() {
    $statuses = wc_get_order_statuses();
    return $statuses;
}

/**
 * Funzione helper per verificare se un ordine ha uno stato specifico
 */
function wosb_order_has_status($order, $statuses) {
    if (!is_array($statuses) || empty($statuses)) {
        return false;
    }
    
    $order_status = 'wc-' . $order->get_status();
    return in_array($order_status, $statuses);
}

/**
 * Funzione helper per ottenere il nome leggibile di uno status
 */
function wosb_get_status_name($status) {
    $statuses = wc_get_order_statuses();
    return isset($statuses[$status]) ? $statuses[$status] : $status;
}

/**
 * Funzione helper per validare il codice unicode dashicon
 */
function wosb_validate_dashicon($code) {
    // Rimuovi eventuali spazi e caratteri non necessari
    $code = trim($code);
    
    // Se inizia con \f, è già nel formato corretto
    if (strpos($code, '\f') === 0) {
        return $code;
    }
    
    // Se è solo il codice numerico, aggiungi \f
    if (preg_match('/^[0-9a-fA-F]{3,4}$/', $code)) {
        return '\f' . $code;
    }
    
    // Default fallback
    return '\f147'; // admin-post icon
}

/**
 * Funzione helper per convertire unicode dashicon in CSS
 */
function wosb_dashicon_to_css($unicode) {
    // Rimuovi \f e converti in formato CSS
    $code = str_replace('\f', '', $unicode);
    return '\f' . $code;
}
