<?php
/**
 * Plugin Name: WooCommerce Pagamento a Rate
 * Plugin URI: #
 * Description: Aggiunge un metodo di pagamento "Pagamento a Rate" a WooCommerce
 * Version: 1.0.0
 * Author: Developer
 * Author URI: #
 * Text Domain: woo-pagamento-rate
 * Domain Path: /languages
 * Requires at least: 5.0
 * Requires PHP: 7.2
 * WC requires at least: 4.0
 * WC tested up to: 8.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Verifica se WooCommerce è attivo
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
    return;
}

define('WPR_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WPR_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * Funzione di attivazione del plugin
 */
function wpr_activate() {
    // Crea le cartelle per i template delle email se non esistono
    $template_dir = WPR_PLUGIN_DIR . 'templates/emails/plain';
    if (!is_dir($template_dir)) {
        wp_mkdir_p($template_dir);
    }
    
    // Copia i template delle email se non esistono
    wpr_copy_templates();
    
    // Verifica se WooCommerce è attivo
    if (!class_exists('WooCommerce')) {
        deactivate_plugins(plugin_basename(__FILE__));
        wp_die(__('Questo plugin richiede WooCommerce per funzionare. Installa e attiva WooCommerce prima di attivare questo plugin.', 'woo-pagamento-rate'));
    }
    
    // Cancella eventuali errori di attivazione precedenti
    delete_option('wpr_activation_error');
    
    // Log dell'attivazione
    if (class_exists('WPR_Debug')) {
        WPR_Debug::log('Plugin WooCommerce Pagamento a Rate attivato', 'info');
    }
}
register_activation_hook(__FILE__, 'wpr_activate');

/**
 * Copia i template delle email nella cartella corretta
 */
function wpr_copy_templates() {
    // Verifica e crea le cartelle dei template
    $template_dirs = array(
        WPR_PLUGIN_DIR . 'templates/',
        WPR_PLUGIN_DIR . 'templates/emails/',
        WPR_PLUGIN_DIR . 'templates/emails/plain/'
    );
    
    foreach ($template_dirs as $dir) {
        if (!is_dir($dir)) {
            wp_mkdir_p($dir);
        }
    }
    
    // Definisci i file dei template e il loro contenuto
    $templates = array(
        'templates/emails/wpr-payment-email.php' => file_exists(WPR_PLUGIN_DIR . 'templates/emails/wpr-payment-email.php') ? 
            file_get_contents(WPR_PLUGIN_DIR . 'templates/emails/wpr-payment-email.php') : 
            '<?php
/**
 * Template per l\'email HTML di Pagamento a Rate
 */
defined(\'ABSPATH\') || exit;
do_action(\'woocommerce_email_header\', $email_heading, $email);
?>
<p><?php printf(__("Ciao %s,", "woo-pagamento-rate"), $order->get_billing_first_name()); ?></p>
<p><?php _e("Grazie per aver scelto il pagamento a rate.", "woo-pagamento-rate"); ?></p>
<p><?php _e("Ti contatteremo presto per i dettagli sul pagamento.", "woo-pagamento-rate"); ?></p>
<?php do_action(\'woocommerce_email_footer\', $email); ?>',
        
        'templates/emails/plain/wpr-payment-email.php' => file_exists(WPR_PLUGIN_DIR . 'templates/emails/plain/wpr-payment-email.php') ? 
            file_get_contents(WPR_PLUGIN_DIR . 'templates/emails/plain/wpr-payment-email.php') : 
            '<?php
/**
 * Template per l\'email di testo semplice di Pagamento a Rate
 */
defined(\'ABSPATH\') || exit;

echo "=" . wc_clean($email_heading) . "=\n\n";
echo sprintf(__("Ciao %s,", "woo-pagamento-rate"), $order->get_billing_first_name()) . "\n\n";
echo __("Grazie per aver scelto il pagamento a rate.", "woo-pagamento-rate") . "\n\n";
echo __("Ti contatteremo presto per i dettagli sul pagamento.", "woo-pagamento-rate") . "\n\n";'
    );
    
    // Copia i file dei template
    foreach ($templates as $file => $content) {
        $file_path = WPR_PLUGIN_DIR . $file;
        if (!file_exists($file_path)) {
            file_put_contents($file_path, $content);
        }
    }
}

/**
 * Funzione di disattivazione del plugin
 */
function wpr_deactivate() {
    // Pulizia al momento della disattivazione
    // Per ora non facciamo nulla, ma questa funzione può essere estesa in futuro
    
    // Log della disattivazione
    if (class_exists('WPR_Debug')) {
        WPR_Debug::log('Plugin WooCommerce Pagamento a Rate disattivato', 'info');
    }
}
register_deactivation_hook(__FILE__, 'wpr_deactivate');

/**
 * Gestione degli errori del plugin
 */
function wpr_admin_notices() {
    if ($error = get_option('wpr_activation_error')) {
        echo '<div class="error"><p>' . esc_html($error) . '</p></div>';
        delete_option('wpr_activation_error');
    }
}
add_action('admin_notices', 'wpr_admin_notices');

// Inizializza il plugin
function wpr_init() {
    load_plugin_textdomain('woo-pagamento-rate', false, dirname(plugin_basename(__FILE__)) . '/languages');
    
    // Include la classe di debug
    require_once WPR_PLUGIN_DIR . 'includes/class-wpr-debug.php';
    WPR_Debug::init();
}
add_action('plugins_loaded', 'wpr_init');

// Aggiungi il gateway di pagamento
function wpr_add_payment_gateway($gateways) {
    $gateways[] = 'WPR_Payment_Gateway';
    return $gateways;
}
add_filter('woocommerce_payment_gateways', 'wpr_add_payment_gateway');

// Registra la classe del gateway di pagamento
function wpr_register_gateway() {
    require_once WPR_PLUGIN_DIR . 'includes/class-wpr-payment-gateway.php';
}
add_action('plugins_loaded', 'wpr_register_gateway', 11);

// Registra l'email
function wpr_register_email($emails) {
    require_once WPR_PLUGIN_DIR . 'includes/emails/class-wpr-payment-email.php';
    $emails['WPR_Payment_Email'] = new WPR_Payment_Email();
    
    return $emails;
}
add_filter('woocommerce_email_classes', 'wpr_register_email'); 