<?php

/**

 * Classe per l'esportazione dei coupon

 *

 * @package WooCommerce Coupon Importer Exporter

 */



// Impedisci l'accesso diretto

if (!defined('ABSPATH')) {

    exit;

}



/**

 * Classe per l'esportazione dei coupon

 */

class WCCIE_Exporter {



    /**

     * Esporta i coupon in un file CSV

     *

     * @param string $coupon_status Stato dei coupon da esportare (all, publish, draft, ecc.)

     * @param bool $include_usage_logs Se includere i log di utilizzo

     * @return array|WP_Error Informazioni sul file esportato o errore

     */

    public function export_coupons($coupon_status = 'all', $include_usage_logs = true) {

        global $wpdb;

        

        // Crea directory temporanea se non esiste

        $upload_dir = wp_upload_dir();

        $export_dir = $upload_dir['basedir'] . '/wccie';

        

        if (!file_exists($export_dir)) {

            wp_mkdir_p($export_dir);

        }

        

        // Genera nome file

        $date = date('Y-m-d-H-i-s');

        $filename = 'wc-coupons-export-' . $date . '.csv';

        $file_path = $export_dir . '/' . $filename;

        

        // Ottieni i coupon

        $args = array(

            'post_type' => 'shop_coupon',

            'posts_per_page' => -1,

            'post_status' => $coupon_status === 'all' ? array('publish', 'draft', 'pending', 'private') : $coupon_status,

        );

        

        $coupons = get_posts($args);

        

        if (empty($coupons)) {

            return new WP_Error('no_coupons', __('Nessun coupon trovato con i criteri specificati.', 'wc-coupon-importer-exporter'));

        }

        

        // Apri file per la scrittura

        $file = fopen($file_path, 'w');

        

        if (!$file) {

            return new WP_Error('file_error', __('Impossibile creare il file di esportazione.', 'wc-coupon-importer-exporter'));

        }

        

        // Definisci intestazioni CSV

        $headers = array(

            'id',

            'code',

            'description',

            'discount_type',

            'amount',

            'expiry_date',

            'minimum_amount',

            'maximum_amount',

            'individual_use',

            'exclude_sale_items',

            'product_ids',

            'excluded_product_ids',

            'product_categories',

            'excluded_product_categories',

            'customer_emails',

            'usage_limit',

            'usage_limit_per_user',

            'limit_usage_to_x_items',

            'usage_count',

            'free_shipping',

            'exclude_shipping_methods',

            'product_attributes',

            'excluded_product_attributes',

            'status'

        );

        

        // Aggiungi intestazioni per i log di utilizzo se richiesto

        if ($include_usage_logs) {

            $headers[] = 'usage_logs';

        }

        

        // Scrivi intestazioni

        fputcsv($file, $headers);

        

        // Esporta ogni coupon

        foreach ($coupons as $coupon) {

            $wc_coupon = new WC_Coupon($coupon->ID);

            

            $row = array(

                'id' => $coupon->ID,

                'code' => $wc_coupon->get_code(),

                'description' => $wc_coupon->get_description(),

                'discount_type' => $wc_coupon->get_discount_type(),

                'amount' => $wc_coupon->get_amount(),

                'expiry_date' => $wc_coupon->get_date_expires() ? $wc_coupon->get_date_expires()->date('Y-m-d') : '',

                'minimum_amount' => $wc_coupon->get_minimum_amount(),

                'maximum_amount' => $wc_coupon->get_maximum_amount(),

                'individual_use' => $wc_coupon->get_individual_use() ? 'yes' : 'no',

                'exclude_sale_items' => $wc_coupon->get_exclude_sale_items() ? 'yes' : 'no',

                'product_ids' => implode(',', $wc_coupon->get_product_ids()),

                'excluded_product_ids' => implode(',', $wc_coupon->get_excluded_product_ids()),

                'product_categories' => implode(',', $wc_coupon->get_product_categories()),

                'excluded_product_categories' => implode(',', $wc_coupon->get_excluded_product_categories()),

                'customer_emails' => implode(',', $wc_coupon->get_email_restrictions()),

                'usage_limit' => $wc_coupon->get_usage_limit(),

                'usage_limit_per_user' => $wc_coupon->get_usage_limit_per_user(),

                'limit_usage_to_x_items' => $wc_coupon->get_limit_usage_to_x_items(),

                'usage_count' => $wc_coupon->get_usage_count(),

                'free_shipping' => $wc_coupon->get_free_shipping() ? 'yes' : 'no',

                'exclude_shipping_methods' => implode(',', (array) $wc_coupon->get_meta('exclude_shipping_methods', true)),

                'product_attributes' => $this->format_meta_array($wc_coupon->get_meta('product_attributes', true)),

                'excluded_product_attributes' => $this->format_meta_array($wc_coupon->get_meta('excluded_product_attributes', true)),

                'status' => $coupon->post_status

            );

            

            // Aggiungi log di utilizzo se richiesto

            if ($include_usage_logs) {

                $usage_logs = $this->get_coupon_usage_logs($coupon->ID);

                $row['usage_logs'] = $this->format_usage_logs($usage_logs);

            }

            

            // Scrivi riga

            fputcsv($file, $row);

        }

        

        // Chiudi file

        fclose($file);

        

        // Crea URL per il download

        $download_url = $upload_dir['baseurl'] . '/wccie/' . $filename;

        

        return array(

            'download_url' => $download_url,

            'filename' => $filename,

            'count' => count($coupons)

        );

    }

    

    /**

     * Formatta un array di metadati per l'esportazione

     *

     * @param array $meta_array Array di metadati

     * @return string Metadati formattati

     */

    private function format_meta_array($meta_array) {

        if (empty($meta_array) || !is_array($meta_array)) {

            return '';

        }

        

        $formatted = array();

        

        foreach ($meta_array as $key => $value) {

            if (is_array($value)) {

                $value = implode('|', $value);

            }

            $formatted[] = $key . ':' . $value;

        }

        

        return implode(';', $formatted);

    }

    

    /**

     * Ottiene i log di utilizzo di un coupon

     *

     * @param int $coupon_id ID del coupon

     * @return array Log di utilizzo

     */

    private function get_coupon_usage_logs($coupon_id) {

        global $wpdb;

        

        $logs = array();

        

        // Ottieni gli ordini che hanno utilizzato questo coupon

        $order_ids = $wpdb->get_col($wpdb->prepare("

            SELECT DISTINCT order_id 

            FROM {$wpdb->prefix}woocommerce_order_items 

            WHERE order_item_type = 'coupon' 

            AND order_item_name = (SELECT post_title FROM {$wpdb->posts} WHERE ID = %d)

        ", $coupon_id));

        

        if (empty($order_ids)) {

            return $logs;

        }

        

        foreach ($order_ids as $order_id) {

            $order = wc_get_order($order_id);

            

            if (!$order) {

                continue;

            }

            

            $user_id = $order->get_user_id();

            $user_email = $order->get_billing_email();

            $date_created = $order->get_date_created() ? $order->get_date_created()->date('Y-m-d H:i:s') : '';

            

            $logs[] = array(

                'order_id' => $order_id,

                'user_id' => $user_id,

                'user_email' => $user_email,

                'date' => $date_created

            );

        }

        

        return $logs;

    }

    

    /**

     * Formatta i log di utilizzo per l'esportazione

     *

     * @param array $logs Log di utilizzo

     * @return string Log formattati

     */

    private function format_usage_logs($logs) {

        if (empty($logs)) {

            return '';

        }

        

        $formatted = array();

        

        foreach ($logs as $log) {

            $formatted[] = implode('|', array(

                $log['order_id'],

                $log['user_id'],

                $log['user_email'],

                $log['date']

            ));

        }

        

        return implode(';', $formatted);

    }

} 