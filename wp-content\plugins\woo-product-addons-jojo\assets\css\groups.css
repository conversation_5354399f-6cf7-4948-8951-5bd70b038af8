/**
 * Stili CSS per la pagina di gestione dei gruppi di accessori
 */

/* Contenitore principale */
.wpaj-groups-container {
    margin-top: 20px;
}

/* Lista dei gruppi */
.wpaj-groups {
    margin: 0;
    padding: 0;
    list-style: none;
}

.wpaj-group-item {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 15px;
    padding: 15px;
}

.wpaj-group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.wpaj-group-header h3 {
    margin: 0;
    padding: 0;
}

.wpaj-group-actions {
    display: flex;
    gap: 10px;
}

.wpaj-group-description {
    margin-bottom: 10px;
    color: #666;
}

.wpaj-group-categories,
.wpaj-group-accessories {
    margin-bottom: 5px;
}

/* Form del gruppo */
#wpaj-group-form-container {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
}

.form-field {
    margin-bottom: 15px;
}

.form-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.form-field input[type="text"],
.form-field textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-field select {
    width: 100%;
    min-height: 100px;
}

.form-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

/* Contenitore dei prodotti accessori */
#group_accessories_container {
    margin-top: 10px;
    border: 1px solid #ddd;
    padding: 10px;
    max-height: 300px;
    overflow-y: auto;
}

/* Prodotto selezionato */
.selected-product {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #eee;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.selected-product:last-child {
    margin-bottom: 0;
}

/* Immagine del prodotto */
.selected-product .product-image {
    flex: 0 0 50px;
    margin-right: 10px;
}

.selected-product .product-image img {
    max-width: 100%;
    height: auto;
    border-radius: 3px;
}

/* Informazioni del prodotto */
.selected-product .product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.selected-product .product-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.selected-product .product-price {
    color: #666;
    font-size: 0.9em;
}

/* Pulsante di rimozione */
.selected-product .remove-product {
    color: #a00;
    text-decoration: none;
    cursor: pointer;
    padding: 5px;
}

.selected-product .remove-product:hover {
    color: #dc3232;
}

/* Select2 personalizzato */
.wpaj-select2 {
    width: 100%;
}

/* Messaggio di nessun accessorio */
.no-accessories-message {
    display: block;
    text-align: center;
    padding: 10px;
    color: #666;
}

/* Suggerimenti prodotti */
.product-suggestion {
    display: flex;
    align-items: center;
}

.product-suggestion-image {
    flex: 0 0 40px;
    margin-right: 10px;
}

.product-suggestion-image img {
    max-width: 100%;
    height: auto;
    border-radius: 3px;
}

.product-suggestion-info {
    flex: 1;
}

.product-suggestion-name {
    font-weight: bold;
}

.product-suggestion-price {
    font-size: 0.9em;
    color: #666;
} 