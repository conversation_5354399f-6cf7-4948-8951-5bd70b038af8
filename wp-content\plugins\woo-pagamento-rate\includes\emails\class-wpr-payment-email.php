<?php
/**
 * Classe per l'email di pagamento a rate
 */
class WPR_Payment_Email extends WC_Email {

    /**
     * Costruttore
     */
    public function __construct() {
        $this->id             = 'wpr_payment_email';
        $this->title          = __('Pagamento a Rate', 'woo-pagamento-rate');
        $this->description    = __('Email inviata al cliente quando seleziona il pagamento a rate.', 'woo-pagamento-rate');
        $this->template_html  = 'emails/wpr-payment-email.php';
        $this->template_plain = 'emails/plain/wpr-payment-email.php';
        $this->template_base  = WPR_PLUGIN_DIR . 'templates/';
        $this->customer_email = true;
        $this->placeholders   = array(
            '{site_title}'   => $this->get_blogname(),
            '{order_date}'   => '',
            '{order_number}' => '',
        );

        // Chiama il costruttore parent
        parent::__construct();
        
        // Verifica che le directory dei template esistano
        if (!is_dir($this->template_base)) {
            wp_mkdir_p($this->template_base . 'emails/plain');
        }
    }

    /**
     * Trigger per l'email
     *
     * @param int $order_id
     */
    public function trigger($order_id) {
        $this->setup_locale();
        
        if ($order_id) {
            $this->object = wc_get_order($order_id);
            
            if (is_a($this->object, 'WC_Order')) {
                $this->placeholders['{order_date}']   = wc_format_datetime($this->object->get_date_created());
                $this->placeholders['{order_number}'] = $this->object->get_order_number();
                $this->recipient = $this->object->get_billing_email();
            }
        }
        
        // Invia l'email solo se il metodo di pagamento è "pagamento_rate"
        if ($this->is_enabled() && $this->get_recipient() && $this->object && $this->object->get_payment_method() === 'pagamento_rate') {
            $this->send($this->get_recipient(), $this->get_subject(), $this->get_content(), $this->get_headers(), $this->get_attachments());
        }
        
        $this->restore_locale();
    }

    /**
     * Ottieni il contenuto HTML dell'email
     *
     * @return string
     */
    public function get_content_html() {
        return wc_get_template_html(
            $this->template_html,
            array(
                'order'         => $this->object,
                'email_heading' => $this->get_heading(),
                'sent_to_admin' => false,
                'plain_text'    => false,
                'email'         => $this,
                'additional_content' => $this->get_additional_content()
            ),
            '',
            $this->template_base
        );
    }

    /**
     * Ottieni il contenuto dell'email in testo semplice
     *
     * @return string
     */
    public function get_content_plain() {
        return wc_get_template_html(
            $this->template_plain,
            array(
                'order'         => $this->object,
                'email_heading' => $this->get_heading(),
                'sent_to_admin' => false,
                'plain_text'    => true,
                'email'         => $this,
                'additional_content' => $this->get_additional_content()
            ),
            '',
            $this->template_base
        );
    }

    /**
     * Inizializza le impostazioni del form
     */
    public function init_form_fields() {
        $this->form_fields = array(
            'enabled' => array(
                'title'   => __('Abilita/Disabilita', 'woo-pagamento-rate'),
                'type'    => 'checkbox',
                'label'   => __('Abilita questa notifica email', 'woo-pagamento-rate'),
                'default' => 'yes',
            ),
            'subject' => array(
                'title'       => __('Oggetto', 'woo-pagamento-rate'),
                'type'        => 'text',
                'desc_tip'    => true,
                'description' => __('Formato dell\'oggetto dell\'email che può contenere placeholders.', 'woo-pagamento-rate'),
                'placeholder' => $this->get_default_subject(),
                'default'     => $this->get_default_subject(),
            ),
            'heading' => array(
                'title'       => __('Intestazione Email', 'woo-pagamento-rate'),
                'type'        => 'text',
                'desc_tip'    => true,
                'description' => __('Formato dell\'intestazione dell\'email che può contenere placeholders.', 'woo-pagamento-rate'),
                'placeholder' => $this->get_default_heading(),
                'default'     => $this->get_default_heading(),
            ),
            'additional_content' => array(
                'title'       => __('Contenuto Aggiuntivo', 'woo-pagamento-rate'),
                'description' => __('Testo da aggiungere all\'email.', 'woo-pagamento-rate'),
                'css'         => 'width:400px; height: 75px;',
                'placeholder' => __('N/A', 'woo-pagamento-rate'),
                'type'        => 'textarea',
                'default'     => $this->get_default_additional_content(),
                'desc_tip'    => true,
            ),
            'email_type' => array(
                'title'       => __('Formato Email', 'woo-pagamento-rate'),
                'type'        => 'select',
                'description' => __('Scegli il formato dell\'email.', 'woo-pagamento-rate'),
                'default'     => 'html',
                'class'       => 'email_type wc-enhanced-select',
                'options'     => $this->get_email_type_options(),
                'desc_tip'    => true,
            ),
        );
    }

    /**
     * Ottieni l'oggetto di default dell'email
     *
     * @return string
     */
    public function get_default_subject() {
        return __('Dettagli del tuo Pagamento a Rate per l\'ordine {order_number}', 'woo-pagamento-rate');
    }

    /**
     * Ottieni l'intestazione di default dell'email
     *
     * @return string
     */
    public function get_default_heading() {
        return __('Dettagli del tuo Pagamento a Rate', 'woo-pagamento-rate');
    }

    /**
     * Ottieni il contenuto aggiuntivo di default dell'email
     *
     * @return string
     */
    public function get_default_additional_content() {
        return __('Grazie per aver scelto il pagamento a rate. Ti contatteremo presto per i dettagli sulla gestione del pagamento.', 'woo-pagamento-rate');
    }
} 