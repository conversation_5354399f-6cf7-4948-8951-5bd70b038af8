<?php
if (!defined('ABSPATH')) {
    exit;
}

$is_edit = isset($status) && $status;
$title = $is_edit ? __('Modifica Status Ordine', 'woo-custom-status-jojo') : __('Nuovo Status Ordine', 'woo-custom-status-jojo');

// Ottieni tutti gli status di WooCommerce
$wc_statuses = wc_get_order_statuses();
$allowed_statuses = $is_edit && $status->allowed_statuses ? explode(',', $status->allowed_statuses) : [];
?>

<div class="wrap">
    <h2><?php echo esc_html($title); ?></h2>

    <?php settings_errors('wcst_messages'); ?>

    <form method="post" action="" class="wcst-form">
        <?php wp_nonce_field('wcst_status_action'); ?>
        
        <?php if ($is_edit) : ?>
            <input type="hidden" name="status_id" value="<?php echo esc_attr($status->id); ?>">
        <?php endif; ?>

        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="status_name"><?php esc_html_e('Nome Status', 'woo-custom-status-jojo'); ?></label>
                </th>
                <td>
                    <input type="text" 
                           name="status_name" 
                           id="status_name" 
                           class="regular-text" 
                           value="<?php echo $is_edit ? esc_attr($status->status_name) : ''; ?>" 
                           required>
                    <p class="description">
                        <?php esc_html_e('Il nome dello status che apparirà nel pannello admin.', 'woo-custom-status-jojo'); ?>
                    </p>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="status_slug"><?php esc_html_e('Slug Status', 'woo-custom-status-jojo'); ?></label>
                </th>
                <td>
                    <input type="text" 
                           name="status_slug" 
                           id="status_slug" 
                           class="regular-text" 
                           value="<?php echo $is_edit ? esc_attr($status->status_slug) : ''; ?>" 
                           required>
                    <p class="description">
                        <?php esc_html_e('Lo slug verrà generato automaticamente dal nome dello status.', 'woo-custom-status-jojo'); ?>
                    </p>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="background_color"><?php esc_html_e('Colore Sfondo Etichetta', 'woo-custom-status-jojo'); ?></label>
                </th>
                <td>
                    <input type="text" 
                           name="background_color" 
                           id="background_color" 
                           class="wcst-color-picker" 
                           value="<?php echo $is_edit ? esc_attr($status->background_color) : '#e5e5e5'; ?>" 
                           required>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="text_color"><?php esc_html_e('Colore Testo Etichetta', 'woo-custom-status-jojo'); ?></label>
                </th>
                <td>
                    <input type="text" 
                           name="text_color" 
                           id="text_color" 
                           class="wcst-color-picker" 
                           value="<?php echo $is_edit ? esc_attr($status->text_color) : '#000000'; ?>" 
                           required>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <?php esc_html_e('Pulsante Azione', 'woo-custom-status-jojo'); ?>
                </th>
                <td>
                    <label>
                        <input type="checkbox" 
                               name="show_action_button" 
                               value="1" 
                               <?php checked($is_edit && $status->show_action_button); ?>>
                        <?php esc_html_e('Mostra pulsante azione nella lista ordini', 'woo-custom-status-jojo'); ?>
                    </label>
                </td>
            </tr>

            <tr class="action-button-options" style="display: none;">
                <th scope="row">
                    <label for="action_icon"><?php esc_html_e('Icona Pulsante', 'woo-custom-status-jojo'); ?></label>
                </th>
                <td>
                    <input type="text" 
                           name="action_icon" 
                           id="action_icon" 
                           class="regular-text" 
                           value="<?php echo $is_edit ? esc_attr($status->action_icon) : ''; ?>"
                           placeholder="f227">
                    <p class="description">
                        <?php 
                        printf(
                            __('Inserisci il codice esadecimale della dashicon (es. f227). Puoi trovare la lista completa con i codici <a href="%s" target="_blank">qui</a>.', 'woo-custom-status-jojo'),
                            'https://developer.wordpress.org/resource/dashicons/'
                        );
                        ?>
                    </p>
                </td>
            </tr>

            <tr class="action-button-options" >
                <th scope="row">
                    <label for="allowed_statuses"><?php esc_html_e('Mostra Pulsante per Status', 'woo-custom-status-jojo'); ?></label>
                </th>
                <td>
                    <select name="allowed_statuses[]" id="allowed_statuses" class="wc-enhanced-select" multiple="multiple" style="width: 400px;">
                        <?php foreach ($wc_statuses as $status_key => $status_name) : 
                            $status_key = str_replace('wc-', '', $status_key);
                        ?>
                            <option value="<?php echo esc_attr($status_key); ?>" 
                                    <?php selected(in_array($status_key, $allowed_statuses)); ?>>
                                <?php echo esc_html($status_name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="description">
                        <?php esc_html_e('Seleziona gli status per cui il pulsante azione sarà visibile. Se non selezioni nessuno status, il pulsante sarà visibile per tutti gli status.', 'woo-custom-status-jojo'); ?>
                    </p>
                </td>
            </tr>
        </table>

        <p class="submit">
            <input type="submit" 
                   name="submit_status" 
                   class="button button-primary" 
                   value="<?php echo $is_edit ? esc_attr__('Aggiorna Status', 'woo-custom-status-jojo') : esc_attr__('Crea Status', 'woo-custom-status-jojo'); ?>">
            <a href="?page=woo-custom-status" class="button">
                <?php esc_html_e('Annulla', 'woo-custom-status-jojo'); ?>
            </a>
        </p>
    </form>
</div>

<style>
.wcst-form .form-table th {
    width: 200px;
}
</style>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Gestione visualizzazione opzioni pulsante azione
    var showActionButton = $('input[name="show_action_button"]');
    var actionButtonOptions = $('.action-button-options');

    function toggleActionButtonOptions() {
        actionButtonOptions.toggle(showActionButton.is(':checked'));
    }

    showActionButton.on('change', toggleActionButtonOptions);
    toggleActionButtonOptions(); // Esegui subito per impostare lo stato iniziale

    // Inizializza select2 con opzioni specifiche
    if (typeof $.fn.select2 !== 'undefined') {
        $('.wc-enhanced-select').select2({
            width: '400px',
            placeholder: 'Seleziona gli status...',
            allowClear: true
        });
    } else {
        console.log('Select2 non è disponibile. Verifica che sia caricato correttamente.');
    }
});
</script> 