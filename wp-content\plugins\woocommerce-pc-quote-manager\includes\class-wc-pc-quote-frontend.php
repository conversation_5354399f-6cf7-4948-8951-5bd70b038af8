<?php
if (!defined('ABSPATH')) {
    exit;
}

class WC_PC_Quote_Frontend {
    
    public function __construct() {
        // Aggiungi filtri per personalizzazioni frontend se necessario
        add_action('wp_head', array($this, 'add_frontend_styles'));
    }

    /**
     * Aggiunge stili frontend personalizzati
     */
    public function add_frontend_styles() {
        if ($this->is_quote_form_page()) {
            ?>
            <style>
                .wc-pc-quote-form-container {
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background: #f9f9f9;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                
                .wc-pc-quote-form-container h3 {
                    text-align: center;
                    margin-bottom: 30px;
                    color: #333;
                }
                
                .wc-pc-quote-success {
                    background: #d4edda;
                    color: #155724;
                    padding: 15px;
                    border-radius: 5px;
                    border: 1px solid #c3e6cb;
                    margin-bottom: 20px;
                    text-align: center;
                }
                
                .wc-pc-quote-field {
                    margin-bottom: 20px;
                }
                
                .wc-pc-quote-field label {
                    display: block;
                    margin-bottom: 5px;
                    font-weight: bold;
                    color: #333;
                }
                
                .wc-pc-quote-field input[type="text"],
                .wc-pc-quote-field input[type="email"],
                .wc-pc-quote-field select,
                .wc-pc-quote-field textarea {
                    width: 100%;
                    padding: 10px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    font-size: 14px;
                    box-sizing: border-box;
                }
                
                .wc-pc-quote-field textarea {
                    resize: vertical;
                    min-height: 100px;
                }
                
                .wc-pc-quote-checkboxes {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 10px;
                    margin-top: 10px;
                }
                
                .wc-pc-quote-checkboxes label {
                    display: flex;
                    align-items: center;
                    font-weight: normal;
                    margin-bottom: 0;
                }
                
                .wc-pc-quote-checkboxes input[type="checkbox"] {
                    width: auto;
                    margin-right: 8px;
                }
                
                .wc-pc-quote-submit {
                    text-align: center;
                    margin-top: 30px;
                }
                
                .wc-pc-quote-submit input[type="submit"] {
                    background: #0073aa;
                    color: white;
                    padding: 12px 30px;
                    border: none;
                    border-radius: 4px;
                    font-size: 16px;
                    cursor: pointer;
                    transition: background-color 0.3s;
                }
                
                .wc-pc-quote-submit input[type="submit"]:hover {
                    background: #005a87;
                }
                
                @media (max-width: 768px) {
                    .wc-pc-quote-form-container {
                        margin: 10px;
                        padding: 15px;
                    }
                    
                    .wc-pc-quote-checkboxes {
                        grid-template-columns: 1fr;
                    }
                }
            </style>
            <?php
        }
    }

    /**
     * Verifica se la pagina corrente contiene il form dei preventivi
     */
    private function is_quote_form_page() {
        global $post;
        
        if (!$post) {
            return false;
        }
        
        // Controlla se il contenuto della pagina contiene lo shortcode
        return has_shortcode($post->post_content, 'pc_quote_form');
    }

    /**
     * Valida i dati del form
     */
    public static function validate_form_data($data) {
        $errors = array();

        // Verifica che $data sia un array
        if (!is_array($data)) {
            $errors['general'] = __('Dati del form non validi.', 'wc-pc-quote-manager');
            return $errors;
        }

        // Validazione nome
        if (empty($data['customer_name'])) {
            $errors['customer_name'] = __('Il nome è obbligatorio.', 'wc-pc-quote-manager');
        } elseif (strlen($data['customer_name']) > 255) {
            $errors['customer_name'] = __('Il nome è troppo lungo (massimo 255 caratteri).', 'wc-pc-quote-manager');
        }

        // Validazione email
        if (empty($data['customer_email'])) {
            $errors['customer_email'] = __('L\'email è obbligatoria.', 'wc-pc-quote-manager');
        } elseif (!is_email($data['customer_email'])) {
            $errors['customer_email'] = __('Inserisci un indirizzo email valido.', 'wc-pc-quote-manager');
        } elseif (strlen($data['customer_email']) > 255) {
            $errors['customer_email'] = __('L\'email è troppo lunga (massimo 255 caratteri).', 'wc-pc-quote-manager');
        }

        // Validazione telefono
        if (empty($data['customer_phone'])) {
            $errors['customer_phone'] = __('Il numero di telefono è obbligatorio.', 'wc-pc-quote-manager');
        } elseif (strlen($data['customer_phone']) > 100) {
            $errors['customer_phone'] = __('Il numero di telefono è troppo lungo (massimo 100 caratteri).', 'wc-pc-quote-manager');
        }

        // Validazione tipologia PC
        $valid_pc_types = array('Gaming', 'Office', 'Entrambi');
        if (empty($data['pc_type'])) {
            $errors['pc_type'] = __('Seleziona una tipologia PC.', 'wc-pc-quote-manager');
        } elseif (!in_array($data['pc_type'], $valid_pc_types)) {
            $errors['pc_type'] = __('Tipologia PC non valida. Seleziona tra: Gaming, Office, Entrambi.', 'wc-pc-quote-manager');
        }

        // Validazione budget
        $valid_budgets = array(
            'Da 500€ a 750€',
            'Da 750€ a 1000€',
            'Da 1000€ a 1250€',
            'Da 1250€ a 1500€',
            'Da 1500€ a 2000€',
            'Da 2000€ a 2500€',
            'Da 2500€ a 3000€',
            'Più di 3000€'
        );
        if (empty($data['budget'])) {
            $errors['budget'] = __('Seleziona una fascia di budget.', 'wc-pc-quote-manager');
        } elseif (!in_array($data['budget'], $valid_budgets)) {
            $errors['budget'] = __('Fascia di budget non valida.', 'wc-pc-quote-manager');
        }

        // Validazione preferenza processore
        $valid_processors = array('Intel', 'AMD', 'Nessuna Preferenza');
        if (empty($data['processor_preference'])) {
            $errors['processor_preference'] = __('Seleziona una preferenza processore.', 'wc-pc-quote-manager');
        } elseif (!in_array($data['processor_preference'], $valid_processors)) {
            $errors['processor_preference'] = __('Preferenza processore non valida.', 'wc-pc-quote-manager');
        }

        // Validazione preferenza scheda video
        $valid_graphics = array('Nvidia', 'AMD', 'Nessuna Preferenza');
        if (empty($data['graphics_preference'])) {
            $errors['graphics_preference'] = __('Seleziona una preferenza scheda video.', 'wc-pc-quote-manager');
        } elseif (!in_array($data['graphics_preference'], $valid_graphics)) {
            $errors['graphics_preference'] = __('Preferenza scheda video non valida.', 'wc-pc-quote-manager');
        }

        // Validazione bisogni aggiuntivi
        $valid_needs = array('Monitor', 'Tastiera', 'Mouse', 'Cuffie', 'Tappetino', 'Ho già Tutto');
        if (empty($data['additional_needs']) || !is_array($data['additional_needs'])) {
            $errors['additional_needs'] = __('Seleziona almeno una opzione per "Ho anche bisogno di".', 'wc-pc-quote-manager');
        } else {
            foreach ($data['additional_needs'] as $need) {
                if (!in_array($need, $valid_needs)) {
                    $errors['additional_needs'] = __('Una o più opzioni selezionate non sono valide.', 'wc-pc-quote-manager');
                    break;
                }
            }
        }

        // Validazione altre richieste (opzionale ma con limite di lunghezza)
        if (!empty($data['other_requests']) && strlen($data['other_requests']) > 1000) {
            $errors['other_requests'] = __('Il campo "Altre Richieste" è troppo lungo (massimo 1000 caratteri).', 'wc-pc-quote-manager');
        }

        return $errors;
    }

    /**
     * Sanitizza i dati del form
     */
    public static function sanitize_form_data($data) {
        // Verifica che $data sia un array
        if (!is_array($data)) {
            throw new Exception('Form data must be an array');
        }

        return array(
            'customer_name' => isset($data['customer_name']) ? sanitize_text_field($data['customer_name']) : '',
            'customer_email' => isset($data['customer_email']) ? sanitize_email($data['customer_email']) : '',
            'customer_phone' => isset($data['customer_phone']) ? sanitize_text_field($data['customer_phone']) : '',
            'pc_type' => isset($data['pc_type']) ? sanitize_text_field($data['pc_type']) : '',
            'budget' => isset($data['budget']) ? sanitize_text_field($data['budget']) : '',
            'processor_preference' => isset($data['processor_preference']) ? sanitize_text_field($data['processor_preference']) : '',
            'graphics_preference' => isset($data['graphics_preference']) ? sanitize_text_field($data['graphics_preference']) : '',
            'additional_needs' => isset($data['additional_needs']) && is_array($data['additional_needs']) ? array_map('sanitize_text_field', $data['additional_needs']) : array(),
            'other_requests' => isset($data['other_requests']) ? sanitize_textarea_field($data['other_requests']) : ''
        );
    }

    /**
     * Formatta i dati per la visualizzazione
     */
    public static function format_quote_data($quote) {
        return array(
            'id' => $quote->id,
            'customer_name' => $quote->customer_name,
            'customer_email' => $quote->customer_email,
            'customer_phone' => $quote->customer_phone,
            'pc_type' => $quote->pc_type,
            'budget' => $quote->budget,
            'processor_preference' => $quote->processor_preference,
            'graphics_preference' => $quote->graphics_preference,
            'additional_needs' => $quote->additional_needs,
            'other_requests' => $quote->other_requests,
            'status' => $quote->status,
            'admin_response' => $quote->admin_response,
            'created_at' => date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($quote->created_at)),
            'updated_at' => date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($quote->updated_at))
        );
    }
}
