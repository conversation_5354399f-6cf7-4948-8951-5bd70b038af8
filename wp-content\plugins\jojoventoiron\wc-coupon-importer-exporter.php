<?php

/**

 * Plugin Name: WooCommerce Coupon Importer Exporter

 * Plugin URI: https://example.com/wc-coupon-importer-exporter

 * Description: Un plugin per importare ed esportare i codici promozionali di WooCommerce e i loro log di utilizzo.

 * Version: 1.0.0

 * Author: Your Name

 * Author URI: https://example.com

 * Text Domain: wc-coupon-importer-exporter

 * Domain Path: /languages

 * Requires at least: 5.0

 * Requires PHP: 7.2

 * WC requires at least: 3.0.0

 * WC tested up to: 8.0.0

 */



// Impedisci l'accesso diretto

if (!defined('ABSPATH')) {

    exit;

}



// Definisci costanti

define('WCCIE_VERSION', '1.0.0');

define('WCCIE_PLUGIN_DIR', plugin_dir_path(__FILE__));

define('WCCIE_PLUGIN_URL', plugin_dir_url(__FILE__));

define('WCCIE_PLUGIN_BASENAME', plugin_basename(__FILE__));



// Verifica che WooCommerce sia attivo

function wccie_check_woocommerce_active() {

    if (!class_exists('WooCommerce')) {

        add_action('admin_notices', 'wccie_woocommerce_missing_notice');

        return false;

    }

    return true;

}



// Avviso se WooCommerce non è attivo

function wccie_woocommerce_missing_notice() {

    ?>

    <div class="error">

        <p><?php _e('WooCommerce Coupon Importer Exporter richiede che WooCommerce sia installato e attivo.', 'wc-coupon-importer-exporter'); ?></p>

    </div>

    <?php

}



// Carica il plugin

function wccie_init() {

    if (!wccie_check_woocommerce_active()) {

        return;

    }

    

    // Carica i file necessari

    require_once WCCIE_PLUGIN_DIR . 'includes/class-wccie-admin.php';

    require_once WCCIE_PLUGIN_DIR . 'includes/class-wccie-exporter.php';

    require_once WCCIE_PLUGIN_DIR . 'includes/class-wccie-importer.php';

    

    // Inizializza le classi

    new WCCIE_Admin();

}

add_action('plugins_loaded', 'wccie_init');



// Attivazione del plugin

function wccie_activate() {

    // Crea directory temporanea per i file CSV se non esiste

    $upload_dir = wp_upload_dir();

    $wccie_dir = $upload_dir['basedir'] . '/wccie';

    

    if (!file_exists($wccie_dir)) {

        wp_mkdir_p($wccie_dir);

    }

    

    // Crea un file .htaccess per proteggere la directory

    if (!file_exists($wccie_dir . '/.htaccess')) {

        $htaccess_content = "deny from all";

        file_put_contents($wccie_dir . '/.htaccess', $htaccess_content);

    }

}

register_activation_hook(__FILE__, 'wccie_activate');



// Disattivazione del plugin

function wccie_deactivate() {

    // Pulizia se necessario

}

register_deactivation_hook(__FILE__, 'wccie_deactivate'); 