<?php
if (!defined('ABSPATH')) {
    exit;
}

class WC_PC_Quote_Emails {
    
    public function __construct() {
        // Hook per nuovo preventivo
        add_action('wc_pc_quote_new_quote', array($this, 'send_new_quote_notification'));
        
        // Hook per risposta admin
        add_action('wc_pc_quote_admin_response', array($this, 'send_admin_response_email'));

        // Hook per risposta cliente
        add_action('wc_pc_quote_customer_response', array($this, 'send_customer_response_notification'), 10, 2);
    }

    /**
     * Invia notifica email all'admin per nuovo preventivo
     */
    public function send_new_quote_notification($quote_id) {
        global $wpdb;
        
        $quote = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}wc_pc_quotes WHERE id = %d",
            $quote_id
        ));

        if (!$quote) {
            return false;
        }

        // Email dell'admin (usa l'email dell'admin di WordPress)
        $admin_email = get_option('admin_email');
        
        // Oggetto email
        $subject = sprintf(
            __('[%s] Nuovo Preventivo PC - #%d', 'wc-pc-quote-manager'),
            get_bloginfo('name'),
            $quote->id
        );

        // Corpo email
        $message = $this->get_new_quote_email_template($quote);

        // Headers
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
        );

        // Invia email
        return wp_mail($admin_email, $subject, $message, $headers);
    }

    /**
     * Invia email di risposta al cliente
     */
    public function send_admin_response_email($quote_id) {
        global $wpdb;
        
        $quote = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}wc_pc_quotes WHERE id = %d",
            $quote_id
        ));

        if (!$quote || empty($quote->admin_response)) {
            return false;
        }

        // Email del cliente
        $customer_email = $quote->customer_email;
        
        // Oggetto email
        $subject = sprintf(
            __('[%s] Risposta al tuo Preventivo PC - #%d', 'wc-pc-quote-manager'),
            get_bloginfo('name'),
            $quote->id
        );

        // Corpo email
        $message = $this->get_admin_response_email_template($quote);

        // Headers
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
        );

        // Invia email
        return wp_mail($customer_email, $subject, $message, $headers);
    }

    /**
     * Template email per nuovo preventivo (admin)
     */
    private function get_new_quote_email_template($quote) {
        ob_start();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title><?php _e('Nuovo Preventivo PC', 'wc-pc-quote-manager'); ?></title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #0073aa; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .quote-details { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .quote-details h3 { margin-top: 0; color: #0073aa; }
                .detail-row { margin: 10px 0; }
                .detail-label { font-weight: bold; display: inline-block; width: 150px; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1><?php _e('Nuovo Preventivo PC Ricevuto', 'wc-pc-quote-manager'); ?></h1>
                </div>
                
                <div class="content">
                    <p><?php _e('È stato ricevuto un nuovo preventivo PC dal sito web.', 'wc-pc-quote-manager'); ?></p>
                    
                    <div class="quote-details">
                        <h3><?php _e('Dettagli Cliente', 'wc-pc-quote-manager'); ?></h3>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Nome:', 'wc-pc-quote-manager'); ?></span>
                            <?php echo esc_html($quote->customer_name); ?>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Email:', 'wc-pc-quote-manager'); ?></span>
                            <a href="mailto:<?php echo esc_attr($quote->customer_email); ?>"><?php echo esc_html($quote->customer_email); ?></a>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Telefono:', 'wc-pc-quote-manager'); ?></span>
                            <?php echo esc_html($quote->customer_phone); ?>
                        </div>
                    </div>

                    <div class="quote-details">
                        <h3><?php _e('Specifiche Richieste', 'wc-pc-quote-manager'); ?></h3>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Tipologia PC:', 'wc-pc-quote-manager'); ?></span>
                            <?php echo esc_html($quote->pc_type); ?>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Budget:', 'wc-pc-quote-manager'); ?></span>
                            <?php echo esc_html($quote->budget); ?>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Processore:', 'wc-pc-quote-manager'); ?></span>
                            <?php echo esc_html($quote->processor_preference); ?>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Scheda Video:', 'wc-pc-quote-manager'); ?></span>
                            <?php echo esc_html($quote->graphics_preference); ?>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Accessori:', 'wc-pc-quote-manager'); ?></span>
                            <?php echo esc_html($quote->additional_needs); ?>
                        </div>
                        <?php if (!empty($quote->other_requests)): ?>
                        <div class="detail-row">
                            <span class="detail-label"><?php _e('Altre Richieste:', 'wc-pc-quote-manager'); ?></span>
                            <?php echo nl2br(esc_html($quote->other_requests)); ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <p>
                        <a href="<?php echo esc_url(admin_url('admin.php?page=wc-pc-quotes&action=view&quote_id=' . $quote->id)); ?>" 
                           style="background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                            <?php _e('Visualizza e Rispondi', 'wc-pc-quote-manager'); ?>
                        </a>
                    </p>
                </div>

                <div class="footer">
                    <p><?php printf(__('Email inviata da %s', 'wc-pc-quote-manager'), get_bloginfo('name')); ?></p>
                </div>
            </div>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }

    /**
     * Template email per risposta admin (cliente)
     */
    private function get_admin_response_email_template($quote) {
        ob_start();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title><?php _e('Risposta al tuo Preventivo PC', 'wc-pc-quote-manager'); ?></title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #0073aa; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .quote-summary { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .response-box { background: #e8f5e8; padding: 20px; margin: 20px 0; border-radius: 5px; border-left: 4px solid #4caf50; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1><?php _e('Risposta al tuo Preventivo PC', 'wc-pc-quote-manager'); ?></h1>
                </div>
                
                <div class="content">
                    <p><?php printf(__('Ciao %s,', 'wc-pc-quote-manager'), esc_html($quote->customer_name)); ?></p>
                    
                    <p><?php _e('Abbiamo preparato una risposta al tuo preventivo PC. Ecco i dettagli:', 'wc-pc-quote-manager'); ?></p>

                    <div class="quote-summary">
                        <h3><?php printf(__('Preventivo #%d', 'wc-pc-quote-manager'), $quote->id); ?></h3>
                        <p><strong><?php _e('Tipologia:', 'wc-pc-quote-manager'); ?></strong> <?php echo esc_html($quote->pc_type); ?></p>
                        <p><strong><?php _e('Budget:', 'wc-pc-quote-manager'); ?></strong> <?php echo esc_html($quote->budget); ?></p>
                        <p><strong><?php _e('Processore:', 'wc-pc-quote-manager'); ?></strong> <?php echo esc_html($quote->processor_preference); ?></p>
                        <p><strong><?php _e('Scheda Video:', 'wc-pc-quote-manager'); ?></strong> <?php echo esc_html($quote->graphics_preference); ?></p>
                    </div>

                    <div class="response-box">
                        <h3><?php _e('La nostra risposta:', 'wc-pc-quote-manager'); ?></h3>
                        <p><?php echo nl2br(esc_html($quote->admin_response)); ?></p>
                    </div>

                    <p><?php _e('Se hai domande o vuoi procedere con l\'ordine, puoi rispondere direttamente cliccando il link qui sotto:', 'wc-pc-quote-manager'); ?></p>

                    <?php if (!empty($quote->response_token)): ?>
                    <p style="text-align: center; margin: 30px 0;">
                        <a href="<?php echo esc_url(home_url('/quote-response/?token=' . $quote->response_token)); ?>"
                           style="background: #0073aa; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                            <?php _e('Rispondi al Preventivo', 'wc-pc-quote-manager'); ?>
                        </a>
                    </p>
                    <p style="font-size: 12px; color: #666; text-align: center;">
                        <?php _e('Questo link è valido per 30 giorni dalla data di invio.', 'wc-pc-quote-manager'); ?>
                    </p>
                    <?php endif; ?>

                    <p><?php _e('Grazie per averci scelto!', 'wc-pc-quote-manager'); ?></p>
                </div>

                <div class="footer">
                    <p><?php printf(__('Email inviata da %s', 'wc-pc-quote-manager'), get_bloginfo('name')); ?></p>
                    <p><?php printf(__('Sito web: %s', 'wc-pc-quote-manager'), '<a href="' . home_url() . '">' . home_url() . '</a>'); ?></p>
                </div>
            </div>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }

    /**
     * Invia notifica email all'admin per risposta cliente
     */
    public function send_customer_response_notification($quote_id, $customer_message) {
        global $wpdb;

        $quote = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}wc_pc_quotes WHERE id = %d",
            $quote_id
        ));

        if (!$quote) {
            return false;
        }

        // Email dell'admin
        $admin_email = get_option('admin_email');

        // Oggetto email
        $subject = sprintf(
            __('[%s] Nuova Risposta Cliente - Preventivo #%d', 'wc-pc-quote-manager'),
            get_bloginfo('name'),
            $quote->id
        );

        // Corpo email
        $message = $this->get_customer_response_notification_template($quote, $customer_message);

        // Headers
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
        );

        // Invia email
        return wp_mail($admin_email, $subject, $message, $headers);
    }

    /**
     * Template email per notifica risposta cliente (admin)
     */
    private function get_customer_response_notification_template($quote, $customer_message) {
        ob_start();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title><?php _e('Nuova Risposta Cliente', 'wc-pc-quote-manager'); ?></title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #0073aa; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .quote-details { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .quote-details h3 { margin-top: 0; color: #0073aa; }
                .customer-message { background: #f1f8e9; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #4caf50; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1><?php _e('Nuova Risposta Cliente Ricevuta', 'wc-pc-quote-manager'); ?></h1>
                </div>

                <div class="content">
                    <p><?php printf(__('Il cliente %s ha risposto al preventivo #%d.', 'wc-pc-quote-manager'), esc_html($quote->customer_name), $quote->id); ?></p>

                    <div class="customer-message">
                        <h3><?php _e('Messaggio del Cliente:', 'wc-pc-quote-manager'); ?></h3>
                        <p><?php echo nl2br(esc_html($customer_message)); ?></p>
                    </div>

                    <div class="quote-details">
                        <h3><?php _e('Dettagli Preventivo', 'wc-pc-quote-manager'); ?></h3>
                        <p><strong><?php _e('Cliente:', 'wc-pc-quote-manager'); ?></strong> <?php echo esc_html($quote->customer_name); ?></p>
                        <p><strong><?php _e('Email:', 'wc-pc-quote-manager'); ?></strong> <a href="mailto:<?php echo esc_attr($quote->customer_email); ?>"><?php echo esc_html($quote->customer_email); ?></a></p>
                        <p><strong><?php _e('Tipologia PC:', 'wc-pc-quote-manager'); ?></strong> <?php echo esc_html($quote->pc_type); ?></p>
                        <p><strong><?php _e('Budget:', 'wc-pc-quote-manager'); ?></strong> <?php echo esc_html($quote->budget); ?></p>
                    </div>

                    <p>
                        <a href="<?php echo esc_url(admin_url('admin.php?page=wc-pc-quotes&action=view&quote_id=' . $quote->id)); ?>"
                           style="background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                            <?php _e('Visualizza e Rispondi', 'wc-pc-quote-manager'); ?>
                        </a>
                    </p>
                </div>

                <div class="footer">
                    <p><?php printf(__('Email inviata da %s', 'wc-pc-quote-manager'), get_bloginfo('name')); ?></p>
                </div>
            </div>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }
}
