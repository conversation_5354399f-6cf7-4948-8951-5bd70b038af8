<?php
/**
 * Plugin Name: Woo Custom Status Jojo
 * Plugin URI: 
 * Description: Plugin per la gestione di stati ordine personalizzati in WooCommerce
 * Version: 1.0.0
 * Author: 
 * Text Domain: woo-custom-status-jojo
 * Domain Path: /languages
 * Requires at least: 5.8
 * Requires PHP: 8.0
 * WC requires at least: 6.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Verifica che WooCommerce sia attivo
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
    add_action('admin_notices', function() {
        echo '<div class="error"><p>' . 
             __('Woo Custom Status Jojo richiede WooCommerce per funzionare.', 'woo-custom-status-jojo') . 
             '</p></div>';
    });
    return;
}

// Definizione costanti
define('WCST_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WCST_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WCST_VERSION', '1.0.0');

// Caricamento file delle classi
require_once WCST_PLUGIN_DIR . 'includes/class-woo-custom-status-admin.php';
require_once WCST_PLUGIN_DIR . 'includes/class-woo-custom-status-email.php';
require_once WCST_PLUGIN_DIR . 'includes/class-woo-custom-status-db.php';
require_once WCST_PLUGIN_DIR . 'includes/class-woo-custom-status-integration.php';

// Inizializzazione del plugin
function wcst_init() {
    load_plugin_textdomain('woo-custom-status-jojo', false, dirname(plugin_basename(__FILE__)) . '/languages');
    
    if (is_admin()) {
        new Woo_Custom_Status_Admin();
        new Woo_Custom_Status_Integration();
    }
}
add_action('plugins_loaded', 'wcst_init');

// Attivazione del plugin
register_activation_hook(__FILE__, 'wcst_activate');
function wcst_activate() {
    $db = new Woo_Custom_Status_DB();
    $db->create_tables();
} 