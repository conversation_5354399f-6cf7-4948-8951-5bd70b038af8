<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe per l'integrazione con la lista ordini WooCommerce
 */
class Email_Action_Buttons_Integration {
    
    private $db;
    
    public function __construct() {
        $this->db = new Email_Action_Buttons_DB();

        // Aggiungi i pulsanti alla lista ordini con PRIORITÀ MOLTO ALTA per evitare conflitti
        add_filter('woocommerce_admin_order_actions', array($this, 'add_order_actions'), 999, 2);

        // Aggiungi CSS per i pulsanti con priorità alta
        add_action('admin_head', array($this, 'add_order_actions_css'), 999);

        // CSS aggressivo rimosso - causava barre blu abnormi

        // Aggiungi messaggi di feedback nella lista ordini
        add_action('admin_notices', array($this, 'show_order_list_notices'));

        // Sistema rilevamento conflitti rimosso - non più necessario

        // Forza re-registrazione dei nostri hook dopo altri plugin
        add_action('wp_loaded', array($this, 'force_hook_priority'), 999);

        // Hook di emergenza per forzare i pulsanti se altri plugin li rimuovono
        add_action('current_screen', array($this, 'emergency_hook_registration'), 9999);

        // Pulizia una tantum delle opzioni debug
        add_action('admin_init', array($this, 'cleanup_debug_options'), 1);
    }
    
    /**
     * Aggiunge i pulsanti azione personalizzati alla lista ordini
     */
    public function add_order_actions($actions, $order) {
        if (!$order) {
            return $actions;
        }
        
        // Ottieni lo stato dell'ordine con prefisso wc-
        $order_status = 'wc-' . $order->get_status();
        
        // Ottieni i pulsanti per questo stato ordine
        $buttons = $this->db->get_buttons_for_status($order_status);
        
        if (empty($buttons)) {
            return $actions;
        }
        
        // STRATEGIA AGGRESSIVA: Assicurati che i nostri pulsanti siano sempre presenti
        foreach ($buttons as $button) {
            $action_key = 'weab_button_' . $button->id;

            // Rimuovi eventuali azioni esistenti con la stessa chiave per evitare duplicati
            if (isset($actions[$action_key])) {
                unset($actions[$action_key]);
            }

            // Aggiungi il nostro pulsante con priorità
            $actions[$action_key] = array(
                'url' => wp_nonce_url(
                    admin_url('admin-post.php?action=weab_send_email&order_id=' . $order->get_id() . '&button_id=' . $button->id),
                    'weab_send_email_' . $order->get_id() . '_' . $button->id,
                    'weab_nonce'
                ),
                'name' => esc_attr($button->button_name),
                'action' => $action_key,
                'priority' => 999 // Metadato per debug
            );
        }

        // OVERRIDE AGGRESSIVO: Se altri plugin hanno rimosso i nostri pulsanti, forzali
        $this->ensure_our_buttons_exist($actions, $buttons, $order);

        // Log per debug
        if (WP_DEBUG && WP_DEBUG_LOG) {
            $our_buttons = array_filter($actions, function($key) {
                return strpos($key, 'weab_button_') === 0;
            }, ARRAY_FILTER_USE_KEY);

            if (!empty($our_buttons)) {
                error_log('WEAB: Added ' . count($our_buttons) . ' buttons for order ' . $order->get_id());
            }
        }

        return $actions;
    }

    /**
     * Assicura che i nostri pulsanti esistano sempre nell'array azioni
     */
    private function ensure_our_buttons_exist(&$actions, $buttons, $order) {
        foreach ($buttons as $button) {
            $action_key = 'weab_button_' . $button->id;

            // Se il pulsante non esiste o è stato rimosso, forzalo
            if (!isset($actions[$action_key])) {
                $actions[$action_key] = array(
                    'url' => wp_nonce_url(
                        admin_url('admin-post.php?action=weab_send_email&order_id=' . $order->get_id() . '&button_id=' . $button->id),
                        'weab_send_email_' . $order->get_id() . '_' . $button->id,
                        'weab_nonce'
                    ),
                    'name' => esc_attr($button->button_name),
                    'action' => $action_key,
                    'forced' => true // Flag per debug
                );

                // Log del forcing
                if (WP_DEBUG && WP_DEBUG_LOG) {
                    error_log("WEAB: Forced button '{$button->button_name}' for order {$order->get_id()}");
                }
            }
        }
    }

    /**
     * Aggiunge CSS per i pulsanti personalizzati
     */
    public function add_order_actions_css() {
        global $current_screen;
        
        if (!$current_screen || $current_screen->id !== 'edit-shop_order') {
            return;
        }
        
        // Ottieni tutti i pulsanti per generare CSS dinamico
        $buttons = $this->db->get_all_buttons();
        
        if (empty($buttons)) {
            return;
        }
        
        echo '<style type="text/css">';
        
        foreach ($buttons as $button) {
            $action_key = 'weab_button_' . $button->id;
            $unicode_input = !empty($button->dashicon_code) ? $button->dashicon_code : '\f465';

            // Se l'input è un codice unicode (con backslash singolo o doppio) - supporta 3 o 4 caratteri
            if (preg_match('/^\\\\{1,2}f[0-9a-fA-F]{3,4}$/i', $unicode_input)) {
                // Rimuovi tutti i backslash iniziali per ottenere il codice pulito
                $unicode = preg_replace('/^\\\\+/', '', $unicode_input);
            } else if (preg_match('/^f[0-9a-fA-F]{3,4}$/i', $unicode_input)) {
                // Se inizia con 'f' ma senza backslash
                $unicode = $unicode_input;
            } else if (preg_match('/^[0-9a-fA-F]{3,4}$/i', $unicode_input)) {
                // Se è solo il codice hex senza 'f', aggiungilo
                $unicode = 'f' . $unicode_input;
            } else {
                // Fallback: prova a convertire da nome dashicon (per compatibilità)
                $dashicon_name = str_replace('dashicons-', '', $unicode_input);
                $unicode = $this->get_dashicon_unicode($dashicon_name);
            }

            echo ".wc-action-button-{$action_key}::after {
                font-family: Dashicons !important;
                content: '\\{$unicode}' !important;
            }\n";

            // Debug: aggiungi commento CSS con informazioni
            echo "/* Button ID: {$button->id}, Input: '{$unicode_input}', Unicode: '{$unicode}' */\n";
        }
        
        echo '</style>';
    }

    // Metodo add_priority_css rimosso - causava barre blu abnormi

    // Metodi di rilevamento conflitti rimossi - sistema debug non più necessario

    /**
     * Pulizia una tantum delle opzioni debug non più necessarie
     */
    public function cleanup_debug_options() {
        // Rimuovi opzioni debug se esistono
        if (get_option('weab_detected_conflicts')) {
            delete_option('weab_detected_conflicts');
        }

        // Rimuovi questo hook dopo la prima esecuzione
        remove_action('admin_init', array($this, 'cleanup_debug_options'), 1);
    }

    /**
     * Forza la priorità dei nostri hook dopo che altri plugin si sono caricati
     */
    public function force_hook_priority() {
        // Rimuovi e ri-aggiungi il nostro hook con priorità massima
        remove_filter('woocommerce_admin_order_actions', array($this, 'add_order_actions'), 999);
        add_filter('woocommerce_admin_order_actions', array($this, 'add_order_actions'), 9999, 2);

        // Log dell'operazione
        if (WP_DEBUG && WP_DEBUG_LOG) {
            error_log('WooCommerce Email Action Buttons - Hook priority forced to 9999');
        }
    }

    /**
     * Hook di emergenza per forzare la registrazione dei pulsanti
     */
    public function emergency_hook_registration() {
        $screen = get_current_screen();

        // Solo nella pagina lista ordini
        if (!$screen || $screen->id !== 'edit-shop_order') {
            return;
        }

        // Verifica se il nostro hook è ancora registrato
        global $wp_filter;
        $hook_name = 'woocommerce_admin_order_actions';
        $our_hook_found = false;

        if (isset($wp_filter[$hook_name])) {
            foreach ($wp_filter[$hook_name]->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) &&
                        is_object($callback['function'][0]) &&
                        get_class($callback['function'][0]) === 'Email_Action_Buttons_Integration') {
                        $our_hook_found = true;
                        break 2;
                    }
                }
            }
        }

        // Se il nostro hook non è stato trovato, registralo di emergenza
        if (!$our_hook_found) {
            add_filter('woocommerce_admin_order_actions', array($this, 'add_order_actions'), 99999, 2);

            if (WP_DEBUG && WP_DEBUG_LOG) {
                error_log('WEAB EMERGENCY: Re-registered hook with priority 99999');
            }
        }

        // JavaScript aggressivo rimosso - non necessario con priorità hook corretta
    }

    // Metodo add_emergency_javascript rimosso - non necessario

    /**
     * Converte il nome dashicon in unicode
     */
    private function get_dashicon_unicode($dashicon_name) {
        $dashicons = array(
            // Email e comunicazione
            'email' => 'f465',
            'email-alt' => 'f466',
            'email-alt2' => 'f467',
            'admin-comments' => 'f117',
            'format-chat' => 'f125',
            'testimonial' => 'f473',
            'megaphone' => 'f488',
            'bell' => 'f16d',
            'phone' => 'f525',

            // Azioni comuni
            'yes' => 'f147',
            'yes-alt' => 'f502',
            'no' => 'f158',
            'no-alt' => 'f335',
            'plus' => 'f132',
            'plus-alt' => 'f502',
            'minus' => 'f460',
            'edit' => 'f464',
            'trash' => 'f182',
            'update' => 'f463',
            'saved' => 'f147',

            // Stato e notifiche
            'warning' => 'f534',
            'info' => 'f348',
            'dismiss' => 'f153',
            'marker' => 'f159',
            'star-filled' => 'f155',
            'star-half' => 'f459',
            'star-empty' => 'f154',
            'flag' => 'f227',

            // Media e file
            'paperclip' => 'f546',
            'download' => 'f316',
            'upload' => 'f317',
            'media-archive' => 'f501',
            'media-audio' => 'f127',
            'media-code' => 'f499',
            'media-default' => 'f116',
            'media-document' => 'f497',
            'media-interactive' => 'f490',
            'media-spreadsheet' => 'f495',
            'media-text' => 'f491',
            'media-video' => 'f126',

            // Condivisione e social
            'share' => 'f237',
            'share-alt' => 'f240',
            'share-alt2' => 'f242',
            'twitter' => 'f301',
            'facebook' => 'f304',
            'facebook-alt' => 'f305',
            'googleplus' => 'f462',
            'linkedin' => 'f207',
            'pinterest' => 'f209',
            'youtube' => 'f213',
            'instagram' => 'f12d',
            'whatsapp' => 'f12a',

            // Navigazione
            'external' => 'f504',
            'arrow-up' => 'f142',
            'arrow-down' => 'f140',
            'arrow-left' => 'f141',
            'arrow-right' => 'f139',
            'arrow-up-alt' => 'f342',
            'arrow-down-alt' => 'f346',
            'arrow-left-alt' => 'f340',
            'arrow-right-alt' => 'f344',

            // Admin
            'admin-appearance' => 'f100',
            'admin-collapse' => 'f148',
            'admin-customizer' => 'f540',
            'admin-generic' => 'f111',
            'admin-home' => 'f102',
            'admin-media' => 'f104',
            'admin-multisite' => 'f541',
            'admin-network' => 'f112',
            'admin-page' => 'f105',
            'admin-plugins' => 'f106',
            'admin-post' => 'f109',
            'admin-settings' => 'f108',
            'admin-site' => 'f319',
            'admin-site-alt' => 'f11d',
            'admin-site-alt2' => 'f11e',
            'admin-site-alt3' => 'f11f',
            'admin-tools' => 'f107',
            'admin-users' => 'f110',

            // E-commerce
            'cart' => 'f174',
            'products' => 'f312',
            'money' => 'f526',
            'money-alt' => 'f18e',
            'businessman' => 'f12e',
            'id' => 'f336',
            'id-alt' => 'f337',
            'store' => 'f513',
            'tag' => 'f323',
            'tickets' => 'f486',
            'tickets-alt' => 'f524',

            // Tempo e calendario
            'calendar' => 'f145',
            'calendar-alt' => 'f508',
            'clock' => 'f469',
            'hourglass' => 'f18c',

            // Strumenti
            'hammer' => 'f308',
            'art' => 'f309',
            'building' => 'f512',
            'camera' => 'f306',
            'camera-alt' => 'f129',
            'carrot' => 'f511',
            'chart-area' => 'f239',
            'chart-bar' => 'f185',
            'chart-line' => 'f238',
            'chart-pie' => 'f184',
            'clipboard' => 'f481',
            'cloud' => 'f176',
            'desktop' => 'f472',
            'laptop' => 'f547',
            'smartphone' => 'f470',
            'tablet' => 'f471'
        );

        return isset($dashicons[$dashicon_name]) ? $dashicons[$dashicon_name] : 'f465'; // default email
    }

    /**
     * Ottieni tutte le icone disponibili con le loro descrizioni
     */
    public static function get_available_dashicons() {
        return array(
            // Email e comunicazione
            'email' => 'Email',
            'email-alt' => 'Email alternativa',
            'email-alt2' => 'Email alternativa 2',
            'admin-comments' => 'Commenti',
            'format-chat' => 'Chat',
            'testimonial' => 'Testimonianza',
            'megaphone' => 'Megafono',
            'bell' => 'Campanella',
            'phone' => 'Telefono',

            // Azioni comuni
            'yes' => 'Sì / Conferma',
            'yes-alt' => 'Sì alternativo',
            'no' => 'No / Rifiuta',
            'no-alt' => 'No alternativo',
            'plus' => 'Più / Aggiungi',
            'plus-alt' => 'Più alternativo',
            'minus' => 'Meno / Rimuovi',
            'edit' => 'Modifica',
            'trash' => 'Elimina',
            'update' => 'Aggiorna',
            'saved' => 'Salvato',

            // Stato e notifiche
            'warning' => 'Avviso',
            'info' => 'Informazione',
            'dismiss' => 'Chiudi',
            'marker' => 'Marcatore',
            'star-filled' => 'Stella piena',
            'star-half' => 'Mezza stella',
            'star-empty' => 'Stella vuota',
            'flag' => 'Bandiera',

            // Media e file
            'paperclip' => 'Allegato',
            'download' => 'Scarica',
            'upload' => 'Carica',
            'media-archive' => 'Archivio',
            'media-audio' => 'Audio',
            'media-document' => 'Documento',
            'media-video' => 'Video',

            // E-commerce
            'cart' => 'Carrello',
            'products' => 'Prodotti',
            'money' => 'Denaro',
            'money-alt' => 'Denaro alternativo',
            'businessman' => 'Uomo d\'affari',
            'store' => 'Negozio',
            'tag' => 'Tag',
            'tickets' => 'Biglietti',

            // Tempo
            'calendar' => 'Calendario',
            'calendar-alt' => 'Calendario alternativo',
            'clock' => 'Orologio',
            'hourglass' => 'Clessidra',

            // Navigazione
            'external' => 'Esterno',
            'arrow-up' => 'Freccia su',
            'arrow-down' => 'Freccia giù',
            'arrow-left' => 'Freccia sinistra',
            'arrow-right' => 'Freccia destra',

            // Social
            'share' => 'Condividi',
            'twitter' => 'Twitter',
            'facebook' => 'Facebook',
            'linkedin' => 'LinkedIn',
            'youtube' => 'YouTube',
            'instagram' => 'Instagram'
        );
    }
    
    /**
     * Mostra messaggi di feedback nella lista ordini
     */
    public function show_order_list_notices() {
        global $current_screen;
        
        if (!$current_screen || $current_screen->id !== 'edit-shop_order') {
            return;
        }
        
        $message = isset($_GET['message']) ? sanitize_text_field($_GET['message']) : '';
        $error = isset($_GET['error']) ? urldecode($_GET['error']) : '';
        
        if ($message) {
            $messages = array(
                'email_sent' => 'Email inviata con successo al cliente.',
            );
            
            if (isset($messages[$message])) {
                echo '<div class="notice notice-success is-dismissible"><p>' . $messages[$message] . '</p></div>';
            }
        }
        
        if ($error) {
            echo '<div class="notice notice-error is-dismissible"><p>' . $error . '</p></div>';
        }
    }
    
    /**
     * Ottieni informazioni sui pulsanti per debug
     */
    public function get_buttons_debug_info() {
        if (!current_user_can('manage_options')) {
            return array();
        }
        
        $buttons = $this->db->get_all_buttons();
        $debug_info = array();
        
        foreach ($buttons as $button) {
            $debug_info[] = array(
                'id' => $button->id,
                'name' => $button->button_name,
                'statuses' => $button->order_statuses,
                'dashicon' => $button->dashicon_code
            );
        }
        
        return $debug_info;
    }
    
    /**
     * Verifica se un ordine può ricevere email
     */
    private function can_send_email_to_order($order) {
        if (!$order) {
            return false;
        }
        
        // Verifica che l'ordine abbia un'email
        $email = $order->get_billing_email();
        if (empty($email) || !is_email($email)) {
            return false;
        }
        
        // Verifica che l'ordine non sia in stato draft o auto-draft
        $status = $order->get_status();
        if (in_array($status, array('draft', 'auto-draft'))) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Ottieni statistiche sui pulsanti utilizzati
     */
    public function get_button_usage_stats() {
        if (!current_user_can('manage_woocommerce')) {
            return array();
        }
        
        // Questa funzione potrebbe essere estesa per tracciare l'utilizzo dei pulsanti
        // Per ora restituisce informazioni di base
        $buttons = $this->db->get_all_buttons();
        $stats = array();
        
        foreach ($buttons as $button) {
            $stats[] = array(
                'button_name' => $button->button_name,
                'order_statuses_count' => is_array($button->order_statuses) ? count($button->order_statuses) : 0,
                'created_at' => $button->created_at
            );
        }
        
        return $stats;
    }
}
