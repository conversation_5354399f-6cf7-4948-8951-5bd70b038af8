<?php
if (!defined('ABSPATH')) {
    exit;
}

class Woo_Custom_Status_DB {
    private $status_table;
    private $email_table;

    public function __construct() {
        global $wpdb;
        $this->status_table = $wpdb->prefix . 'wcst_custom_statuses';
        $this->email_table = $wpdb->prefix . 'wcst_status_emails';
    }

    public function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        $sql_status = "CREATE TABLE IF NOT EXISTS {$this->status_table} (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            status_name varchar(100) NOT NULL,
            status_slug varchar(100) NOT NULL,
            background_color varchar(7) NOT NULL,
            text_color varchar(7) NOT NULL,
            show_action_button tinyint(1) DEFAULT 0,
            action_icon varchar(50),
            allowed_statuses text DEFAULT NULL,
            priority int(11) DEFAULT 300,
            PRIMARY KEY  (id),
            UNIQUE KEY status_slug (status_slug)
        ) $charset_collate;";

        $sql_email = "CREATE TABLE IF NOT EXISTS {$this->email_table} (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            status_id mediumint(9) NOT NULL,
            is_enabled tinyint(1) DEFAULT 0,
            email_to varchar(255),
            email_subject varchar(255),
            email_content text,
            PRIMARY KEY  (id),
            FOREIGN KEY (status_id) REFERENCES {$this->status_table}(id) ON DELETE CASCADE
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_status);
        dbDelta($sql_email);
    }

    public function get_all_statuses() {
        global $wpdb;
        return $wpdb->get_results("SELECT * FROM {$this->status_table} ORDER BY priority ASC");
    }

    public function get_status($id) {
        global $wpdb;
        return $wpdb->get_row($wpdb->prepare("SELECT * FROM {$this->status_table} WHERE id = %d", $id));
    }

    public function get_status_by_slug($slug) {
        global $wpdb;
        return $wpdb->get_row($wpdb->prepare("SELECT * FROM {$this->status_table} WHERE status_slug = %s", $slug));
    }

    public function add_status($data) {
        global $wpdb;
        $wpdb->insert($this->status_table, [
            'status_name' => sanitize_text_field($data['status_name']),
            'status_slug' => sanitize_title($data['status_slug']),
            'background_color' => sanitize_hex_color($data['background_color']),
            'text_color' => sanitize_hex_color($data['text_color']),
            'show_action_button' => isset($data['show_action_button']) ? 1 : 0,
            'action_icon' => sanitize_text_field($data['action_icon']),
            'allowed_statuses' => isset($data['allowed_statuses']) && is_array($data['allowed_statuses']) ? 
                sanitize_text_field(implode(',', $data['allowed_statuses'])) : '',
            'priority' => $this->get_next_priority()
        ]);
        return $wpdb->insert_id;
    }

    private function get_next_priority() {
        global $wpdb;
        $max_priority = $wpdb->get_var("SELECT MAX(priority) FROM {$this->status_table}");
        return $max_priority ? $max_priority + 1 : 300;
    }

    public function update_status($id, $data) {
        global $wpdb;
        return $wpdb->update($this->status_table, [
            'status_name' => sanitize_text_field($data['status_name']),
            'status_slug' => sanitize_title($data['status_slug']),
            'background_color' => sanitize_hex_color($data['background_color']),
            'text_color' => sanitize_hex_color($data['text_color']),
            'show_action_button' => isset($data['show_action_button']) ? 1 : 0,
            'action_icon' => sanitize_text_field($data['action_icon']),
            'allowed_statuses' => isset($data['allowed_statuses']) && is_array($data['allowed_statuses']) ? 
                sanitize_text_field(implode(',', $data['allowed_statuses'])) : ''
        ], ['id' => $id]);
    }

    public function delete_status($id) {
        global $wpdb;
        return $wpdb->delete($this->status_table, ['id' => $id]);
    }

    // Metodi per la gestione delle email
    public function get_status_email($status_id) {
        global $wpdb;
        return $wpdb->get_row($wpdb->prepare("SELECT * FROM {$this->email_table} WHERE status_id = %d", $status_id));
    }

    public function save_status_email($data) {
        global $wpdb;
        $existing = $this->get_status_email($data['status_id']);
        
        $email_data = [
            'status_id' => $data['status_id'],
            'is_enabled' => isset($data['is_enabled']) ? 1 : 0,
            'email_to' => sanitize_email($data['email_to']),
            'email_subject' => sanitize_text_field($data['email_subject']),
            'email_content' => wp_kses_post($data['email_content'])
        ];

        if ($existing) {
            return $wpdb->update($this->email_table, $email_data, ['status_id' => $data['status_id']]);
        } else {
            return $wpdb->insert($this->email_table, $email_data);
        }
    }
} 