<?php
/**
 * Template per l'email di testo semplice di Pagamento a Rate
 *
 * Questo template può essere sovrascritto nel tema copiandolo in 
 * {tema}/woocommerce/emails/plain/wpr-payment-email.php.
 */

defined('ABSPATH') || exit;

echo "=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=\n";
echo esc_html(wp_strip_all_tags($email_heading));
echo "\n=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=\n\n";

echo esc_html__('Gentile Cliente,', 'woo-pagamento-rate') . "\n\n";

echo esc_html__('grazie per aver scelto di acquistare presso di noi con la modalità di pagamento tramite finanziamento.', 'woo-pagamento-rate') . "\n\n";

echo esc_html__('Per procedere con la pratica, ti chiediamo cortesemente di rispondere direttamente a questa email o inviare un messaggio WhatsApp al numero [inserire numero WhatsApp] indicando le seguenti informazioni:', 'woo-pagamento-rate') . "\n\n";

echo "- " . esc_html__('Nome e Cognome', 'woo-pagamento-rate') . "\n";
echo "- " . esc_html__('Numero dell\'ordine', 'woo-pagamento-rate') . "\n";
echo "- " . esc_html__('Numero di rate desiderate (puoi scegliere tra 6, 12 o 24 rate)', 'woo-pagamento-rate') . "\n\n";

echo esc_html__('Inoltre, ti chiediamo gentilmente di allegare i seguenti documenti:', 'woo-pagamento-rate') . "\n\n";

echo "- " . esc_html__('Documento di Identità (fronte-retro)', 'woo-pagamento-rate') . "\n";
echo "- " . esc_html__('Codice Fiscale (fronte-retro)', 'woo-pagamento-rate') . "\n";
echo "- " . esc_html__('Ultima busta paga', 'woo-pagamento-rate') . "\n\n";

echo esc_html__('Una volta ricevuta tutta la documentazione, procederemo rapidamente con la verifica e ti invieremo conferma.', 'woo-pagamento-rate') . "\n\n";

echo esc_html__('Rimaniamo a disposizione per qualsiasi dubbio o chiarimento.', 'woo-pagamento-rate') . "\n\n";

echo esc_html__('Cordiali saluti', 'woo-pagamento-rate') . "\n\n";

echo "=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=\n";
echo esc_html__('Riepilogo del tuo ordine', 'woo-pagamento-rate') . "\n";
echo "=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=\n\n";

echo esc_html__('Ordine n. ', 'woo-pagamento-rate') . $order->get_order_number() . "\n\n";

echo esc_html__('Prodotti:', 'woo-pagamento-rate') . "\n";
foreach ($order->get_items() as $item_id => $item) {
	$product = $item->get_product();
	echo wp_kses_post(apply_filters('woocommerce_order_item_name', $item->get_name(), $item, false));
	echo ' X ' . esc_html($item->get_quantity());
	echo ' = ' . wp_kses_post($order->get_formatted_line_subtotal($item)) . "\n";
}

echo "\n";

if ($order->get_shipping_total() > 0) {
	echo esc_html__('Spedizione:', 'woo-pagamento-rate') . ' ' . wp_kses_post($order->get_shipping_total()) . "\n";
}

foreach ($order->get_tax_totals() as $code => $tax) {
	echo esc_html($tax->label) . ': ' . wp_kses_post(wc_price($tax->amount)) . "\n";
}

echo "\n" . esc_html__('Totale:', 'woo-pagamento-rate') . ' ' . wp_kses_post($order->get_formatted_order_total()) . "\n\n";

if ($additional_content) {
	echo esc_html(wp_strip_all_tags(wptexturize($additional_content)));
	echo "\n\n----------------------------------------\n\n";
}

echo wp_kses_post(apply_filters('woocommerce_email_footer_text', get_option('woocommerce_email_footer_text'))); 