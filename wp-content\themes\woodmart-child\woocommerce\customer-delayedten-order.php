<?php

/**

 * Customer completed order email

 *

 * This template can be overridden by copying it to yourtheme/woocommerce/emails/customer-completed-order.php.

 *

 * HOWEVER, on occasion WooCommerce will need to update template files and you

 * (the theme developer) will need to copy the new files to your theme to

 * maintain compatibility. We try to do this as little as possible, but it does

 * happen. When this occurs the version of the template file will be bumped and

 * the readme will list any important changes.

 *

 * @see https://docs.woocommerce.com/document/template-structure/

 * @package WooCommerce/Templates/Emails

 * @version 3.7.0

 */



if ( ! defined( 'ABSPATH' ) ) {

	exit;

}

	$ship = wc_get_order($order);

	$ship->get_shipping_methods();

/*

 * @hooked WC_Emails::email_header() Output the email header

 */

do_action( 'woocommerce_email_header', $email_heading, $email ); ?>



<?php /* translators: %s: Customer first name */ ?>

<p><?php printf( esc_html__( 'Gentile Cliente %s,', 'woocommerce' ), esc_html( $order->get_billing_first_name() ) ); ?></p>



<p>La ringraziamo per aver scelto Centro Telefonia Noli.</p>

<p>Desideriamo informarla che il Suo ordine è attualmente in lavorazione. Al momento, a causa della elevata richiesta per il prodotto da Lei ordinato non possiamo stimare una data precisa di consegna.

<p>Stiamo procedendo con le consegne seguendo la data di arrivo dell ordine.</p>

<p>Comprendiamo la Sua attesa e Le assicuriamo che stiamo monitorando costantemente il suo ordine. Non appena avremo maggiori informazioni sulla data di consegna, sarà nostra premura aggiornarla con una nuova comunicazione.</p>

<p>Cordiali Saluti</p>

<p>Centro Telefonia Noli</p>





<?php



/*

 * @hooked WC_Emails::order_details() Shows the order details table.

 * @hooked WC_Structured_Data::generate_order_data() Generates structured data.

 * @hooked WC_Structured_Data::output_structured_data() Outputs structured data.

 * @since 2.5.0

 */

do_action( 'woocommerce_email_order_details', $order, $sent_to_admin, $plain_text, $email );



/*

 * @hooked WC_Emails::order_meta() Shows order meta data.

 */

do_action( 'woocommerce_email_order_meta', $order, $sent_to_admin, $plain_text, $email );



/*

 * @hooked WC_Emails::customer_details() Shows customer details

 * @hooked WC_Emails::email_address() Shows email address

 */

do_action( 'woocommerce_email_customer_details', $order, $sent_to_admin, $plain_text, $email );



/**

 * Show user-defined additional content - this is set in each email's settings.

 */



/*

 * @hooked WC_Emails::email_footer() Output the email footer

 */

do_action( 'woocommerce_email_footer', $email );