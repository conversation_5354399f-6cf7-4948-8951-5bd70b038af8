# WooCommerce Pulsanti Status Ordine

Un plugin WordPress per WooCommerce che aggiunge pulsanti di azione personalizzati alla lista ordini dell'admin per aggiornare lo status degli ordini e inviare email ai clienti.

## Caratteristiche

- **Interfaccia Admin Completa**: Gestione CRUD completa per i pulsanti personalizzati
- **Integrazione WooCommerce**: I pulsanti appaiono direttamente nella lista ordini dell'admin
- **Aggiornamento Status**: Ogni pulsante può cambiare lo status di un ordine
- **Email Personalizzate**: Invio automatico di email ai clienti usando i template WooCommerce
- **Filtri Status**: I pulsanti appaiono solo per gli status ordine specificati
- **Icone Personalizzate**: Supporto per Dashicons WordPress
- **Interfaccia Italiana**: Tutte le etichette e messaggi in italiano
- **Responsive**: Interfaccia ottimizzata per dispositivi mobili

## Requisiti

- WordPress 5.8 o superiore
- WooCommerce 6.0 o superiore
- PHP 8.0 o superiore

## Installazione

1. Carica la cartella del plugin in `/wp-content/plugins/`
2. Attiva il plugin dal menu 'Plugin' di WordPress
3. Vai su WooCommerce > Pulsanti Status Ordine per configurare i pulsanti

## Utilizzo

### Creazione di un Pulsante

1. Vai su **WooCommerce > Pulsanti Status Ordine**
2. Clicca su **Aggiungi Nuovo Pulsante**
3. Compila i campi richiesti:
   - **Nome Pulsante**: Il testo che apparirà sul pulsante
   - **Descrizione**: Note interne (opzionale)
   - **Status degli Ordini**: Seleziona per quali status il pulsante deve apparire
   - **Status di Destinazione**: Lo status a cui l'ordine verrà cambiato
   - **Codice Unicode Dashicon**: Icona del pulsante (es. \f147)
   - **Testo Email**: Contenuto dell'email da inviare al cliente

### Placeholder Email Disponibili

Nel contenuto dell'email puoi utilizzare questi placeholder:

- `{order_number}` - Numero ordine
- `{customer_name}` - Nome completo cliente
- `{customer_first_name}` - Nome cliente
- `{customer_last_name}` - Cognome cliente
- `{order_date}` - Data ordine
- `{order_total}` - Totale ordine
- `{order_status}` - Status attuale ordine
- `{new_status}` - Nuovo status ordine
- `{site_name}` - Nome sito
- `{site_url}` - URL sito
- `{order_details}` - Dettagli completi ordine (tabella HTML)
- `{billing_address}` - Indirizzo fatturazione
- `{shipping_address}` - Indirizzo spedizione

### Utilizzo nella Lista Ordini

1. Vai su **WooCommerce > Ordini**
2. I pulsanti personalizzati appariranno nella colonna "Azioni Status"
3. Clicca su un pulsante per:
   - Aggiornare lo status dell'ordine
   - Inviare automaticamente un'email al cliente
   - Aggiungere una nota all'ordine

## Struttura File

```
woo-order-status-buttons/
├── woo-order-status-buttons.php     # File principale del plugin
├── includes/
│   ├── class-order-status-buttons-db.php          # Gestione database
│   ├── class-order-status-buttons-admin.php       # Interfaccia admin
│   ├── class-order-status-buttons-email.php       # Gestione email
│   └── class-order-status-buttons-integration.php # Integrazione WooCommerce
├── templates/
│   ├── admin-buttons-list.php       # Template lista pulsanti
│   └── admin-button-form.php        # Template form pulsante
├── assets/
│   ├── css/
│   │   └── admin.css                # Stili admin
│   └── js/
│       └── admin.js                 # JavaScript admin
├── uninstall.php                    # Script disinstallazione
└── README.md                        # Documentazione
```

## Sicurezza

Il plugin implementa le migliori pratiche di sicurezza WordPress:

- **Nonce Verification**: Tutti i form utilizzano nonce per prevenire CSRF
- **Capability Checks**: Verifica dei permessi utente per tutte le azioni
- **Data Sanitization**: Sanitizzazione di tutti gli input utente
- **SQL Injection Prevention**: Uso di prepared statements
- **XSS Protection**: Escape di tutti gli output

## Compatibilità

- **HPOS**: Supporto completo per High-Performance Order Storage
- **Multisite**: Compatibile con installazioni WordPress multisite
- **Mobile**: Interfaccia responsive per dispositivi mobili
- **Accessibility**: Supporto per screen reader e navigazione da tastiera

## Personalizzazione

### Hook Disponibili

```php
// Personalizza l'oggetto dell'email
add_filter('wosb_email_subject', function($subject, $order, $button) {
    return 'Oggetto personalizzato: ' . $subject;
}, 10, 3);
```

### CSS Personalizzato

Puoi personalizzare l'aspetto dei pulsanti aggiungendo CSS personalizzato:

```css
.wosb-action-button {
    background: #your-color !important;
}
```

## Troubleshooting

### I pulsanti non appaiono nella lista ordini

1. Verifica che il pulsante sia configurato per lo status corretto dell'ordine
2. Controlla che l'utente abbia i permessi `manage_woocommerce`
3. Assicurati che WooCommerce sia attivo e aggiornato

### Le email non vengono inviate

1. Verifica la configurazione email di WordPress
2. Controlla che l'ordine abbia un indirizzo email valido
3. Verifica i log degli errori per messaggi specifici

### Errori di database

1. Disattiva e riattiva il plugin per ricreare le tabelle
2. Verifica i permessi del database
3. Controlla i log degli errori WordPress

## Changelog

### 1.0.0
- Rilascio iniziale
- Gestione CRUD completa per pulsanti personalizzati
- Integrazione con lista ordini WooCommerce
- Sistema email con template WooCommerce
- Interfaccia admin in italiano
- Supporto per Dashicons
- Validazione e sicurezza complete

## Supporto

Per supporto e segnalazione bug, contatta lo sviluppatore.

## Licenza

Questo plugin è rilasciato sotto licenza GPL v2 o successiva.
