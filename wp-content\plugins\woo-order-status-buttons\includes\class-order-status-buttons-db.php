<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe per la gestione del database dei pulsanti status ordine
 */
class Order_Status_Buttons_DB {
    
    private $table_name;
    
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'woo_order_status_buttons';
    }
    
    /**
     * Crea le tabelle del database
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE {$this->table_name} (
            id int(11) NOT NULL AUTO_INCREMENT,
            button_name varchar(255) NOT NULL,
            button_description text,
            order_statuses longtext,
            target_status varchar(50) NOT NULL,
            email_content longtext,
            dashicon_code varchar(100) DEFAULT '\f147',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Verifica se la tabella è stata creata correttamente
        if ($wpdb->get_var("SHOW TABLES LIKE '{$this->table_name}'") != $this->table_name) {
            wosb_log('Errore nella creazione della tabella: ' . $this->table_name, 'error');
        }
    }
    
    /**
     * Inserisce un nuovo pulsante
     */
    public function insert_button($data) {
        global $wpdb;
        
        $result = $wpdb->insert(
            $this->table_name,
            array(
                'button_name' => wosb_sanitize_input($data['button_name']),
                'button_description' => wosb_sanitize_input($data['button_description'], 'textarea'),
                'order_statuses' => maybe_serialize($data['order_statuses']),
                'target_status' => wosb_sanitize_input($data['target_status']),
                'email_content' => wosb_sanitize_input($data['email_content'], 'html'),
                'dashicon_code' => wosb_sanitize_input($data['dashicon_code'])
            ),
            array('%s', '%s', '%s', '%s', '%s', '%s')
        );
        
        if ($result === false) {
            wosb_log('Errore nell\'inserimento del pulsante: ' . $wpdb->last_error, 'error');
            return false;
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Aggiorna un pulsante esistente
     */
    public function update_button($id, $data) {
        global $wpdb;
        
        $result = $wpdb->update(
            $this->table_name,
            array(
                'button_name' => wosb_sanitize_input($data['button_name']),
                'button_description' => wosb_sanitize_input($data['button_description'], 'textarea'),
                'order_statuses' => maybe_serialize($data['order_statuses']),
                'target_status' => wosb_sanitize_input($data['target_status']),
                'email_content' => wosb_sanitize_input($data['email_content'], 'html'),
                'dashicon_code' => wosb_sanitize_input($data['dashicon_code'])
            ),
            array('id' => intval($id)),
            array('%s', '%s', '%s', '%s', '%s', '%s'),
            array('%d')
        );
        
        if ($result === false) {
            wosb_log('Errore nell\'aggiornamento del pulsante: ' . $wpdb->last_error, 'error');
            return false;
        }
        
        return $result;
    }
    
    /**
     * Elimina un pulsante
     */
    public function delete_button($id) {
        global $wpdb;
        
        $result = $wpdb->delete(
            $this->table_name,
            array('id' => intval($id)),
            array('%d')
        );
        
        if ($result === false) {
            wosb_log('Errore nell\'eliminazione del pulsante: ' . $wpdb->last_error, 'error');
            return false;
        }
        
        return $result;
    }
    
    /**
     * Ottiene un pulsante per ID
     */
    public function get_button($id) {
        global $wpdb;
        
        $button = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$this->table_name} WHERE id = %d", intval($id))
        );
        
        if ($button) {
            $button->order_statuses = maybe_unserialize($button->order_statuses);
        }
        
        return $button;
    }
    
    /**
     * Ottiene tutti i pulsanti
     */
    public function get_all_buttons() {
        global $wpdb;
        
        $buttons = $wpdb->get_results("SELECT * FROM {$this->table_name} ORDER BY created_at DESC");
        
        if ($buttons) {
            foreach ($buttons as $button) {
                $button->order_statuses = maybe_unserialize($button->order_statuses);
            }
        }
        
        return $buttons;
    }
    
    /**
     * Ottiene i pulsanti per uno specifico status ordine
     */
    public function get_buttons_for_status($order_status) {
        global $wpdb;

        $buttons = $wpdb->get_results("SELECT * FROM {$this->table_name} ORDER BY created_at DESC");
        $filtered_buttons = array();

        wosb_log("Cercando pulsanti per status: " . $order_status);
        wosb_log("Pulsanti totali nel database: " . count($buttons));

        if ($buttons) {
            foreach ($buttons as $button) {
                $button->order_statuses = maybe_unserialize($button->order_statuses);

                wosb_log("Pulsante: " . $button->button_name . ", Status configurati: " . print_r($button->order_statuses, true));

                // Verifica se questo pulsante è applicabile per lo status dell'ordine
                if (is_array($button->order_statuses) && in_array($order_status, $button->order_statuses)) {
                    $filtered_buttons[] = $button;
                    wosb_log("Pulsante " . $button->button_name . " MATCH per status " . $order_status);
                } else {
                    wosb_log("Pulsante " . $button->button_name . " NO MATCH per status " . $order_status);
                }
            }
        }

        wosb_log("Pulsanti filtrati: " . count($filtered_buttons));
        return $filtered_buttons;
    }
    
    /**
     * Conta il numero totale di pulsanti
     */
    public function count_buttons() {
        global $wpdb;
        
        return $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name}");
    }
    
    /**
     * Verifica se la tabella esiste
     */
    public function table_exists() {
        global $wpdb;
        
        return $wpdb->get_var("SHOW TABLES LIKE '{$this->table_name}'") == $this->table_name;
    }
    
    /**
     * Elimina la tabella (per uninstall)
     */
    public function drop_table() {
        global $wpdb;
        
        $wpdb->query("DROP TABLE IF EXISTS {$this->table_name}");
    }
    
    /**
     * Ottiene statistiche sui pulsanti
     */
    public function get_button_stats() {
        global $wpdb;
        
        $stats = array();
        $stats['total'] = $this->count_buttons();
        $stats['by_target_status'] = $wpdb->get_results(
            "SELECT target_status, COUNT(*) as count FROM {$this->table_name} GROUP BY target_status"
        );
        
        return $stats;
    }
}
