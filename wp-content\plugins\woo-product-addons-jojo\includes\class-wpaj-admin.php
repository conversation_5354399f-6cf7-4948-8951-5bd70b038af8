<?php
/**
 * Classe per la gestione della parte amministrativa del plugin
 *
 * @package Woo_Product_Addons_Jojo
 * @since 1.0.0
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe WPAJ_Admin
 */
class WPAJ_Admin {

    /**
     * Costruttore
     */
    public function __construct() {
        // Aggiungi un nuovo tab per i prodotti accessori
        add_filter('woocommerce_product_data_tabs', array($this, 'add_product_accessories_tab'));
        
        // Aggiungi i campi personalizzati nel nuovo tab
        add_action('woocommerce_product_data_panels', array($this, 'add_product_accessories_tab_content'));
        
        // Salva i dati dei campi personalizzati
        add_action('woocommerce_process_product_meta', array($this, 'save_product_accessories_field'));
        
        // Aggiungi script e stili per l'admin
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Aggiungi endpoint AJAX per la ricerca dei prodotti
        add_action('wp_ajax_wpaj_search_products', array($this, 'ajax_search_products'));
    }

    /**
     * Aggiunge un nuovo tab per i prodotti accessori
     *
     * @param array $tabs Array dei tab esistenti
     * @return array Array dei tab modificato
     */
    public function add_product_accessories_tab($tabs) {
        $tabs['product_accessories'] = array(
            'label'    => __('Prodotti Accessori', 'woo-product-addons-jojo'),
            'target'   => 'product_accessories_data',
            'class'    => array(),
            'priority' => 80,
        );
        
        return $tabs;
    }

    /**
     * Aggiunge il contenuto del tab per i prodotti accessori
     */
    public function add_product_accessories_tab_content() {
        global $post;
        
        echo '<div id="product_accessories_data" class="panel woocommerce_options_panel">';
        
        // Campo nascosto per memorizzare gli ID dei prodotti accessori
        woocommerce_wp_hidden_input(
            array(
                'id'    => '_product_accessories',
                'value' => get_post_meta($post->ID, '_product_accessories', true),
            )
        );
        
        // Campo di ricerca per i prodotti accessori
        echo '<p class="form-field">';
        echo '<label for="product_accessories_search">' . __('Cerca Prodotti', 'woo-product-addons-jojo') . '</label>';
        echo '<input type="text" class="short" id="product_accessories_search" placeholder="' . __('Cerca prodotti...', 'woo-product-addons-jojo') . '">';
        echo '<span class="description">' . __('Cerca e seleziona i prodotti da offrire come accessori.', 'woo-product-addons-jojo') . '</span>';
        echo '</p>';
        
        // Contenitore per i prodotti accessori selezionati
        echo '<div class="form-field">';
        echo '<label>' . __('Prodotti Selezionati', 'woo-product-addons-jojo') . '</label>';
        echo '<div id="product_accessories_container" class="product_accessories_container">';
        
        // Carica i prodotti accessori già selezionati
        $accessories_ids = get_post_meta($post->ID, '_product_accessories', true);
        if (!empty($accessories_ids)) {
            $accessories_ids = explode(',', $accessories_ids);
            foreach ($accessories_ids as $product_id) {
                $product = wc_get_product($product_id);
                if ($product) {
                    $this->render_selected_product($product);
                }
            }
        }
        
        echo '</div>';
        echo '</div>';
        
        echo '</div>';
    }

    /**
     * Renderizza un prodotto selezionato nella lista degli accessori
     *
     * @param WC_Product $product Prodotto da renderizzare
     */
    private function render_selected_product($product) {
        $product_id = $product->get_id();
        $product_name = $product->get_name();
        $product_price = wc_price($product->get_price());
        $product_image = $product->get_image(array(50, 50));
        
        echo '<div class="selected-product" data-product-id="' . esc_attr($product_id) . '">';
        echo '<div class="product-image">' . $product_image . '</div>';
        echo '<div class="product-info">';
        echo '<span class="product-name">' . esc_html($product_name) . '</span>';
        echo '<span class="product-price">' . $product_price . '</span>';
        echo '</div>';
        echo '<a href="#" class="remove-product">&times;</a>';
        echo '</div>';
    }

    /**
     * Salva i dati dei prodotti accessori
     *
     * @param int $post_id ID del prodotto
     */
    public function save_product_accessories_field($post_id) {
        $accessories = isset($_POST['_product_accessories']) ? sanitize_text_field($_POST['_product_accessories']) : '';
        update_post_meta($post_id, '_product_accessories', $accessories);
    }

    /**
     * Carica gli script e gli stili per l'admin
     *
     * @param string $hook Hook corrente
     */
    public function enqueue_admin_scripts($hook) {
        // Carica gli script solo nella pagina di modifica del prodotto
        if ('post.php' !== $hook && 'post-new.php' !== $hook) {
            return;
        }
        
        global $post;
        if (!$post || 'product' !== $post->post_type) {
            return;
        }
        
        // Registra e carica lo stile CSS
        wp_register_style(
            'wpaj-admin-style',
            WPAJ_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            WPAJ_PLUGIN_VERSION
        );
        wp_enqueue_style('wpaj-admin-style');
        
        // Registra e carica lo script JS
        wp_register_script(
            'wpaj-admin-script',
            WPAJ_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'jquery-ui-autocomplete'),
            WPAJ_PLUGIN_VERSION,
            true
        );
        
        // Passa i dati allo script
        wp_localize_script(
            'wpaj-admin-script',
            'wpaj_admin_params',
            array(
                'ajax_url'   => admin_url('admin-ajax.php'),
                'nonce'      => wp_create_nonce('wpaj_search_products_nonce'),
                'i18n'       => array(
                    'error'   => __('Si è verificato un errore. Riprova.', 'woo-product-addons-jojo'),
                    'no_products' => __('Nessun prodotto trovato.', 'woo-product-addons-jojo'),
                ),
            )
        );
        
        wp_enqueue_script('wpaj-admin-script');
    }

    /**
     * Gestisce la richiesta AJAX per la ricerca dei prodotti
     */
    public function ajax_search_products() {
        // Verifica il nonce
        check_ajax_referer('wpaj_search_products_nonce', 'nonce');
        
        // Verifica i permessi
        if (!current_user_can('edit_products')) {
            wp_die(-1);
        }
        
        $term = isset($_GET['term']) ? sanitize_text_field($_GET['term']) : '';
        
        if (empty($term)) {
            wp_die();
        }
        
        $args = array(
            'post_type'      => 'product',
            'post_status'    => 'publish',
            'posts_per_page' => 10,
            's'              => $term,
        );
        
        $products = array();
        $query = new WP_Query($args);
        
        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $product = wc_get_product(get_the_ID());
                
                if (!$product) {
                    continue;
                }
                
                $products[] = array(
                    'id'    => $product->get_id(),
                    'label' => $product->get_name(),
                    'value' => $product->get_name(),
                    'price' => $product->get_price_html(),
                    'image' => $product->get_image(array(50, 50)),
                );
            }
        }
        
        wp_reset_postdata();
        
        wp_send_json($products);
    }
} 