<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe per l'integrazione con WooCommerce
 */
class Order_Status_Buttons_Integration {
    
    private $db;
    
    public function __construct() {
        $this->db = new Order_Status_Buttons_DB();

        // Registra gli hook immediatamente - non aspettare admin_init
        $this->setup_hooks();

        // Gestisci le azioni dei pulsanti
        add_action('admin_post_wosb_button_action', array($this, 'handle_button_action'));



        // Aggiungi CSS per i pulsanti
        add_action('admin_head', array($this, 'add_button_styles'));

        // Aggiungi JavaScript per conferma azioni
        add_action('admin_footer', array($this, 'add_button_scripts'));
    }

    /**
     * Setup degli hook in base al sistema utilizzato
     */
    public function setup_hooks() {
        wosb_log("Setup hooks chiamato");

        // Per il sistema legacy (HPOS disabilitato), usa sempre questi hook
        wosb_log("Registrando hook legacy per shop_order");

        // Invece di creare una nuova colonna, aggiungiamo i pulsanti alla colonna azioni esistente
        // Hook legacy per post type shop_order
        add_action('manage_shop_order_posts_custom_column', array($this, 'add_buttons_to_actions_column'), 20, 2);

        // Hook per edit.php?post_type=shop_order
        add_action('manage_shop_order_posts_custom_column', array($this, 'add_buttons_to_actions_column'), 20, 2);

        // Hook aggiuntivi per compatibilità con versioni diverse di WooCommerce
        add_action('manage_woocommerce_page_wc-orders_custom_column', array($this, 'add_buttons_to_actions_column_hpos'), 20, 2);

        // Hook per HPOS se disponibile
        if (class_exists('Automattic\WooCommerce\Utilities\OrderUtil')) {
            add_action('woocommerce_shop_order_list_table_custom_column', array($this, 'add_buttons_to_actions_column_hpos'), 20, 2);
        }

        wosb_log("Hook registrati completati");
    }


    


    /**
     * Aggiunge i pulsanti alla colonna azioni esistente (sistema legacy)
     */
    public function add_buttons_to_actions_column($column_name, $post_id) {
        if ($column_name === 'wc_actions' || $column_name === 'order_actions') {
            $order = wc_get_order($post_id);

            if ($order) {
                wosb_log("Aggiungendo pulsanti alla colonna azioni per ordine: " . $order->get_id());
                echo '<div class="wosb-inline-actions">';
                $this->render_order_action_buttons_inline($order);
                echo '</div>';
            }
        }
    }

    /**
     * Aggiunge i pulsanti alla colonna azioni esistente (HPOS)
     */
    public function add_buttons_to_actions_column_hpos($column_name, $order) {
        if ($column_name === 'wc_actions' || $column_name === 'order_actions') {
            if (is_numeric($order)) {
                $order = wc_get_order($order);
            }

            if ($order) {
                wosb_log("HPOS: Aggiungendo pulsanti alla colonna azioni per ordine: " . $order->get_id());
                echo '<div class="wosb-inline-actions">';
                $this->render_order_action_buttons_inline($order);
                echo '</div>';
            }
        }
    }
    


    /**
     * Renderizza i pulsanti di azione inline per la colonna azioni esistente
     */
    private function render_order_action_buttons_inline($order) {
        $order_status = 'wc-' . $order->get_status();
        $buttons = $this->db->get_buttons_for_status($order_status);

        // Debug: log per capire cosa sta succedendo
        wosb_log("Ordine ID: " . $order->get_id() . ", Status: " . $order_status . ", Pulsanti trovati: " . count($buttons));

        if (empty($buttons)) {
            return; // Non mostrare nulla se non ci sono pulsanti
        }

        foreach ($buttons as $button) {
            $action_url = wp_nonce_url(
                admin_url('admin-post.php?action=wosb_button_action&button_id=' . $button->id . '&order_id=' . $order->get_id()),
                'wosb_button_action_' . $button->id . '_' . $order->get_id(),
                'wosb_nonce'
            );

            $target_status_name = wosb_get_status_name($button->target_status);

            echo '<a href="' . esc_url($action_url) . '" ';
            echo 'class="button wosb-inline-button" ';
            echo 'title="' . esc_attr($button->button_name . ' - Cambia a: ' . $target_status_name) . '" ';
            echo 'style="margin-left: 4px;">';

            // Aggiungi icona se presente
            if (!empty($button->dashicon_code)) {
                echo '<span class="dashicons ' . $this->get_dashicon_class($button->dashicon_code) . '"></span>';
            }

            echo '<span class="wosb-button-text">' . esc_html($button->button_name) . '</span>';
            echo '</a>';
        }
    }

    /**
     * Converte il codice unicode in classe dashicon
     */
    private function get_dashicon_class($unicode_code) {
        // Rimuovi \f e converti in classe dashicon
        $code = str_replace('\f', '', $unicode_code);

        // Mappa dei codici più comuni
        $icon_map = array(
            '147' => 'dashicons-admin-post',
            '148' => 'dashicons-admin-media',
            '149' => 'dashicons-admin-links',
            '150' => 'dashicons-admin-page',
            '151' => 'dashicons-admin-comments',
            '152' => 'dashicons-admin-appearance',
            '153' => 'dashicons-admin-plugins',
            '154' => 'dashicons-admin-users',
            '155' => 'dashicons-admin-tools',
            '156' => 'dashicons-admin-settings',
            '157' => 'dashicons-admin-network',
            '158' => 'dashicons-admin-home',
            '159' => 'dashicons-admin-generic',
            '160' => 'dashicons-admin-collapse',
            '161' => 'dashicons-filter',
            '162' => 'dashicons-admin-customizer',
            '163' => 'dashicons-admin-multisite',
            '164' => 'dashicons-admin-site',
            '165' => 'dashicons-admin-site-alt',
            '166' => 'dashicons-admin-site-alt2',
            '167' => 'dashicons-admin-site-alt3',
            '168' => 'dashicons-dashboard',
            '169' => 'dashicons-admin-post',
            '170' => 'dashicons-admin-media',
            '171' => 'dashicons-admin-links',
            '172' => 'dashicons-admin-page',
            '173' => 'dashicons-admin-comments',
            '174' => 'dashicons-admin-appearance',
            '175' => 'dashicons-admin-plugins',
            '176' => 'dashicons-admin-users',
            '177' => 'dashicons-admin-tools',
            '178' => 'dashicons-admin-settings',
            '179' => 'dashicons-admin-network',
            '180' => 'dashicons-admin-home',
            '181' => 'dashicons-admin-generic',
            '182' => 'dashicons-admin-collapse',
            '183' => 'dashicons-filter',
            '184' => 'dashicons-admin-customizer',
            '185' => 'dashicons-admin-multisite',
            '200' => 'dashicons-welcome-write-blog',
            '201' => 'dashicons-welcome-add-page',
            '202' => 'dashicons-welcome-view-site',
            '203' => 'dashicons-welcome-widgets-menus',
            '204' => 'dashicons-welcome-comments',
            '205' => 'dashicons-welcome-learn-more',
            '206' => 'dashicons-format-aside',
            '207' => 'dashicons-format-image',
            '208' => 'dashicons-format-gallery',
            '209' => 'dashicons-format-video',
            '210' => 'dashicons-format-status',
            '211' => 'dashicons-format-quote',
            '212' => 'dashicons-format-chat',
            '213' => 'dashicons-format-audio',
            '214' => 'dashicons-camera',
            '215' => 'dashicons-camera-alt',
            '216' => 'dashicons-images-alt',
            '217' => 'dashicons-images-alt2',
            '218' => 'dashicons-video-alt',
            '219' => 'dashicons-video-alt2',
            '220' => 'dashicons-video-alt3',
            '221' => 'dashicons-media-archive',
            '222' => 'dashicons-media-audio',
            '223' => 'dashicons-media-code',
            '224' => 'dashicons-media-default',
            '225' => 'dashicons-media-document',
            '226' => 'dashicons-media-interactive',
            '227' => 'dashicons-media-spreadsheet',
            '228' => 'dashicons-media-text',
            '229' => 'dashicons-media-video',
            '230' => 'dashicons-playlist-audio',
            '231' => 'dashicons-playlist-video',
            '232' => 'dashicons-controls-play',
            '233' => 'dashicons-controls-pause',
            '234' => 'dashicons-controls-forward',
            '235' => 'dashicons-controls-skipforward',
            '236' => 'dashicons-controls-back',
            '237' => 'dashicons-controls-skipback',
            '238' => 'dashicons-controls-repeat',
            '239' => 'dashicons-controls-volumeon',
            '240' => 'dashicons-controls-volumeoff'
        );

        return isset($icon_map[$code]) ? $icon_map[$code] : 'dashicons-admin-generic';
    }
    
    /**
     * Gestisce l'azione del pulsante
     */
    public function handle_button_action() {
        if (!current_user_can('edit_shop_orders')) {
            wp_die('Non hai i permessi per eseguire questa azione.');
        }
        
        $button_id = isset($_GET['button_id']) ? intval($_GET['button_id']) : 0;
        $order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;
        
        // Verifica nonce
        if (!wp_verify_nonce($_GET['wosb_nonce'], 'wosb_button_action_' . $button_id . '_' . $order_id)) {
            wp_die('Errore di sicurezza. Riprova.');
        }
        
        // Ottieni il pulsante e l'ordine
        $button = $this->db->get_button($button_id);
        $order = wc_get_order($order_id);
        
        if (!$button || !$order) {
            wp_redirect(add_query_arg(array(
                'error' => urlencode('Pulsante o ordine non trovato.')
            ), wp_get_referer()));
            exit;
        }
        
        // Verifica che il pulsante sia applicabile per lo status corrente dell'ordine
        $current_status = 'wc-' . $order->get_status();
        if (!wosb_order_has_status($order, $button->order_statuses)) {
            wp_redirect(add_query_arg(array(
                'error' => urlencode('Questo pulsante non è applicabile per lo status corrente dell\'ordine.')
            ), wp_get_referer()));
            exit;
        }
        
        // Aggiorna lo status dell'ordine
        $target_status = str_replace('wc-', '', $button->target_status);
        $order->update_status($target_status, 'Status aggiornato tramite pulsante: ' . $button->button_name);
        
        // Invia l'email al cliente
        $email_sent = $this->send_customer_email($order, $button);
        
        // Redirect con messaggio di successo
        $message = 'Status dell\'ordine aggiornato con successo.';
        if ($email_sent) {
            $message .= ' Email inviata al cliente.';
        } else {
            $message .= ' Errore nell\'invio dell\'email al cliente.';
        }
        
        wp_redirect(add_query_arg(array(
            'success' => urlencode($message)
        ), wp_get_referer()));
        
        exit;
    }
    
    /**
     * Invia email al cliente
     */
    private function send_customer_email($order, $button) {
        $email_handler = new Order_Status_Buttons_Email();
        return $email_handler->send_custom_email($order, $button);
    }
    
    /**
     * Aggiunge gli stili CSS per i pulsanti
     */
    public function add_button_styles() {
        $screen = get_current_screen();
        
        if (!$screen || !in_array($screen->id, array('woocommerce_page_wc-orders', 'edit-shop_order'))) {
            return;
        }
        
        ?>
        <style type="text/css">
        .wosb-order-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }
        
        .wosb-action-button {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            background: #0073aa;
            color: white !important;
            text-decoration: none !important;
            border-radius: 3px;
            font-size: 12px;
            line-height: 1;
            transition: background-color 0.2s;
        }
        
        .wosb-action-button:hover {
            background: #005a87;
            color: white !important;
        }
        
        .wosb-action-button .dashicons {
            font-size: 14px;
            width: 14px;
            height: 14px;
            margin-right: 4px;
        }
        
        .wosb-button-text {
            white-space: nowrap;
        }
        
        @media (max-width: 782px) {
            .wosb-action-button .wosb-button-text {
                display: none;
            }
            
            .wosb-action-button .dashicons {
                margin-right: 0;
            }
        }
        </style>
        <?php
    }
    
    /**
     * Aggiunge JavaScript per conferma azioni
     */
    public function add_button_scripts() {
        $screen = get_current_screen();
        
        if (!$screen || !in_array($screen->id, array('woocommerce_page_wc-orders', 'edit-shop_order'))) {
            return;
        }
        
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Rimuoviamo l'alert di conferma - i pulsanti funzionano direttamente
            $('.wosb-action-button, .wosb-inline-button').on('click', function(e) {
                // Aggiungi solo un indicatore di caricamento
                $(this).addClass('wosb-loading').prop('disabled', true);
                $(this).find('.wosb-button-text').text('Elaborazione...');
            });
        });
        </script>
        <?php
    }


}
