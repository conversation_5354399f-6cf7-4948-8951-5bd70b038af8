<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe per l'integrazione con WooCommerce
 */
class Order_Status_Buttons_Integration {
    
    private $db;
    
    public function __construct() {
        $this->db = new Order_Status_Buttons_DB();

        // Hook per entrambi i sistemi (HPOS e Legacy)
        add_action('admin_init', array($this, 'setup_hooks'));

        // Gestisci le azioni dei pulsanti
        add_action('admin_post_wosb_button_action', array($this, 'handle_button_action'));

        // Aggiungi CSS per i pulsanti
        add_action('admin_head', array($this, 'add_button_styles'));

        // Aggiungi JavaScript per conferma azioni
        add_action('admin_footer', array($this, 'add_button_scripts'));
    }

    /**
     * Setup degli hook in base al sistema utilizzato
     */
    public function setup_hooks() {
        // Verifica se HPOS è abilitato
        if (class_exists('Automattic\WooCommerce\Utilities\OrderUtil') &&
            method_exists('Automattic\WooCommerce\Utilities\OrderUtil', 'custom_orders_table_usage_is_enabled') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            wosb_log("HPOS è abilitato, usando hook HPOS");

            // HPOS hooks
            add_filter('woocommerce_shop_order_list_table_columns', array($this, 'add_order_actions_column'));
            add_action('woocommerce_shop_order_list_table_custom_column', array($this, 'populate_order_actions_column'), 10, 2);

        } else {
            wosb_log("HPOS non è abilitato, usando hook legacy");

            // Legacy hooks
            add_filter('manage_shop_order_posts_columns', array($this, 'add_order_actions_column'));
            add_action('manage_shop_order_posts_custom_column', array($this, 'populate_order_actions_column_legacy'), 10, 2);
        }

        // Hook aggiuntivi per compatibilità
        add_filter('manage_woocommerce_page_wc-orders_columns', array($this, 'add_order_actions_column'));
        add_action('manage_woocommerce_page_wc-orders_custom_column', array($this, 'populate_order_actions_column'), 10, 2);
    }
    
    /**
     * Aggiunge la colonna azioni alla lista ordini
     */
    public function add_order_actions_column($columns) {
        // Debug: log delle colonne esistenti
        wosb_log("Colonne esistenti: " . implode(', ', array_keys($columns)));

        // Inserisci la colonna prima della colonna "Date"
        $new_columns = array();

        foreach ($columns as $key => $column) {
            if ($key === 'order_date' || $key === 'date') {
                $new_columns['wosb_actions'] = 'Azioni Status';
            }
            $new_columns[$key] = $column;
        }

        // Se non abbiamo trovato order_date o date, aggiungi alla fine
        if (!isset($new_columns['wosb_actions'])) {
            $new_columns['wosb_actions'] = 'Azioni Status';
        }

        wosb_log("Nuove colonne: " . implode(', ', array_keys($new_columns)));

        return $new_columns;
    }
    
    /**
     * Popola la colonna azioni per HPOS (High-Performance Order Storage)
     */
    public function populate_order_actions_column($column_name, $order) {
        wosb_log("HPOS: Popolando colonna: " . $column_name);

        if ($column_name === 'wosb_actions') {
            if (is_numeric($order)) {
                $order = wc_get_order($order);
            }

            if ($order) {
                wosb_log("HPOS: Rendering pulsanti per ordine ID: " . $order->get_id());
                $this->render_order_action_buttons($order);
            } else {
                wosb_log("HPOS: Ordine non valido");
                echo '<span class="na" style="color: red;">Ordine non valido</span>';
            }
        }
    }

    /**
     * Popola la colonna azioni per il sistema legacy
     */
    public function populate_order_actions_column_legacy($column_name, $post_id) {
        wosb_log("Legacy: Popolando colonna: " . $column_name . " per post ID: " . $post_id);

        if ($column_name === 'wosb_actions') {
            $order = wc_get_order($post_id);

            if ($order) {
                wosb_log("Legacy: Rendering pulsanti per ordine ID: " . $order->get_id());
                $this->render_order_action_buttons($order);
            } else {
                wosb_log("Legacy: Ordine non valido per post ID: " . $post_id);
                echo '<span class="na" style="color: red;">Ordine non valido</span>';
            }
        }
    }
    
    /**
     * Renderizza i pulsanti di azione per un ordine
     */
    private function render_order_action_buttons($order) {
        $order_status = 'wc-' . $order->get_status();
        $buttons = $this->db->get_buttons_for_status($order_status);

        // Debug: log per capire cosa sta succedendo
        wosb_log("Ordine ID: " . $order->get_id() . ", Status: " . $order_status . ", Pulsanti trovati: " . count($buttons));

        if (empty($buttons)) {
            // Mostra sempre qualcosa per debug
            echo '<span class="na" style="color: red;">Nessun pulsante per ' . esc_html($order_status) . '</span>';
            return;
        }

        echo '<div class="wosb-order-actions">';

        foreach ($buttons as $button) {
            $action_url = wp_nonce_url(
                admin_url('admin-post.php?action=wosb_button_action&button_id=' . $button->id . '&order_id=' . $order->get_id()),
                'wosb_button_action_' . $button->id . '_' . $order->get_id(),
                'wosb_nonce'
            );

            $target_status_name = wosb_get_status_name($button->target_status);
            $dashicon_css = wosb_dashicon_to_css($button->dashicon_code);

            echo '<a href="' . esc_url($action_url) . '" ';
            echo 'class="wosb-action-button" ';
            echo 'title="' . esc_attr($button->button_name . ' - Cambia a: ' . $target_status_name) . '" ';
            echo 'data-confirm="Sei sicuro di voler cambiare lo status dell\'ordine #' . $order->get_order_number() . ' a \'' . esc_attr($target_status_name) . '\' e inviare un\'email al cliente?">';
            echo '<span class="dashicons" style="font-family: dashicons;">' . $dashicon_css . '</span>';
            echo '<span class="wosb-button-text">' . esc_html($button->button_name) . '</span>';
            echo '</a>';
        }

        echo '</div>';
    }
    
    /**
     * Gestisce l'azione del pulsante
     */
    public function handle_button_action() {
        if (!current_user_can('edit_shop_orders')) {
            wp_die('Non hai i permessi per eseguire questa azione.');
        }
        
        $button_id = isset($_GET['button_id']) ? intval($_GET['button_id']) : 0;
        $order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;
        
        // Verifica nonce
        if (!wp_verify_nonce($_GET['wosb_nonce'], 'wosb_button_action_' . $button_id . '_' . $order_id)) {
            wp_die('Errore di sicurezza. Riprova.');
        }
        
        // Ottieni il pulsante e l'ordine
        $button = $this->db->get_button($button_id);
        $order = wc_get_order($order_id);
        
        if (!$button || !$order) {
            wp_redirect(add_query_arg(array(
                'error' => urlencode('Pulsante o ordine non trovato.')
            ), wp_get_referer()));
            exit;
        }
        
        // Verifica che il pulsante sia applicabile per lo status corrente dell'ordine
        $current_status = 'wc-' . $order->get_status();
        if (!wosb_order_has_status($order, $button->order_statuses)) {
            wp_redirect(add_query_arg(array(
                'error' => urlencode('Questo pulsante non è applicabile per lo status corrente dell\'ordine.')
            ), wp_get_referer()));
            exit;
        }
        
        // Aggiorna lo status dell'ordine
        $target_status = str_replace('wc-', '', $button->target_status);
        $order->update_status($target_status, 'Status aggiornato tramite pulsante: ' . $button->button_name);
        
        // Invia l'email al cliente
        $email_sent = $this->send_customer_email($order, $button);
        
        // Redirect con messaggio di successo
        $message = 'Status dell\'ordine aggiornato con successo.';
        if ($email_sent) {
            $message .= ' Email inviata al cliente.';
        } else {
            $message .= ' Errore nell\'invio dell\'email al cliente.';
        }
        
        wp_redirect(add_query_arg(array(
            'success' => urlencode($message)
        ), wp_get_referer()));
        
        exit;
    }
    
    /**
     * Invia email al cliente
     */
    private function send_customer_email($order, $button) {
        $email_handler = new Order_Status_Buttons_Email();
        return $email_handler->send_custom_email($order, $button);
    }
    
    /**
     * Aggiunge gli stili CSS per i pulsanti
     */
    public function add_button_styles() {
        $screen = get_current_screen();
        
        if (!$screen || !in_array($screen->id, array('woocommerce_page_wc-orders', 'edit-shop_order'))) {
            return;
        }
        
        ?>
        <style type="text/css">
        .wosb-order-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }
        
        .wosb-action-button {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            background: #0073aa;
            color: white !important;
            text-decoration: none !important;
            border-radius: 3px;
            font-size: 12px;
            line-height: 1;
            transition: background-color 0.2s;
        }
        
        .wosb-action-button:hover {
            background: #005a87;
            color: white !important;
        }
        
        .wosb-action-button .dashicons {
            font-size: 14px;
            width: 14px;
            height: 14px;
            margin-right: 4px;
        }
        
        .wosb-button-text {
            white-space: nowrap;
        }
        
        @media (max-width: 782px) {
            .wosb-action-button .wosb-button-text {
                display: none;
            }
            
            .wosb-action-button .dashicons {
                margin-right: 0;
            }
        }
        </style>
        <?php
    }
    
    /**
     * Aggiunge JavaScript per conferma azioni
     */
    public function add_button_scripts() {
        $screen = get_current_screen();
        
        if (!$screen || !in_array($screen->id, array('woocommerce_page_wc-orders', 'edit-shop_order'))) {
            return;
        }
        
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('.wosb-action-button').on('click', function(e) {
                var confirmMessage = $(this).data('confirm');
                if (confirmMessage && !confirm(confirmMessage)) {
                    e.preventDefault();
                    return false;
                }
            });
        });
        </script>
        <?php
    }
}
