<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe per l'integrazione con WooCommerce
 */
class Order_Status_Buttons_Integration {
    
    private $db;
    
    public function __construct() {
        $this->db = new Order_Status_Buttons_DB();

        // Registra gli hook immediatamente - non aspettare admin_init
        $this->setup_hooks();

        // Gestisci le azioni dei pulsanti
        add_action('admin_post_wosb_button_action', array($this, 'handle_button_action'));



        // Aggiungi CSS per i pulsanti
        add_action('admin_head', array($this, 'add_button_styles'));

        // Aggiungi JavaScript per conferma azioni
        add_action('admin_footer', array($this, 'add_button_scripts'));
    }

    /**
     * Setup degli hook in base al sistema utilizzato
     */
    public function setup_hooks() {
        wosb_log("Setup hooks chiamato");

        // Per il sistema legacy (HPOS disabilitato), usa sempre questi hook
        wosb_log("Registrando hook legacy per shop_order");

        // Usa il filtro corretto di WooCommerce per aggiungere pulsanti alla colonna azioni
        add_filter('woocommerce_admin_order_actions', array($this, 'add_order_actions'), 999, 2);

        wosb_log("Hook registrati completati");
    }


    


    /**
     * Aggiunge i pulsanti azione personalizzati alla lista ordini
     */
    public function add_order_actions($actions, $order) {
        if (!$order) {
            return $actions;
        }

        // Ottieni lo stato dell'ordine con prefisso wc-
        $order_status = 'wc-' . $order->get_status();

        // Ottieni i pulsanti per questo stato ordine
        $buttons = $this->db->get_buttons_for_status($order_status);

        wosb_log("Ordine ID: " . $order->get_id() . ", Status: " . $order_status . ", Pulsanti trovati: " . count($buttons));

        if (empty($buttons)) {
            return $actions;
        }

        // Aggiungi ogni pulsante all'array azioni
        foreach ($buttons as $button) {
            $action_key = 'wosb_button_' . $button->id;

            // Rimuovi eventuali azioni esistenti con la stessa chiave per evitare duplicati
            if (isset($actions[$action_key])) {
                unset($actions[$action_key]);
            }

            // Aggiungi il nostro pulsante
            $actions[$action_key] = array(
                'url' => wp_nonce_url(
                    admin_url('admin-post.php?action=wosb_button_action&button_id=' . $button->id . '&order_id=' . $order->get_id()),
                    'wosb_button_action_' . $button->id . '_' . $order->get_id(),
                    'wosb_nonce'
                ),
                'name' => esc_attr($button->button_name),
                'action' => $action_key
            );
        }

        return $actions;
    }
    





    
    /**
     * Gestisce l'azione del pulsante
     */
    public function handle_button_action() {
        if (!current_user_can('edit_shop_orders')) {
            wp_die('Non hai i permessi per eseguire questa azione.');
        }
        
        $button_id = isset($_GET['button_id']) ? intval($_GET['button_id']) : 0;
        $order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;
        
        // Verifica nonce
        if (!wp_verify_nonce($_GET['wosb_nonce'], 'wosb_button_action_' . $button_id . '_' . $order_id)) {
            wp_die('Errore di sicurezza. Riprova.');
        }
        
        // Ottieni il pulsante e l'ordine
        $button = $this->db->get_button($button_id);
        $order = wc_get_order($order_id);
        
        if (!$button || !$order) {
            wp_redirect(add_query_arg(array(
                'error' => urlencode('Pulsante o ordine non trovato.')
            ), wp_get_referer()));
            exit;
        }
        
        // Verifica che il pulsante sia applicabile per lo status corrente dell'ordine
        $current_status = 'wc-' . $order->get_status();
        if (!wosb_order_has_status($order, $button->order_statuses)) {
            wp_redirect(add_query_arg(array(
                'error' => urlencode('Questo pulsante non è applicabile per lo status corrente dell\'ordine.')
            ), wp_get_referer()));
            exit;
        }
        
        // Aggiorna lo status dell'ordine
        $target_status = str_replace('wc-', '', $button->target_status);
        $order->update_status($target_status, 'Status aggiornato tramite pulsante: ' . $button->button_name);
        
        // Invia l'email al cliente
        $email_sent = $this->send_customer_email($order, $button);
        
        // Redirect con messaggio di successo
        $message = 'Status dell\'ordine aggiornato con successo.';
        if ($email_sent) {
            $message .= ' Email inviata al cliente.';
        } else {
            $message .= ' Errore nell\'invio dell\'email al cliente.';
        }
        
        wp_redirect(add_query_arg(array(
            'success' => urlencode($message)
        ), wp_get_referer()));
        
        exit;
    }
    
    /**
     * Invia email al cliente
     */
    private function send_customer_email($order, $button) {
        $email_handler = new Order_Status_Buttons_Email();
        return $email_handler->send_custom_email($order, $button);
    }
    
    /**
     * Aggiunge CSS dinamico per i pulsanti personalizzati
     */
    public function add_button_styles() {
        $screen = get_current_screen();

        if (!$screen || !in_array($screen->id, array('woocommerce_page_wc-orders', 'edit-shop_order'))) {
            return;
        }

        // Ottieni tutti i pulsanti per generare CSS dinamico
        $buttons = $this->db->get_all_buttons();

        if (empty($buttons)) {
            return;
        }

        echo '<style type="text/css">';

        foreach ($buttons as $button) {
            $action_key = 'wosb_button_' . $button->id;
            $unicode_input = !empty($button->dashicon_code) ? $button->dashicon_code : '\f147';

            // Gestione dell'unicode come nel plugin esistente
            if (preg_match('/^\\\\{1,2}f[0-9a-fA-F]{3,4}$/i', $unicode_input)) {
                // Rimuovi tutti i backslash iniziali per ottenere il codice pulito
                $unicode = preg_replace('/^\\\\+/', '', $unicode_input);
            } else if (preg_match('/^f[0-9a-fA-F]{3,4}$/i', $unicode_input)) {
                // Se inizia con 'f' ma senza backslash
                $unicode = $unicode_input;
            } else if (preg_match('/^[0-9a-fA-F]{3,4}$/i', $unicode_input)) {
                // Se è solo il codice hex senza 'f', aggiungilo
                $unicode = 'f' . $unicode_input;
            } else {
                // Fallback
                $unicode = 'f147';
            }

            echo ".wc-action-button-{$action_key}::after {
                font-family: Dashicons !important;
                content: '\\{$unicode}' !important;
            }\n";

            // Debug: aggiungi commento CSS con informazioni
            echo "/* Button ID: {$button->id}, Name: '{$button->button_name}', Input: '{$unicode_input}', Unicode: '{$unicode}' */\n";
        }

        echo '</style>';
    }
    
    /**
     * Aggiunge JavaScript per conferma azioni
     */
    public function add_button_scripts() {
        $screen = get_current_screen();
        
        if (!$screen || !in_array($screen->id, array('woocommerce_page_wc-orders', 'edit-shop_order'))) {
            return;
        }
        
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // I pulsanti WooCommerce funzionano direttamente senza conferma
            // Aggiungi solo un indicatore di caricamento per i nostri pulsanti
            $('[class*="wc-action-button-wosb_button_"]').on('click', function(e) {
                $(this).addClass('wosb-loading').prop('disabled', true);
            });
        });
        </script>
        <?php
    }


}
