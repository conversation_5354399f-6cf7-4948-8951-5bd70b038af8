<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe per l'integrazione con WooCommerce
 */
class Order_Status_Buttons_Integration {
    
    private $db;
    
    public function __construct() {
        $this->db = new Order_Status_Buttons_DB();

        // Registra gli hook immediatamente - non aspettare admin_init
        $this->setup_hooks();

        // Gestisci le azioni dei pulsanti
        add_action('admin_post_wosb_button_action', array($this, 'handle_button_action'));

        // Handler AJAX per fallback
        add_action('wp_ajax_wosb_get_order_buttons', array($this, 'ajax_get_order_buttons'));

        // Aggiungi CSS per i pulsanti
        add_action('admin_head', array($this, 'add_button_styles'));

        // Aggiungi JavaScript per conferma azioni
        add_action('admin_footer', array($this, 'add_button_scripts'));
    }

    /**
     * Setup degli hook in base al sistema utilizzato
     */
    public function setup_hooks() {
        wosb_log("Setup hooks chiamato");

        // Per il sistema legacy (HPOS disabilitato), usa sempre questi hook
        wosb_log("Registrando hook legacy per shop_order");

        // Hook legacy per post type shop_order
        add_filter('manage_shop_order_posts_columns', array($this, 'add_order_actions_column'), 20);
        add_action('manage_shop_order_posts_custom_column', array($this, 'populate_order_actions_column_legacy'), 20, 2);

        // Hook per edit.php?post_type=shop_order
        add_filter('manage_edit-shop_order_columns', array($this, 'add_order_actions_column'), 20);
        add_action('manage_shop_order_posts_custom_column', array($this, 'populate_order_actions_column_legacy'), 20, 2);

        // Hook aggiuntivi per compatibilità con versioni diverse di WooCommerce
        add_filter('manage_woocommerce_page_wc-orders_columns', array($this, 'add_order_actions_column'), 20);
        add_action('manage_woocommerce_page_wc-orders_custom_column', array($this, 'populate_order_actions_column'), 20, 2);

        // Hook per HPOS se disponibile
        if (class_exists('Automattic\WooCommerce\Utilities\OrderUtil')) {
            add_filter('woocommerce_shop_order_list_table_columns', array($this, 'add_order_actions_column'), 20);
            add_action('woocommerce_shop_order_list_table_custom_column', array($this, 'populate_order_actions_column'), 20, 2);
        }

        wosb_log("Hook registrati completati");

        // Hook di fallback - forza l'aggiunta della colonna con JavaScript se gli hook non funzionano
        add_action('admin_footer', array($this, 'add_fallback_column_script'));
    }

    /**
     * Script di fallback per aggiungere la colonna se gli hook non funzionano
     */
    public function add_fallback_column_script() {
        $screen = get_current_screen();

        if (!$screen || !in_array($screen->id, array('edit-shop_order', 'woocommerce_page_wc-orders'))) {
            return;
        }

        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Verifica se la colonna esiste già
            if ($('th.column-wosb_actions').length > 0) {
                console.log('WOSB: Colonna già presente');
                return;
            }

            console.log('WOSB: Aggiungendo colonna con JavaScript fallback');

            // Trova la tabella degli ordini
            var $table = $('.wp-list-table.orders, .wp-list-table.posts');

            if ($table.length === 0) {
                console.log('WOSB: Tabella ordini non trovata');
                return;
            }

            // Aggiungi header della colonna
            var $headerRow = $table.find('thead tr, .tablenav + .wp-list-table thead tr').first();
            if ($headerRow.length > 0) {
                $headerRow.append('<th class="column-wosb_actions">Azioni Status</th>');
                console.log('WOSB: Header colonna aggiunto');
            }

            // Aggiungi celle per ogni riga
            $table.find('tbody tr').each(function() {
                var $row = $(this);
                var $orderCell = $row.find('.order_number a, .column-order_number a').first();

                if ($orderCell.length > 0) {
                    var orderUrl = $orderCell.attr('href');
                    var orderIdMatch = orderUrl.match(/[?&]id=(\d+)|post=(\d+)/);
                    var orderId = orderIdMatch ? (orderIdMatch[1] || orderIdMatch[2]) : null;

                    if (orderId) {
                        // Fai una chiamata AJAX per ottenere i pulsanti per questo ordine
                        $.post(ajaxurl, {
                            action: 'wosb_get_order_buttons',
                            order_id: orderId,
                            nonce: '<?php echo wp_create_nonce('wosb_ajax'); ?>'
                        }, function(response) {
                            if (response.success) {
                                $row.append('<td class="column-wosb_actions">' + response.data + '</td>');
                            } else {
                                $row.append('<td class="column-wosb_actions"><span class="na">—</span></td>');
                            }
                        });
                    } else {
                        $row.append('<td class="column-wosb_actions"><span class="na">—</span></td>');
                    }
                } else {
                    $row.append('<td class="column-wosb_actions"><span class="na">—</span></td>');
                }
            });
        });
        </script>
        <?php
    }
    
    /**
     * Aggiunge la colonna azioni alla lista ordini
     */
    public function add_order_actions_column($columns) {
        // Debug: log delle colonne esistenti
        wosb_log("HOOK CHIAMATO! add_order_actions_column");
        wosb_log("Colonne esistenti: " . implode(', ', array_keys($columns)));

        // Inserisci la colonna prima della colonna "Date" o alla fine
        $new_columns = array();
        $added = false;

        foreach ($columns as $key => $column) {
            // Prova diversi nomi di colonne dove inserire
            if (in_array($key, array('order_date', 'date', 'order_status', 'order_total', 'wc_actions'))) {
                $new_columns['wosb_actions'] = 'Azioni Status';
                $added = true;
            }
            $new_columns[$key] = $column;
        }

        // Se non abbiamo trovato una posizione, aggiungi alla fine
        if (!$added) {
            $new_columns['wosb_actions'] = 'Azioni Status';
        }

        wosb_log("Colonna aggiunta! Nuove colonne: " . implode(', ', array_keys($new_columns)));

        return $new_columns;
    }
    
    /**
     * Popola la colonna azioni per HPOS (High-Performance Order Storage)
     */
    public function populate_order_actions_column($column_name, $order) {
        wosb_log("HPOS: Popolando colonna: " . $column_name);

        if ($column_name === 'wosb_actions') {
            if (is_numeric($order)) {
                $order = wc_get_order($order);
            }

            if ($order) {
                wosb_log("HPOS: Rendering pulsanti per ordine ID: " . $order->get_id());
                $this->render_order_action_buttons($order);
            } else {
                wosb_log("HPOS: Ordine non valido");
                echo '<span class="na" style="color: red;">Ordine non valido</span>';
            }
        }
    }

    /**
     * Popola la colonna azioni per il sistema legacy
     */
    public function populate_order_actions_column_legacy($column_name, $post_id) {
        wosb_log("Legacy: Popolando colonna: " . $column_name . " per post ID: " . $post_id);

        if ($column_name === 'wosb_actions') {
            $order = wc_get_order($post_id);

            if ($order) {
                wosb_log("Legacy: Rendering pulsanti per ordine ID: " . $order->get_id());
                $this->render_order_action_buttons($order);
            } else {
                wosb_log("Legacy: Ordine non valido per post ID: " . $post_id);
                echo '<span class="na" style="color: red;">Ordine non valido</span>';
            }
        }
    }
    
    /**
     * Renderizza i pulsanti di azione per un ordine
     */
    private function render_order_action_buttons($order) {
        $order_status = 'wc-' . $order->get_status();
        $buttons = $this->db->get_buttons_for_status($order_status);

        // Debug: log per capire cosa sta succedendo
        wosb_log("Ordine ID: " . $order->get_id() . ", Status: " . $order_status . ", Pulsanti trovati: " . count($buttons));

        if (empty($buttons)) {
            // Mostra sempre qualcosa per debug
            echo '<span class="na" style="color: red;">Nessun pulsante per ' . esc_html($order_status) . '</span>';
            return;
        }

        echo '<div class="wosb-order-actions">';

        foreach ($buttons as $button) {
            $action_url = wp_nonce_url(
                admin_url('admin-post.php?action=wosb_button_action&button_id=' . $button->id . '&order_id=' . $order->get_id()),
                'wosb_button_action_' . $button->id . '_' . $order->get_id(),
                'wosb_nonce'
            );

            $target_status_name = wosb_get_status_name($button->target_status);
            $dashicon_css = wosb_dashicon_to_css($button->dashicon_code);

            echo '<a href="' . esc_url($action_url) . '" ';
            echo 'class="wosb-action-button" ';
            echo 'title="' . esc_attr($button->button_name . ' - Cambia a: ' . $target_status_name) . '" ';
            echo 'data-confirm="Sei sicuro di voler cambiare lo status dell\'ordine #' . $order->get_order_number() . ' a \'' . esc_attr($target_status_name) . '\' e inviare un\'email al cliente?">';
            echo '<span class="dashicons" style="font-family: dashicons;">' . $dashicon_css . '</span>';
            echo '<span class="wosb-button-text">' . esc_html($button->button_name) . '</span>';
            echo '</a>';
        }

        echo '</div>';
    }
    
    /**
     * Gestisce l'azione del pulsante
     */
    public function handle_button_action() {
        if (!current_user_can('edit_shop_orders')) {
            wp_die('Non hai i permessi per eseguire questa azione.');
        }
        
        $button_id = isset($_GET['button_id']) ? intval($_GET['button_id']) : 0;
        $order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;
        
        // Verifica nonce
        if (!wp_verify_nonce($_GET['wosb_nonce'], 'wosb_button_action_' . $button_id . '_' . $order_id)) {
            wp_die('Errore di sicurezza. Riprova.');
        }
        
        // Ottieni il pulsante e l'ordine
        $button = $this->db->get_button($button_id);
        $order = wc_get_order($order_id);
        
        if (!$button || !$order) {
            wp_redirect(add_query_arg(array(
                'error' => urlencode('Pulsante o ordine non trovato.')
            ), wp_get_referer()));
            exit;
        }
        
        // Verifica che il pulsante sia applicabile per lo status corrente dell'ordine
        $current_status = 'wc-' . $order->get_status();
        if (!wosb_order_has_status($order, $button->order_statuses)) {
            wp_redirect(add_query_arg(array(
                'error' => urlencode('Questo pulsante non è applicabile per lo status corrente dell\'ordine.')
            ), wp_get_referer()));
            exit;
        }
        
        // Aggiorna lo status dell'ordine
        $target_status = str_replace('wc-', '', $button->target_status);
        $order->update_status($target_status, 'Status aggiornato tramite pulsante: ' . $button->button_name);
        
        // Invia l'email al cliente
        $email_sent = $this->send_customer_email($order, $button);
        
        // Redirect con messaggio di successo
        $message = 'Status dell\'ordine aggiornato con successo.';
        if ($email_sent) {
            $message .= ' Email inviata al cliente.';
        } else {
            $message .= ' Errore nell\'invio dell\'email al cliente.';
        }
        
        wp_redirect(add_query_arg(array(
            'success' => urlencode($message)
        ), wp_get_referer()));
        
        exit;
    }
    
    /**
     * Invia email al cliente
     */
    private function send_customer_email($order, $button) {
        $email_handler = new Order_Status_Buttons_Email();
        return $email_handler->send_custom_email($order, $button);
    }
    
    /**
     * Aggiunge gli stili CSS per i pulsanti
     */
    public function add_button_styles() {
        $screen = get_current_screen();
        
        if (!$screen || !in_array($screen->id, array('woocommerce_page_wc-orders', 'edit-shop_order'))) {
            return;
        }
        
        ?>
        <style type="text/css">
        .wosb-order-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }
        
        .wosb-action-button {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            background: #0073aa;
            color: white !important;
            text-decoration: none !important;
            border-radius: 3px;
            font-size: 12px;
            line-height: 1;
            transition: background-color 0.2s;
        }
        
        .wosb-action-button:hover {
            background: #005a87;
            color: white !important;
        }
        
        .wosb-action-button .dashicons {
            font-size: 14px;
            width: 14px;
            height: 14px;
            margin-right: 4px;
        }
        
        .wosb-button-text {
            white-space: nowrap;
        }
        
        @media (max-width: 782px) {
            .wosb-action-button .wosb-button-text {
                display: none;
            }
            
            .wosb-action-button .dashicons {
                margin-right: 0;
            }
        }
        </style>
        <?php
    }
    
    /**
     * Aggiunge JavaScript per conferma azioni
     */
    public function add_button_scripts() {
        $screen = get_current_screen();
        
        if (!$screen || !in_array($screen->id, array('woocommerce_page_wc-orders', 'edit-shop_order'))) {
            return;
        }
        
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('.wosb-action-button').on('click', function(e) {
                var confirmMessage = $(this).data('confirm');
                if (confirmMessage && !confirm(confirmMessage)) {
                    e.preventDefault();
                    return false;
                }
            });
        });
        </script>
        <?php
    }

    /**
     * Handler AJAX per ottenere i pulsanti di un ordine
     */
    public function ajax_get_order_buttons() {
        // Verifica nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wosb_ajax')) {
            wp_die('Errore di sicurezza');
        }

        // Verifica permessi
        if (!current_user_can('edit_shop_orders')) {
            wp_die('Permessi insufficienti');
        }

        $order_id = intval($_POST['order_id']);
        $order = wc_get_order($order_id);

        if (!$order) {
            wp_send_json_error('Ordine non trovato');
        }

        // Genera i pulsanti
        ob_start();
        $this->render_order_action_buttons($order);
        $buttons_html = ob_get_clean();

        wp_send_json_success($buttons_html);
    }
}
