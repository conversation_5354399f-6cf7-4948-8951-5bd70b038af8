/**

 * Script per l'interfaccia di amministrazione del plugin WooCommerce Coupon Importer Exporter

 */

(function($) {

    'use strict';



    // Inizializza quando il DOM è pronto

    $(document).ready(function() {

        // Gestione delle tab

        $('.wccie-tabs-nav a').on('click', function(e) {

            e.preventDefault();

            

            // Rimuovi la classe active da tutte le tab

            $('.wccie-tabs-nav li').removeClass('active');

            $('.wccie-tab-content').removeClass('active');

            

            // Aggiungi la classe active alla tab corrente

            $(this).parent('li').addClass('active');

            

            // Mostra il contenuto della tab corrente

            var target = $(this).attr('href');

            $(target).addClass('active');

        });

        

        // Gestione del form di esportazione

        $('#wccie-export-form').on('submit', function(e) {

            e.preventDefault();

            

            // Mostra spinner

            var $spinner = $(this).find('.spinner');

            var $button = $(this).find('#wccie-export-button');

            var $result = $('#wccie-export-result');

            

            $spinner.addClass('is-active');

            $button.prop('disabled', true);

            $result.hide();

            

            // Raccogli i dati del form

            var formData = {

                action: 'wccie_export_coupons',

                nonce: wccie_params.export_coupons_nonce,

                include_usage_logs: $('input[name="include_usage_logs"]').is(':checked') ? 1 : 0,

                coupon_status: $('select[name="coupon_status"]').val()

            };

            

            // Invia la richiesta AJAX

            $.ajax({

                url: wccie_params.ajax_url,

                type: 'POST',

                data: formData,

                success: function(response) {

                    $spinner.removeClass('is-active');

                    $button.prop('disabled', false);

                    

                    if (response.success) {

                        // Mostra il messaggio di successo

                        $result.removeClass('error').html(

                            '<p>' + response.data.message + '</p>' +

                            '<p><a href="' + response.data.download_url + '" class="download-link" download>' +

                            'Scarica ' + response.data.filename +

                            '</a></p>'

                        ).show();

                        

                        // Avvia automaticamente il download

                        var link = document.createElement('a');

                        link.href = response.data.download_url;

                        link.download = response.data.filename;

                        link.style.display = 'none';

                        document.body.appendChild(link);

                        link.click();

                        document.body.removeChild(link);

                    } else {

                        // Mostra il messaggio di errore

                        $result.addClass('error').html('<p>' + response.data.message + '</p>').show();

                    }

                },

                error: function() {

                    $spinner.removeClass('is-active');

                    $button.prop('disabled', false);

                    $result.addClass('error').html('<p>' + wccie_params.i18n.export_error + '</p>').show();

                }

            });

        });

        

        // Gestione del form di importazione

        $('#wccie-import-form').on('submit', function(e) {

            e.preventDefault();

            

            // Verifica che sia stato selezionato un file

            var fileInput = $('#wccie-import-file')[0];

            if (fileInput.files.length === 0) {

                $('#wccie-import-result').addClass('error').html('<p>' + 'Seleziona un file CSV da importare.' + '</p>').show();

                return;

            }

            

            // Chiedi conferma prima di procedere

            if (!confirm(wccie_params.i18n.confirm_import)) {

                return;

            }

            

            // Mostra spinner

            var $spinner = $(this).find('.spinner');

            var $button = $(this).find('#wccie-import-button');

            var $result = $('#wccie-import-result');

            

            $spinner.addClass('is-active');

            $button.prop('disabled', true);

            $result.hide();

            

            // Prepara i dati del form

            var formData = new FormData(this);

            formData.append('action', 'wccie_import_coupons');

            formData.append('nonce', wccie_params.import_coupons_nonce);

            

            // Invia la richiesta AJAX

            $.ajax({

                url: wccie_params.ajax_url,

                type: 'POST',

                data: formData,

                processData: false,

                contentType: false,

                success: function(response) {

                    $spinner.removeClass('is-active');

                    $button.prop('disabled', false);

                    

                    if (response.success) {

                        // Mostra il messaggio di successo

                        $result.removeClass('error').html('<p>' + response.data.message + '</p>').show();

                        

                        // Resetta il form

                        $('#wccie-import-form')[0].reset();

                    } else {

                        // Mostra il messaggio di errore

                        var errorHtml = '<p>' + response.data.message + '</p>';

                        

                        // Aggiungi eventuali errori specifici

                        if (response.data.errors && response.data.errors.length > 0) {

                            errorHtml += '<div class="wccie-errors"><p>Errori:</p><ul>';

                            $.each(response.data.errors, function(index, error) {

                                errorHtml += '<li>' + error + '</li>';

                            });

                            errorHtml += '</ul></div>';

                        }

                        

                        $result.addClass('error').html(errorHtml).show();

                    }

                },

                error: function() {

                    $spinner.removeClass('is-active');

                    $button.prop('disabled', false);

                    $result.addClass('error').html('<p>' + wccie_params.i18n.import_error + '</p>').show();

                }

            });

        });

    });

})(jQuery); 