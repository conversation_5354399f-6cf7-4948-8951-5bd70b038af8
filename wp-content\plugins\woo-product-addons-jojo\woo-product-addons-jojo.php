<?php
/**
 * Plugin Name: Woo Product Addons Jojo
 * Plugin URI: https://example.com/woo-product-addons-jojo
 * Description: Permette di selezionare prodotti accessori da aggiungere al carrello insieme al prodotto principale.
 * Version: 1.0.0
 * Author: <PERSON>
 * Author URI: https://github.com/JoJoD3v
 * Text Domain: woo-product-addons-jojo
 * Domain Path: /languages
 * Requires at least: 5.6
 * Requires PHP: 7.2
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

// Definizione delle costanti
define('WPAJ_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WPAJ_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WPAJ_PLUGIN_VERSION', '1.0.0');

// Verifica che WooCommerce sia attivo
function wpaj_check_woocommerce_active() {
    if (!class_exists('WooCommerce')) {
        add_action('admin_notices', 'wpaj_woocommerce_missing_notice');
        return false;
    }
    return true;
}

// Avviso se WooCommerce non è attivo
function wpaj_woocommerce_missing_notice() {
    ?>
    <div class="error">
        <p><?php _e('Woo Product Addons Jojo richiede WooCommerce per funzionare correttamente.', 'woo-product-addons-jojo'); ?></p>
    </div>
    <?php
}

function enqueue_select2_jquery() {
    wp_register_style( 'select2css', '//cdnjs.cloudflare.com/ajax/libs/select2/3.4.8/select2.css', false, '1.0', 'all' );
    wp_register_script( 'select2', '//cdnjs.cloudflare.com/ajax/libs/select2/3.4.8/select2.js', array( 'jquery' ), '1.0', true );
    wp_enqueue_style( 'select2css' );
    wp_enqueue_script( 'select2' );
}
add_action( 'admin_enqueue_scripts', 'enqueue_select2_jquery' );
// Carica i file necessari se WooCommerce è attivo
function wpaj_init() {
    if (wpaj_check_woocommerce_active()) {
        // Include i file principali
        require_once WPAJ_PLUGIN_DIR . 'includes/class-wpaj-admin.php';
        require_once WPAJ_PLUGIN_DIR . 'includes/class-wpaj-frontend.php';
        require_once WPAJ_PLUGIN_DIR . 'includes/class-wpaj-groups.php';
        
        // Inizializza le classi
        $groups = new WPAJ_Groups();
        new WPAJ_Admin();
        new WPAJ_Frontend($groups);
        
        // Aggiungi hook per supportare la compatibilità con altri plugin (come wc-product-insurance)
        add_action('woocommerce_add_to_cart', 'wpaj_handle_add_to_cart', 10, 6);
        add_filter('woocommerce_add_cart_item_data', 'wpaj_add_cart_item_data', 10, 4);
        add_filter('woocommerce_get_cart_item_from_session', 'wpaj_get_cart_item_from_session', 10, 2);
        add_filter('woocommerce_cart_item_name', 'wpaj_cart_item_name', 10, 3);
    }
}
add_action('plugins_loaded', 'wpaj_init');

/**
 * Gestisce l'evento di aggiunta al carrello per migliorare la compatibilità con altri plugin
 *
 * @param string $cart_item_key Chiave dell'elemento nel carrello
 * @param int $product_id ID del prodotto
 * @param int $quantity Quantità
 * @param int $variation_id ID della variazione
 * @param array $variation Attributi della variazione
 * @param array $cart_item_data Dati aggiuntivi dell'elemento del carrello
 */
function wpaj_handle_add_to_cart($cart_item_key, $product_id, $quantity, $variation_id, $variation, $cart_item_data) {
    // Questo hook può essere usato per eseguire azioni dopo che un prodotto è stato aggiunto al carrello
    // Ad es. aggiornare metadati o modificare il carrello basandosi sugli accessori
    
    // Controlliamo se si tratta di un prodotto accessorio aggiunto tramite AJAX 
    // (evita le operazioni doppie, poiché il codice principale le esegue già)
    if (isset($cart_item_data['wpaj_accessory']) && $cart_item_data['wpaj_accessory'] === 'yes') {
        // Manteniamo traccia degli accessori anche nei metadati dell'ordine per compatibilità 
        // con la modalità di archiviazione degli ordini ad alte prestazioni di WooCommerce
        $main_product_key = isset($cart_item_data['wpaj_accessory_for']) ? $cart_item_data['wpaj_accessory_for'] : '';
        
        if (!empty($main_product_key) && isset(WC()->cart->cart_contents[$main_product_key])) {
            // Questo è per garantire la compatibilità con qualsiasi altro plugin che potrebbe 
            // lavorare con gli elementi del carrello
            do_action('wpaj_accessory_added_to_cart', $cart_item_key, $main_product_key, $product_id);
        }
    }
}

/**
 * Filtra i dati dell'elemento del carrello prima che venga aggiunto
 *
 * @param array $cart_item_data Dati dell'elemento del carrello
 * @param int $product_id ID del prodotto
 * @param int $variation_id ID della variazione
 * @param int $quantity Quantità
 * @return array Dati dell'elemento del carrello modificati
 */
function wpaj_add_cart_item_data($cart_item_data, $product_id, $variation_id, $quantity) {
    // Questo filtro permette di modificare i dati dell'elemento del carrello prima che venga aggiunto
    // Utile per garantire la compatibilità con altri plugin
    
    // Se i dati sono già stati impostati dal nostro plugin, li lasciamo intatti
    if (isset($cart_item_data['wpaj_main_product']) || isset($cart_item_data['wpaj_accessory'])) {
        return $cart_item_data;
    }
    
    // Si potrebbe identificare un prodotto accessorio aggiunto in modo non standard
    // e aggiungere i dati appropriati
    
    return $cart_item_data;
}

/**
 * Recupera i dati dell'elemento del carrello dalla sessione
 *
 * @param array $cart_item Elemento del carrello
 * @param array $values Valori dalla sessione
 * @return array Elemento del carrello modificato
 */
function wpaj_get_cart_item_from_session($cart_item, $values) {
    // Mantieni i dati del nostro plugin quando l'elemento del carrello viene recuperato dalla sessione
    if (isset($values['wpaj_main_product'])) {
        $cart_item['wpaj_main_product'] = $values['wpaj_main_product'];
    }
    
    if (isset($values['wpaj_accessory'])) {
        $cart_item['wpaj_accessory'] = $values['wpaj_accessory'];
    }
    
    if (isset($values['wpaj_accessory_for'])) {
        $cart_item['wpaj_accessory_for'] = $values['wpaj_accessory_for'];
    }
    
    return $cart_item;
}

/**
 * Modifica il nome dell'elemento del carrello per indicare che è un accessorio
 *
 * @param string $name Nome dell'elemento
 * @param array $cart_item Elemento del carrello
 * @param string $cart_item_key Chiave dell'elemento nel carrello
 * @return string Nome modificato
 */
function wpaj_cart_item_name($name, $cart_item, $cart_item_key) {
    // Se l'elemento è un accessorio, aggiungi un'indicazione al nome
    if (isset($cart_item['wpaj_accessory']) && $cart_item['wpaj_accessory'] === 'yes') {
        $name = sprintf(
            '%s <span class="wpaj-accessory-label">%s</span>',
            $name,
            __('(Accessorio)', 'woo-product-addons-jojo')
        );
    }
    
    return $name;
}

// Attivazione del plugin
register_activation_hook(__FILE__, 'wpaj_activate');
function wpaj_activate() {
    // Codice di attivazione se necessario
}

// Disattivazione del plugin
register_deactivation_hook(__FILE__, 'wpaj_deactivate');
function wpaj_deactivate() {
    // Codice di disattivazione se necessario
} 