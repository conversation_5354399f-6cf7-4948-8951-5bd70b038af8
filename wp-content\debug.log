[13-Aug-2025 15:16:53 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:16:53 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:16:53 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:16:55 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:16:55 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Aggiungendo pulsanti alla colonna azioni per ordine: 29037
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-refunded
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-refunded
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-refunded, Pulsanti trovati: 0
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:16:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:16:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:16:56 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => dashboard
    [has_focus] => false
)

[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:16:57 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:16:57 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:16:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:16:58 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:16:58 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:17:03 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:03 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:03 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:03 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:17:03 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:17:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:03 UTC] WAA: POST data: Array
(
    [_wpnonce] => 2b374eb690
    [_wp_http_referer] => /wp-admin/post.php?post=29037&action=edit
    [user_ID] => 1
    [action] => editpost
    [originalaction] => editpost
    [post_author] => 1
    [post_type] => shop_order
    [original_post_status] => wc-refunded
    [referredby] => http://test.test/wp-admin/edit.php?post_type=shop_order
    [_wp_original_http_referer] => http://test.test/wp-admin/edit.php?post_type=shop_order
    [post_ID] => 29037
    [meta-box-order-nonce] => e33866cbd9
    [closedpostboxesnonce] => 935da108e9
    [original_post_title] => Order &ndash; Agosto 13, 2025 @ 04:00 PM
    [post_title] => Ordine
    [samplepermalinknonce] => e7300fba31
    [wc_order_action] => 
    [save] => Aggiorna
    [order_note] => 
    [order_note_type] => 
    [woocommerce_meta_nonce] => 956ffdfb92
    [post_status] => refunded
    [order_date] => 2025-08-13
    [order_date_hour] => 16
    [order_date_minute] => 00
    [order_date_second] => 56
    [order_status] => wc-processing
    [customer_user] => 1
    [_billing_first_name] => Giovanni
    [_billing_last_name] => Castaldo
    [_billing_company] => 
    [_billing_address_1] => via Giotto 13
    [_billing_address_2] => 
    [_billing_city] => Caserta
    [_billing_postcode] => 81100
    [_billing_country] => IT
    [_billing_state] => CE
    [_billing_email] => <EMAIL>
    [_billing_phone] => 
    [_payment_method] => bacs
    [_transaction_id] => 
    [_shipping_first_name] => Giovanni
    [_shipping_last_name] => Castaldo
    [_shipping_company] => 
    [_shipping_address_1] => via Giotto 13
    [_shipping_address_2] => 
    [_shipping_city] => Caserta
    [_shipping_postcode] => 81100
    [_shipping_country] => IT
    [_shipping_state] => CE
    [_shipping_phone] => 
    [customer_note] => 
    [order_item_id] => Array
        (
            [0] => 8
        )

    [order_item_tax_class] => Array
        (
            [8] => 
        )

    [order_item_qty] => Array
        (
            [8] => 2
        )

    [refund_order_item_qty] => Array
        (
            [8] => 
        )

    [line_subtotal] => Array
        (
            [8] => 858
        )

    [line_total] => Array
        (
            [8] => 858
        )

    [refund_line_total] => Array
        (
            [8] => 
        )

    [order_refund_id] => Array
        (
            [0] => 29038
        )

    [restock_refunded_items] => on
    [refund_amount] => 
    [refund_reason] => 
    [refunded_amount] => 858
    [meta] => Array
        (
            [3949] => Array
                (
                    [key] => is_vat_exempt
                    [value] => no
                )

        )

    [_ajax_nonce] => 7a1c696d40
    [metakeyselect] => #NONE#
    [metakeyinput] => 
    [metavalue] => 
    [_ajax_nonce-add-meta] => cb5e5ef417
)

[13-Aug-2025 15:17:04 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:04 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:04 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:05 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:17:05 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:06 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:06 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 2b374eb690
    [post_ID] => 29037
    [active_post_lock] => 1755098218:1
)

[13-Aug-2025 15:17:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:08 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:17:08 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Aggiungendo pulsanti alla colonna azioni per ordine: 29037
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 1
[13-Aug-2025 15:17:08 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 1
[13-Aug-2025 15:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:09 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:09 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:19 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:19 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:17:29 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:29 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:29 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:29 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:29 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:17:40 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:40 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:40 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:41 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:41 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:17:50 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:17:50 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:17:50 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:17:52 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:17:52 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:18:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:18:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:18:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:18:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:18:07 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:18:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:18:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:18:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:18:20 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:18:20 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:18:56 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:18:56 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:18:56 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:18:58 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:18:58 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => dashboard
    [has_focus] => false
)

[13-Aug-2025 15:19:51 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:19:51 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:19:51 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:19:53 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:19:53 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:20:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:20:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:20:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:20:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:20:07 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:20:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:20:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:20:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:20:22 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:20:22 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:21:52 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:21:52 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:21:52 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:21:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:21:54 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:22:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:22:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:22:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:22:07 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:22:07 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:23:53 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:23:53 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:23:53 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:23:55 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:23:55 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:24:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:08 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:24:08 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:24:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:34 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:24:34 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:35 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:24:35 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 1
[13-Aug-2025 15:24:35 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 1
[13-Aug-2025 15:24:37 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:37 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:37 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:38 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:24:38 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:24:42 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:42 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:42 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:43 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:24:43 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:24:46 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:46 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:46 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:24:47 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:24:47 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:24:59 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:24:59 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:24:59 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:01 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:25:01 UTC] WAA: POST data: Array
(
    [wosb_nonce] => 264146886c
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-order-status-buttons&action=edit&button_id=2
    [action] => wosb_save_button
    [button_id] => 2
    [button_name] => Rimborsato
    [button_description] => rimborsa
    [order_statuses] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [target_status] => wc-refunded
    [dashicon_code] => \\f476
    [email_content] => test
)

[13-Aug-2025 15:25:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:02 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:02 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:07 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:07 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 1
[13-Aug-2025 15:25:07 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 1
[13-Aug-2025 15:25:10 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:10 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:10 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:25:11 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:13 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:13 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 1
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 1
[13-Aug-2025 15:25:13 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 1
[13-Aug-2025 15:25:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:17 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:25:17 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:25:17 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:17 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:21 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:21 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:21 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:21 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:21 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:25:33 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:33 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:33 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:34 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:25:34 UTC] WAA: POST data: Array
(
    [wosb_nonce] => 264146886c
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-order-status-buttons&action=edit&button_id=2
    [action] => wosb_save_button
    [button_id] => 2
    [button_name] => Rimborsato
    [button_description] => rimborsa
    [order_statuses] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [target_status] => wc-refunded
    [dashicon_code] => \\f528
    [email_content] => test
)

[13-Aug-2025 15:25:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:25:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:25:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:25:35 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:25:35 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:26:07 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:07 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:07 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:08 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:08 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:08 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:08 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:26:08 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:26:09 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:26:09 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:26:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:23 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:26:23 UTC] WAA: POST data: Array
(
    [wosb_nonce] => 264146886c
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-order-status-buttons&action=add
    [action] => wosb_save_button
    [button_name] => Altro
    [button_description] => alslsls
    [order_statuses] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [target_status] => wc-failed
    [dashicon_code] => \\f528
    [email_content] => dqdqwdqwdqw
)

[13-Aug-2025 15:26:24 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:24 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:24 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:24 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:26:24 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:26:28 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:28 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:28 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:29 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:26:29 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-processing
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[13-Aug-2025 15:26:29 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 2
[13-Aug-2025 15:26:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:32 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:26:32 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:26:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:26:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:26:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:26:42 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:26:42 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:28:09 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:28:09 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:28:09 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:28:11 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:28:11 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:28:42 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:28:42 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:28:42 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:28:43 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:28:43 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:29:00 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:00 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:00 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:01 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:01 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:01 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:02 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:02 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:02 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:02 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:06 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:06 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:06 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:06 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:06 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:16 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:16 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:16 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:16 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:16 UTC] WAA: POST data: Array
(
    [wosb_nonce] => 264146886c
    [_wp_http_referer] => /wp-admin/admin.php?page=woo-order-status-buttons&action=edit&button_id=3
    [action] => wosb_save_button
    [button_id] => 3
    [button_name] => Altro
    [button_description] => alslsls
    [order_statuses] => Array
        (
            [0] => wc-pending
            [1] => wc-processing
            [2] => wc-on-hold
        )

    [target_status] => wc-failed
    [dashicon_code] => \\f528
    [email_content] => dqdqwdqwdqw
)

[13-Aug-2025 15:29:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:17 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:17 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:20 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:20 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-processing
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-processing
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-processing
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[13-Aug-2025 15:29:21 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-processing, Pulsanti trovati: 2
[13-Aug-2025 15:29:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:24 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:24 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:31 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:31 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:31 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:31 UTC] [WooCommerce Pulsanti Status Ordine] Email inviata con successo per l'ordine #29037
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:32 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:33 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:33 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:33 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:33 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-failed
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro NO MATCH per status wc-failed
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato NO MATCH per status wc-failed
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 0
[13-Aug-2025 15:29:33 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-failed, Pulsanti trovati: 0
[13-Aug-2025 15:29:35 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:35 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:35 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:36 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:36 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:45 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:45 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:45 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:47 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:47 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:47 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:47 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:47 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-refresh-post-nonces] => Array
                (
                    [post_id] => 29037
                )

        )

    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => true
)

[13-Aug-2025 15:29:48 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:48 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:48 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:49 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:49 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:49 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:49 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:49 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:51 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:51 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 2b374eb690
    [post_ID] => 29037
    [active_post_lock] => 1755098225:1
)

[13-Aug-2025 15:29:54 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:54 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:54 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:54 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:54 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:54 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:54 UTC] WAA: POST data: Array
(
    [_wpnonce] => 2b374eb690
    [_wp_http_referer] => /wp-admin/post.php?post=29037&action=edit
    [user_ID] => 1
    [action] => editpost
    [originalaction] => editpost
    [post_author] => 1
    [post_type] => shop_order
    [original_post_status] => wc-failed
    [referredby] => 
    [_wp_original_http_referer] => 
    [post_ID] => 29037
    [meta-box-order-nonce] => e33866cbd9
    [closedpostboxesnonce] => 935da108e9
    [original_post_title] => Order &ndash; Agosto 13, 2025 @ 04:00 PM
    [post_title] => Ordine
    [samplepermalinknonce] => e7300fba31
    [wc_order_action] => 
    [save] => Aggiorna
    [order_note] => 
    [order_note_type] => 
    [woocommerce_meta_nonce] => 956ffdfb92
    [post_status] => failed
    [order_date] => 2025-08-13
    [order_date_hour] => 16
    [order_date_minute] => 00
    [order_date_second] => 56
    [order_status] => wc-on-hold
    [customer_user] => 1
    [_billing_first_name] => Giovanni
    [_billing_last_name] => Castaldo
    [_billing_company] => 
    [_billing_address_1] => via Giotto 13
    [_billing_address_2] => 
    [_billing_city] => Caserta
    [_billing_postcode] => 81100
    [_billing_country] => IT
    [_billing_state] => CE
    [_billing_email] => <EMAIL>
    [_billing_phone] => 
    [_payment_method] => bacs
    [_transaction_id] => 
    [_shipping_first_name] => Giovanni
    [_shipping_last_name] => Castaldo
    [_shipping_company] => 
    [_shipping_address_1] => via Giotto 13
    [_shipping_address_2] => 
    [_shipping_city] => Caserta
    [_shipping_postcode] => 81100
    [_shipping_country] => IT
    [_shipping_state] => CE
    [_shipping_phone] => 
    [customer_note] => 
    [order_item_id] => Array
        (
            [0] => 8
        )

    [order_item_tax_class] => Array
        (
            [8] => 
        )

    [order_item_qty] => Array
        (
            [8] => 2
        )

    [refund_order_item_qty] => Array
        (
            [8] => 
        )

    [line_subtotal] => Array
        (
            [8] => 858
        )

    [line_total] => Array
        (
            [8] => 858
        )

    [refund_line_total] => Array
        (
            [8] => 
        )

    [order_refund_id] => Array
        (
            [0] => 29038
        )

    [restock_refunded_items] => on
    [refund_amount] => 
    [refund_reason] => 
    [refunded_amount] => 858
    [meta] => Array
        (
            [3949] => Array
                (
                    [key] => is_vat_exempt
                    [value] => no
                )

        )

    [_ajax_nonce] => 7a1c696d40
    [metakeyselect] => #NONE#
    [metakeyinput] => 
    [metavalue] => 
    [_ajax_nonce-add-meta] => cb5e5ef417
)

[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:55 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:56 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:56 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => false
)

[13-Aug-2025 15:29:56 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:29:56 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:29:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:29:58 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:29:58 UTC] WAA: POST data: Array
(
    [action] => wp-remove-post-lock
    [_wpnonce] => 2b374eb690
    [post_ID] => 29037
    [active_post_lock] => 1755098989:1
)

[13-Aug-2025 15:29:58 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:29:58 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:29:58 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:00 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:00 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:00 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:01 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:01 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-on-hold
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-on-hold
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-on-hold
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[13-Aug-2025 15:30:01 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-on-hold, Pulsanti trovati: 2
[13-Aug-2025 15:30:02 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:02 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:02 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:03 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:03 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:30:05 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:05 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:05 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:05 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:05 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:13 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:13 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:13 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:14 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:14 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:15 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:15 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:15 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:15 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:15 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:15 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:19 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:19 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:19 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:20 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:20 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:20 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:20 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:20 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:20 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:21 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:21 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:21 UTC] WAA: POST data: Array
(
)

[13-Aug-2025 15:30:23 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:23 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:23 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:23 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:23 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:23 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Cercando pulsanti per status: wc-on-hold
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti totali nel database: 2
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Altro, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Altro MATCH per status wc-on-hold
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante: Rimborsato, Status configurati: Array
(
    [0] => wc-pending
    [1] => wc-processing
    [2] => wc-on-hold
)

[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsante Rimborsato MATCH per status wc-on-hold
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Pulsanti filtrati: 2
[13-Aug-2025 15:30:24 UTC] [WooCommerce Pulsanti Status Ordine] Ordine ID: 29037, Status: wc-on-hold, Pulsanti trovati: 2
[13-Aug-2025 15:30:24 UTC] WEAB: Added 5 buttons for order 29037
[13-Aug-2025 15:30:25 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:25 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:25 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:25 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:25 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:25 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:30:34 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:34 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:34 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:35 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:35 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:35 UTC] WAA: POST data: Array
(
    [data] => Array
        (
            [wp-check-locked-posts] => Array
                (
                    [0] => post-29037
                )

        )

    [interval] => 10
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => edit-shop_order
    [has_focus] => true
)

[13-Aug-2025 15:30:41 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:41 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:41 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:42 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:42 UTC] PHP Deprecated:  strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 7360
[13-Aug-2025 15:30:42 UTC] PHP Deprecated:  str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\laragon\www\test\wp-includes\functions.php on line 2195
[13-Aug-2025 15:30:57 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:30:57 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:30:57 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:30:59 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:30:59 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:30:59 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => shop_order
    [has_focus] => false
)

[13-Aug-2025 15:31:17 UTC] [WooCommerce Pulsanti Status Ordine] Setup hooks chiamato
[13-Aug-2025 15:31:17 UTC] [WooCommerce Pulsanti Status Ordine] Registrando hook legacy per shop_order
[13-Aug-2025 15:31:17 UTC] [WooCommerce Pulsanti Status Ordine] Hook registrati completati
[13-Aug-2025 15:31:18 UTC] WooCommerce Email Action Buttons - Hook priority forced to 9999
[13-Aug-2025 15:31:18 UTC] WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms
[13-Aug-2025 15:31:18 UTC] WAA: POST data: Array
(
    [interval] => 60
    [_nonce] => 6eb43b6fa4
    [action] => heartbeat
    [screen_id] => plugins
    [has_focus] => false
)

