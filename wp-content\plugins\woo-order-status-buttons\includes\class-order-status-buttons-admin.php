<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe per la gestione dell'interfaccia admin
 */
class Order_Status_Buttons_Admin {
    
    private $db;
    
    public function __construct() {
        $this->db = new Order_Status_Buttons_DB();
        
        // Aggiungi menu admin
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Registra assets admin
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        
        // Gestisci le azioni del form
        add_action('admin_post_wosb_save_button', array($this, 'handle_save_button'));
        add_action('admin_post_wosb_delete_button', array($this, 'handle_delete_button'));
        
        // Aggiungi notice per feedback utente
        add_action('admin_notices', array($this, 'show_admin_notices'));
    }
    
    /**
     * Aggiunge il menu admin
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            'Pulsanti Status Ordine',
            'Pulsanti Status Ordine',
            'manage_woocommerce',
            'woo-order-status-buttons',
            array($this, 'render_admin_page')
        );
    }
    
    /**
     * Carica gli assets admin
     */
    public function enqueue_admin_assets($hook) {
        if ('woocommerce_page_woo-order-status-buttons' !== $hook) {
            return;
        }
        
        wp_enqueue_style(
            'wosb-admin-style',
            WOSB_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            WOSB_VERSION
        );
        
        wp_enqueue_script(
            'wosb-admin-script',
            WOSB_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            WOSB_VERSION,
            true
        );
        
        // Aggiungi editor per il contenuto email
        wp_enqueue_editor();
    }
    
    /**
     * Renderizza la pagina admin principale
     */
    public function render_admin_page() {
        if (!wosb_user_can_manage()) {
            wp_die('Non hai i permessi per accedere a questa pagina.');
        }
        
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';
        $button_id = isset($_GET['button_id']) ? intval($_GET['button_id']) : 0;
        
        echo '<div class="wrap">';
        echo '<h1>Pulsanti Status Ordine</h1>';
        
        switch ($action) {
            case 'add':
                $this->render_button_form();
                break;
            case 'edit':
                $this->render_button_form($button_id);
                break;
            default:
                $this->render_buttons_list();
                break;
        }
        
        echo '</div>';
    }
    
    /**
     * Renderizza la lista dei pulsanti
     */
    private function render_buttons_list() {
        $buttons = $this->db->get_all_buttons();
        
        echo '<div class="wosb-buttons-list">';
        echo '<div class="tablenav top">';
        echo '<div class="alignleft actions">';
        echo '<a href="' . admin_url('admin.php?page=woo-order-status-buttons&action=add') . '" class="button button-primary">Aggiungi Nuovo Pulsante</a>';
        echo '</div>';
        echo '</div>';
        
        if (empty($buttons)) {
            echo '<div class="notice notice-info"><p>Nessun pulsante configurato. <a href="' . admin_url('admin.php?page=woo-order-status-buttons&action=add') . '">Aggiungi il primo pulsante</a>.</p></div>';
        } else {
            include WOSB_PLUGIN_DIR . 'templates/admin-buttons-list.php';
        }
        
        echo '</div>';
    }
    
    /**
     * Renderizza il form per aggiungere/modificare un pulsante
     */
    private function render_button_form($button_id = 0) {
        $button = null;
        $is_edit = false;
        
        if ($button_id > 0) {
            $button = $this->db->get_button($button_id);
            $is_edit = true;
            
            if (!$button) {
                echo '<div class="notice notice-error"><p>Pulsante non trovato.</p></div>';
                return;
            }
        }
        
        $order_statuses = wosb_get_order_statuses();
        
        include WOSB_PLUGIN_DIR . 'templates/admin-button-form.php';
    }
    
    /**
     * Gestisce il salvataggio del pulsante
     */
    public function handle_save_button() {
        if (!wosb_user_can_manage()) {
            wp_die('Non hai i permessi per eseguire questa azione.');
        }
        
        // Verifica nonce
        if (!wp_verify_nonce($_POST['wosb_nonce'], 'wosb_save_button')) {
            wp_die('Errore di sicurezza. Riprova.');
        }
        
        $button_id = isset($_POST['button_id']) ? intval($_POST['button_id']) : 0;
        $is_edit = $button_id > 0;
        
        // Validazione dati
        $errors = $this->validate_button_data($_POST);
        
        if (!empty($errors)) {
            $error_message = implode('<br>', $errors);
            wp_redirect(add_query_arg(array(
                'page' => 'woo-order-status-buttons',
                'action' => $is_edit ? 'edit' : 'add',
                'button_id' => $button_id,
                'error' => urlencode($error_message)
            ), admin_url('admin.php')));
            exit;
        }
        
        // Prepara i dati
        $data = array(
            'button_name' => $_POST['button_name'],
            'button_description' => $_POST['button_description'],
            'order_statuses' => isset($_POST['order_statuses']) ? $_POST['order_statuses'] : array(),
            'target_status' => $_POST['target_status'],
            'email_content' => $_POST['email_content'],
            'dashicon_code' => $_POST['dashicon_code']
        );
        
        // Salva nel database
        if ($is_edit) {
            $result = $this->db->update_button($button_id, $data);
            $success_message = 'Pulsante aggiornato con successo.';
        } else {
            $result = $this->db->insert_button($data);
            $success_message = 'Pulsante creato con successo.';
        }
        
        if ($result !== false) {
            wp_redirect(add_query_arg(array(
                'page' => 'woo-order-status-buttons',
                'success' => urlencode($success_message)
            ), admin_url('admin.php')));
        } else {
            wp_redirect(add_query_arg(array(
                'page' => 'woo-order-status-buttons',
                'action' => $is_edit ? 'edit' : 'add',
                'button_id' => $button_id,
                'error' => urlencode('Errore nel salvataggio del pulsante.')
            ), admin_url('admin.php')));
        }
        
        exit;
    }
    
    /**
     * Gestisce l'eliminazione del pulsante
     */
    public function handle_delete_button() {
        if (!wosb_user_can_manage()) {
            wp_die('Non hai i permessi per eseguire questa azione.');
        }
        
        // Verifica nonce
        if (!wp_verify_nonce($_GET['wosb_nonce'], 'wosb_delete_button')) {
            wp_die('Errore di sicurezza. Riprova.');
        }
        
        $button_id = isset($_GET['button_id']) ? intval($_GET['button_id']) : 0;
        
        if ($button_id > 0) {
            $result = $this->db->delete_button($button_id);
            
            if ($result !== false) {
                $message = 'Pulsante eliminato con successo.';
                $type = 'success';
            } else {
                $message = 'Errore nell\'eliminazione del pulsante.';
                $type = 'error';
            }
        } else {
            $message = 'ID pulsante non valido.';
            $type = 'error';
        }
        
        wp_redirect(add_query_arg(array(
            'page' => 'woo-order-status-buttons',
            $type => urlencode($message)
        ), admin_url('admin.php')));
        
        exit;
    }
    
    /**
     * Valida i dati del pulsante
     */
    private function validate_button_data($data) {
        $errors = array();
        
        if (empty($data['button_name'])) {
            $errors[] = 'Il nome del pulsante è obbligatorio.';
        }
        
        if (empty($data['target_status'])) {
            $errors[] = 'Lo status di destinazione è obbligatorio.';
        }
        
        if (empty($data['order_statuses']) || !is_array($data['order_statuses'])) {
            $errors[] = 'Seleziona almeno uno status degli ordini.';
        }
        
        if (empty($data['email_content'])) {
            $errors[] = 'Il contenuto dell\'email è obbligatorio.';
        }
        
        return $errors;
    }
    
    /**
     * Mostra i messaggi admin
     */
    public function show_admin_notices() {
        if (isset($_GET['page']) && $_GET['page'] === 'woo-order-status-buttons') {
            if (isset($_GET['success'])) {
                echo '<div class="notice notice-success is-dismissible"><p>' . esc_html(urldecode($_GET['success'])) . '</p></div>';
            }
            
            if (isset($_GET['error'])) {
                echo '<div class="notice notice-error is-dismissible"><p>' . esc_html(urldecode($_GET['error'])) . '</p></div>';
            }
        }
    }
}
