<?php
if (!defined('ABSPATH')) {
    exit;
}

// Valori di default
$button_name = $is_edit && $button ? $button->button_name : '';
$button_description = $is_edit && $button ? $button->button_description : '';
$selected_order_statuses = $is_edit && $button ? $button->order_statuses : array();
$target_status = $is_edit && $button ? $button->target_status : '';
$email_content = $is_edit && $button ? $button->email_content : '';
$dashicon_code = $is_edit && $button ? $button->dashicon_code : '\f147';

// Assicurati che selected_order_statuses sia un array
if (!is_array($selected_order_statuses)) {
    $selected_order_statuses = array();
}
?>

<div class="wosb-form-container">
    <h2><?php echo $is_edit ? 'Modifica Pulsante' : 'Aggiungi Nuovo Pulsante'; ?></h2>
    
    <form method="post" action="<?php echo admin_url('admin-post.php'); ?>" class="wosb-button-form">
        <?php wp_nonce_field('wosb_save_button', 'wosb_nonce'); ?>
        <input type="hidden" name="action" value="wosb_save_button">
        <?php if ($is_edit): ?>
            <input type="hidden" name="button_id" value="<?php echo $button->id; ?>">
        <?php endif; ?>
        
        <table class="form-table">
            <tbody>
                <!-- Nome Pulsante -->
                <tr>
                    <th scope="row">
                        <label for="button_name">Nome Pulsante *</label>
                    </th>
                    <td>
                        <input type="text" 
                               id="button_name" 
                               name="button_name" 
                               value="<?php echo esc_attr($button_name); ?>" 
                               class="regular-text" 
                               required>
                        <p class="description">Il nome che apparirà sul pulsante nella lista ordini.</p>
                    </td>
                </tr>
                
                <!-- Descrizione -->
                <tr>
                    <th scope="row">
                        <label for="button_description">Descrizione</label>
                    </th>
                    <td>
                        <textarea id="button_description" 
                                  name="button_description" 
                                  rows="3" 
                                  class="large-text"><?php echo esc_textarea($button_description); ?></textarea>
                        <p class="description">Descrizione opzionale del pulsante per uso interno.</p>
                    </td>
                </tr>
                
                <!-- Status degli Ordini -->
                <tr>
                    <th scope="row">
                        <label for="order_statuses">Status degli Ordini *</label>
                    </th>
                    <td>
                        <fieldset>
                            <legend class="screen-reader-text">Seleziona gli status degli ordini</legend>
                            <?php foreach ($order_statuses as $status_key => $status_name): ?>
                                <label>
                                    <input type="checkbox" 
                                           name="order_statuses[]" 
                                           value="<?php echo esc_attr($status_key); ?>"
                                           <?php checked(in_array($status_key, $selected_order_statuses)); ?>>
                                    <?php echo esc_html($status_name); ?>
                                </label><br>
                            <?php endforeach; ?>
                        </fieldset>
                        <p class="description">Seleziona per quali status degli ordini questo pulsante deve apparire.</p>
                    </td>
                </tr>
                
                <!-- Status di Destinazione -->
                <tr>
                    <th scope="row">
                        <label for="target_status">Status di Destinazione *</label>
                    </th>
                    <td>
                        <select id="target_status" name="target_status" class="regular-text" required>
                            <option value="">Seleziona status di destinazione...</option>
                            <?php foreach ($order_statuses as $status_key => $status_name): ?>
                                <option value="<?php echo esc_attr($status_key); ?>" 
                                        <?php selected($target_status, $status_key); ?>>
                                    <?php echo esc_html($status_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description">Lo status a cui l'ordine verrà cambiato quando si clicca il pulsante.</p>
                    </td>
                </tr>
                
                <!-- Codice Unicode Dashicon -->
                <tr>
                    <th scope="row">
                        <label for="dashicon_code">Codice Unicode Dashicon</label>
                    </th>
                    <td>
                        <input type="text" 
                               id="dashicon_code" 
                               name="dashicon_code" 
                               value="<?php echo esc_attr($dashicon_code); ?>" 
                               class="regular-text"
                               placeholder="\f147">
                        <span class="dashicons wosb-icon-preview" id="icon-preview"></span>
                        <p class="description">
                            Codice unicode del Dashicon (es. \f147 per admin-post). 
                            <a href="https://developer.wordpress.org/resource/dashicons/" target="_blank">Vedi tutti i Dashicons</a>
                        </p>
                    </td>
                </tr>
                
                <!-- Testo Email -->
                <tr>
                    <th scope="row">
                        <label for="email_content">Testo Email *</label>
                    </th>
                    <td>
                        <?php
                        wp_editor($email_content, 'email_content', array(
                            'textarea_name' => 'email_content',
                            'textarea_rows' => 10,
                            'media_buttons' => false,
                            'teeny' => true,
                            'quicktags' => true
                        ));
                        ?>
                        <p class="description">
                            <strong>Placeholder disponibili:</strong><br>
                            <code>{order_number}</code> - Numero ordine<br>
                            <code>{customer_name}</code> - Nome completo cliente<br>
                            <code>{customer_first_name}</code> - Nome cliente<br>
                            <code>{order_date}</code> - Data ordine<br>
                            <code>{order_total}</code> - Totale ordine<br>
                            <code>{order_status}</code> - Status attuale ordine<br>
                            <code>{new_status}</code> - Nuovo status ordine<br>
                            <code>{site_name}</code> - Nome sito<br>
                            <code>{order_details}</code> - Dettagli completi ordine<br>
                            <code>{billing_address}</code> - Indirizzo fatturazione<br>
                            <code>{shipping_address}</code> - Indirizzo spedizione
                        </p>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <p class="submit">
            <input type="submit" 
                   name="submit" 
                   id="submit" 
                   class="button button-primary" 
                   value="<?php echo $is_edit ? 'Aggiorna Pulsante' : 'Crea Pulsante'; ?>">
            
            <a href="<?php echo admin_url('admin.php?page=woo-order-status-buttons'); ?>" 
               class="button button-secondary">
                Annulla
            </a>
        </p>
    </form>
</div>

<style>
.wosb-form-container {
    max-width: 800px;
}

.wosb-button-form .form-table th {
    width: 200px;
    vertical-align: top;
    padding-top: 15px;
}

.wosb-button-form fieldset label {
    display: block;
    margin-bottom: 5px;
}

.wosb-icon-preview {
    margin-left: 10px;
    font-size: 20px;
    color: #0073aa;
}

.wosb-placeholder-help {
    background: #f9f9f9;
    border: 1px solid #ddd;
    padding: 10px;
    margin-top: 10px;
    border-radius: 3px;
}

.wosb-placeholder-help code {
    background: #fff;
    padding: 2px 4px;
    border-radius: 2px;
    margin-right: 5px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Aggiorna anteprima icona
    function updateIconPreview() {
        var code = $('#dashicon_code').val();
        if (code) {
            // Rimuovi \f se presente e aggiungi il prefixo corretto
            var cleanCode = code.replace('\\f', '');
            $('#icon-preview').css('content', '"\\f' + cleanCode + '"');
        }
    }
    
    // Aggiorna anteprima al caricamento
    updateIconPreview();
    
    // Aggiorna anteprima quando cambia il valore
    $('#dashicon_code').on('input', updateIconPreview);
    
    // Validazione form
    $('.wosb-button-form').on('submit', function(e) {
        var orderStatuses = $('input[name="order_statuses[]"]:checked').length;
        var targetStatus = $('#target_status').val();
        var emailContent = $('#email_content').val();
        
        if (orderStatuses === 0) {
            alert('Seleziona almeno uno status degli ordini.');
            e.preventDefault();
            return false;
        }
        
        if (!targetStatus) {
            alert('Seleziona uno status di destinazione.');
            e.preventDefault();
            return false;
        }
        
        if (!emailContent.trim()) {
            alert('Il contenuto dell\'email è obbligatorio.');
            e.preventDefault();
            return false;
        }
    });
    
    // Helper per inserire placeholder
    $('.wosb-insert-placeholder').on('click', function(e) {
        e.preventDefault();
        var placeholder = $(this).data('placeholder');
        var editor = tinymce.get('email_content');
        
        if (editor && !editor.isHidden()) {
            editor.insertContent(placeholder);
        } else {
            var textarea = $('#email_content');
            var cursorPos = textarea.prop('selectionStart');
            var textBefore = textarea.val().substring(0, cursorPos);
            var textAfter = textarea.val().substring(cursorPos);
            textarea.val(textBefore + placeholder + textAfter);
        }
    });
});
</script>
