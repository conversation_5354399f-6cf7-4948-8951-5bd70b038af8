# WooCommerce Pagamento a Rate

Plugin per WooCommerce che aggiunge un metodo di pagamento "Pagamento a Rate".

## Descrizione

Questo plugin aggiunge un nuovo metodo di pagamento a WooCommerce chiamato "Pagamento a Rate". Quando un cliente sceglie questo metodo di pagamento al checkout, l'ordine viene impostato come "In Sospeso" e viene inviata un'email automatica al cliente con i dettagli dell'ordine e le informazioni sul pagamento a rate.

## Caratteristiche

- Aggiunge un metodo di pagamento "Pagamento a Rate" al checkout di WooCommerce
- Configura automaticamente lo status dell'ordine come "In Sospeso"
- Invia un'email al cliente con i dettagli dell'ordine
- Impostazioni personalizzabili dal pannello amministrativo di WooCommerce

## Installazione

1. Carica la cartella `woo-pagamento-rate` nella directory `/wp-content/plugins/` del tuo sito WordPress
2. Attiva il plugin attraverso il menu 'Plugin' in WordPress
3. Vai a WooCommerce > Impostazioni > Pagamenti per configurare il metodo di pagamento

## Requisiti

- WordPress 5.0 o superiore
- WooCommerce 4.0 o superiore
- PHP 7.2 o superiore

## Personalizzazione

### Template Email

I template delle email possono essere personalizzati copiandoli nel tuo tema:

- Copia `templates/emails/wpr-payment-email.php` in `{tuo-tema}/woocommerce/emails/wpr-payment-email.php`
- Copia `templates/emails/plain/wpr-payment-email.php` in `{tuo-tema}/woocommerce/emails/plain/wpr-payment-email.php`

## Supporto

Per segnalare bug o richiedere funzionalità, contattare lo sviluppatore. 