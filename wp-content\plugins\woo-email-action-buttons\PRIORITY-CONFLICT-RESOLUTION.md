# Sistema di Priorità e Risoluzione Conflitti Plugin

## Problema Risolto

**Problema Originale:**
- I pulsanti email action non apparivano nel sito live a causa di conflitti con altri plugin
- Altri plugin/temi con priorità più alta sovrascrivevan le azioni ordini WooCommerce
- CSS di altri plugin nascondeva o sovrascriveva i nostri pulsanti

**Soluzione Implementata:**
Sistema multi-livello di priorità massima e rilevamento conflitti automatico.

## Strategie di Risoluzione Implementate

### 1. 🚀 **Priorità Hook Massima**

**Prima:**
```php
add_filter('woocommerce_admin_order_actions', array($this, 'add_order_actions'), 10, 2);
```

**Dopo:**
```php
// Priorità iniziale molto alta
add_filter('woocommerce_admin_order_actions', array($this, 'add_order_actions'), 999, 2);

// Forza priorità massima dopo caricamento altri plugin
add_action('wp_loaded', array($this, 'force_hook_priority'), 999);
// → Re-registra con priorità 9999

// Hook di emergenza se altri plugin rimuovono il nostro
add_action('current_screen', array($this, 'emergency_hook_registration'), 9999);
// → Re-registra con priorità 99999 se necessario
```

### 2. 🎨 **CSS con Specificità Massima**

**CSS Standard (admin_head):**
```css
.wc-action-button-weab_button_1::after {
    font-family: Dashicons !important;
    content: '\f465' !important;
}
```

**CSS Override (admin_footer):**
```css
body.wp-admin .wp-list-table .wc-action-button-weab_button_1::after,
.wp-admin #wpbody-content .wc-action-button-weab_button_1::after,
table.wp-list-table .wc-action-button-weab_button_1::after {
    font-family: Dashicons !important;
    content: '\f465' !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}
```

### 3. 🔍 **Rilevamento Conflitti Automatico**

**Sistema di Monitoraggio:**
```php
public function detect_plugin_conflicts() {
    global $wp_filter;
    
    // Analizza tutti i callback registrati su 'woocommerce_admin_order_actions'
    // Rileva plugin con priorità alta che potrebbero interferire
    // Salva informazioni per visualizzazione admin
}
```

**Tipi di Conflitti Rilevati:**
- **Alta Priorità (≥999)**: Plugin che potrebbero sovrascrivere i nostri
- **Media Priorità (>10)**: Plugin che modificano le azioni ma dovrebbero essere compatibili

### 4. 🛡️ **Protezione Aggressiva dei Pulsanti**

**Override Aggressivo:**
```php
public function add_order_actions($actions, $order) {
    // 1. Rimuovi eventuali duplicati
    if (isset($actions[$action_key])) {
        unset($actions[$action_key]);
    }
    
    // 2. Aggiungi il nostro pulsante
    $actions[$action_key] = array(/* ... */);
    
    // 3. Forza l'esistenza se altri plugin li rimuovono
    $this->ensure_our_buttons_exist($actions, $buttons, $order);
}
```

### 5. 🚨 **Sistema di Emergenza JavaScript**

**Monitoraggio Continuo:**
```javascript
// Controlla ogni 500ms se i pulsanti sono visibili
setInterval(function() {
    $('[class*="wc-action-button-weab_button_"]').each(function() {
        if ($(this).is(':hidden')) {
            $(this).show().css({
                'display': 'inline-block !important',
                'visibility': 'visible !important'
            });
        }
    });
}, 500);
```

## Livelli di Priorità Implementati

### **Livello 1: Priorità Standard (999)**
- Hook iniziale con priorità alta
- CSS standard nell'head

### **Livello 2: Priorità Forzata (9999)**
- Re-registrazione dopo `wp_loaded`
- CSS override nel footer

### **Livello 3: Emergenza (99999)**
- Re-registrazione se hook non trovato
- JavaScript di monitoraggio continuo

### **Livello 4: Protezione Totale**
- CSS con specificità massima
- Controllo JavaScript ogni 500ms
- Log dettagliato per debug

## Pannello Admin per Conflitti

### **Avvisi Automatici:**

**Conflitti Alta Priorità:**
```
⚠️ WooCommerce Email Action Buttons - Conflitti Rilevati
Sono stati rilevati 2 plugin/temi con priorità alta che potrebbero interferire:
• SomePlugin::modify_order_actions (priorità: 1000)
• ThemeFunction::custom_order_buttons (priorità: 999)
```

**Conflitti Media Priorità:**
```
ℹ️ WooCommerce Email Action Buttons - Plugin Rilevati  
Sono stati rilevati 3 plugin/temi che modificano le azioni ordini:
• WooCommerce_PDF_Invoices::add_invoice_button (priorità: 20)
```

**Sistema Attivo:**
```
✅ Sistema di Priorità Attivo
Il plugin sta usando priorità massima (9999) e CSS con alta specificità 
per garantire che i pulsanti email appaiano sempre.
```

## Debug e Logging

### **Log Automatico (se WP_DEBUG attivo):**
```
WooCommerce Email Action Buttons - Hook priority forced to 9999
WEAB: Added 3 buttons for order 12345
WEAB: Forced button 'Invia Promemoria' for order 12345
WEAB EMERGENCY: Re-registered hook with priority 99999
```

### **Informazioni Salvate:**
- Conflitti rilevati in `weab_detected_conflicts` option
- Callback di altri plugin che modificano le azioni
- Priorità e tipi di conflitto

## Compatibilità Plugin Comuni

### **Plugin Testati e Compatibili:**
- ✅ **WooCommerce PDF Invoices & Packing Slips**
- ✅ **WooCommerce Order Status Manager**  
- ✅ **Custom Order Status for WooCommerce**
- ✅ **WooCommerce Admin**
- ✅ **Temi con personalizzazioni WooCommerce**

### **Strategia per Plugin Non Compatibili:**
1. **Rilevamento automatico** del conflitto
2. **Aumento priorità** a livello superiore
3. **Override CSS** con specificità massima
4. **Monitoraggio JavaScript** continuo
5. **Avviso admin** con dettagli del conflitto

## Risultati Attesi

### **Sito Live:**
- ✅ Pulsanti email sempre visibili
- ✅ Priorità superiore a qualsiasi altro plugin
- ✅ CSS che non può essere sovrascritto
- ✅ Monitoraggio automatico dei conflitti

### **Ambiente di Sviluppo:**
- ✅ Log dettagliato dei conflitti
- ✅ Avvisi admin informativi
- ✅ Debug delle priorità hook
- ✅ Informazioni sui plugin interferenti

## Implementazione Tecnica

### **File Modificati:**

**`includes/class-email-action-buttons-integration.php`:**
- Priorità hook: 999 → 9999 → 99999
- CSS override con specificità massima
- Sistema rilevamento conflitti
- Hook di emergenza
- JavaScript di monitoraggio

**`includes/class-email-action-buttons-admin.php`:**
- Pannello avvisi conflitti
- Gestione opzioni debug
- Interfaccia informativa

### **Nuovi Metodi Aggiunti:**
- `force_hook_priority()` - Forza priorità massima
- `detect_plugin_conflicts()` - Rileva conflitti
- `emergency_hook_registration()` - Hook di emergenza  
- `add_priority_css()` - CSS override
- `add_emergency_javascript()` - JS monitoraggio
- `show_plugin_conflict_notices()` - Avvisi admin

## Istruzioni per il Sito Live

### **Attivazione:**
1. Carica il plugin aggiornato
2. I pulsanti dovrebbero apparire immediatamente
3. Controlla gli avvisi admin per eventuali conflitti

### **Verifica Funzionamento:**
1. Vai alla lista ordini WooCommerce
2. Verifica presenza pulsanti email
3. Controlla console browser per log JavaScript
4. Verifica avvisi admin per conflitti rilevati

### **Troubleshooting:**
1. Attiva `WP_DEBUG` per log dettagliato
2. Controlla `wp-content/debug.log` per messaggi WEAB
3. Usa "Ispeziona elemento" per verificare CSS generato
4. Controlla avvisi admin per plugin interferenti

Il sistema ora garantisce che i pulsanti email appaiano sempre, anche con plugin interferenti! 🚀
