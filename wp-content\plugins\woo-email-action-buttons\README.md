# WooCommerce Email Action Buttons

Un plugin WordPress per WooCommerce che aggiunge pulsanti di azione email personalizzati alla lista ordini dell'admin, permettendo di inviare email personalizzate ai clienti con un semplice clic.

## Caratteristiche

- **Interfaccia CRUD completa**: Crea, modifica, visualizza ed elimina pulsanti email personalizzati
- **Integrazione nativa WooCommerce**: I pulsanti appaiono direttamente nella lista ordini admin
- **Sistema email WooCommerce**: Utilizza i template email nativi di WooCommerce per consistenza
- **Localizzazione completa**: Supporto completo per l'italiano con file di traduzione
- **Sicurezza avanzata**: Nonces, sanitizzazione, validazione e controlli dei permessi
- **Placeholder dinamici**: Include informazioni dell'ordine automaticamente nelle email
- **Icone personalizzabili**: Utilizza le Dashicons di WordPress per i pulsanti

## Requisiti

- WordPress 5.8 o superiore
- PHP 8.0 o superiore
- WooCommerce 6.0 o superiore

## Installazione

1. Carica la cartella del plugin in `/wp-content/plugins/`
2. Attiva il plugin tramite il menu 'Plugin' di WordPress
3. Vai su WooCommerce > Pulsanti Azione Email per configurare i tuoi pulsanti

## Utilizzo

### Creazione di un Pulsante Email

1. Vai su **WooCommerce > Pulsanti Azione Email**
2. Clicca su **Aggiungi Nuovo**
3. Compila i campi richiesti:
   - **Nome Pulsante**: Il testo che apparirà sul pulsante
   - **Descrizione**: Descrizione interna (opzionale)
   - **Stati Ordine**: Seleziona per quali stati ordine mostrare il pulsante
   - **Codice Dashicon**: Icona del pulsante (es. `dashicons-email`)
   - **Contenuto Email**: Il messaggio da inviare al cliente

### Placeholder Disponibili

Puoi utilizzare questi placeholder nel contenuto email:

- `{order_number}` - Numero ordine
- `{order_date}` - Data ordine
- `{customer_name}` - Nome cliente
- `{customer_email}` - Email cliente
- `{order_total}` - Totale ordine
- `{order_status}` - Stato ordine
- `{billing_address}` - Indirizzo fatturazione
- `{shipping_address}` - Indirizzo spedizione
- `{payment_method}` - Metodo pagamento
- `{order_items}` - Lista articoli ordine

### Utilizzo dei Pulsanti

1. Vai alla lista ordini WooCommerce (**WooCommerce > Ordini**)
2. I pulsanti personalizzati appariranno nella colonna "Azioni" per gli ordini con stati corrispondenti
3. Clicca su un pulsante per inviare l'email al cliente
4. Riceverai una conferma del successo dell'invio

## Struttura File

```
woo-email-action-buttons/
├── woo-email-action-buttons.php     # File principale del plugin
├── includes/                        # Classi PHP
│   ├── class-email-action-buttons-admin.php
│   ├── class-email-action-buttons-db.php
│   ├── class-email-action-buttons-email.php
│   └── class-email-action-buttons-integration.php
├── templates/                       # Template admin
│   ├── admin-buttons-list.php
│   └── admin-button-form.php
├── assets/                          # CSS e JS
│   └── css/
│       └── admin.css
├── languages/                       # File di traduzione
│   ├── woo-email-action-buttons.pot
│   └── woo-email-action-buttons-it_IT.po
├── uninstall.php                    # Script di disinstallazione
└── README.md                        # Questo file
```

## Sicurezza

Il plugin implementa le migliori pratiche di sicurezza WordPress:

- **Nonces**: Tutti i form utilizzano nonces per prevenire CSRF
- **Sanitizzazione**: Tutti gli input vengono sanitizzati
- **Validazione**: Validazione lato server di tutti i dati
- **Controlli permessi**: Verifica delle capacità utente
- **Escape output**: Tutti gli output vengono escaped

## Personalizzazione

### Aggiungere Nuovi Placeholder

Per aggiungere nuovi placeholder, modifica il metodo `get_available_placeholders()` nella classe `Email_Action_Buttons_Email` e implementa la logica di sostituzione nel metodo `replace_placeholders()`.

### Personalizzare i Template Email

Il plugin utilizza i template email standard di WooCommerce. Puoi personalizzare l'aspetto copiando i template WooCommerce nel tuo tema.

### Aggiungere Nuove Icone

Il plugin supporta tutte le Dashicons di WordPress. Consulta la [documentazione ufficiale](https://developer.wordpress.org/resource/dashicons/) per l'elenco completo.

## Sviluppo

### Hooks Disponibili

Il plugin fornisce diversi hook per la personalizzazione:

```php
// Filtra i pulsanti prima di mostrarli
add_filter('weab_order_action_buttons', 'my_custom_buttons', 10, 2);

// Azione dopo l'invio di un'email
add_action('weab_email_sent', 'my_email_sent_handler', 10, 3);
```

### Debug

Per abilitare il logging, aggiungi al tuo `wp-config.php`:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

I log appariranno nel file di log di WordPress.

## Supporto

Per supporto e segnalazione bug, contatta lo sviluppatore.

## Changelog

### 1.0.0
- Rilascio iniziale
- Interfaccia CRUD completa
- Integrazione con lista ordini WooCommerce
- Sistema email con template WooCommerce
- Localizzazione italiana completa
- Sicurezza e validazione complete

## Licenza

Questo plugin è rilasciato sotto licenza GPL v2 o successiva.

## Crediti

Sviluppato utilizzando le migliori pratiche WordPress e WooCommerce.
