<?php
/**
 * Template per l'email HTML di Pagamento a Rate
 *
 * Questo template può essere sovrascritto nel tema copiandolo in 
 * {tema}/woocommerce/emails/wpr-payment-email.php.
 */

defined('ABSPATH') || exit;

/*
 * @hooked WC_Emails::email_header() Output dell'intestazione dell'email
 */
do_action('woocommerce_email_header', $email_heading, $email);
?>

<p><?php _e('Gentile Cliente,', 'woo-pagamento-rate'); ?></p>

<p><?php _e('grazie per aver scelto di acquistare presso di noi con la modalità di pagamento tramite finanziamento.', 'woo-pagamento-rate'); ?></p>

<p><?php _e('Per procedere con la pratica, ti chiediamo cortesemente di rispondere direttamente a questa email o inviare un messaggio WhatsApp al numero [inserire numero WhatsApp] indicando le seguenti informazioni:', 'woo-pagamento-rate'); ?></p>

<ul>
    <li><?php _e('Nome e Cognome', 'woo-pagamento-rate'); ?></li>
    <li><?php _e('Numero dell\'ordine', 'woo-pagamento-rate'); ?></li>
    <li><?php _e('Numero di rate desiderate (puoi scegliere tra 6, 12 o 24 rate)', 'woo-pagamento-rate'); ?></li>
</ul>

<p><?php _e('Inoltre, ti chiediamo gentilmente di allegare i seguenti documenti:', 'woo-pagamento-rate'); ?></p>

<ul>
    <li><?php _e('Documento di Identità (fronte-retro)', 'woo-pagamento-rate'); ?></li>
    <li><?php _e('Codice Fiscale (fronte-retro)', 'woo-pagamento-rate'); ?></li>
    <li><?php _e('Ultima busta paga', 'woo-pagamento-rate'); ?></li>
</ul>

<p><?php _e('Una volta ricevuta tutta la documentazione, procederemo rapidamente con la verifica e ti invieremo conferma.', 'woo-pagamento-rate'); ?></p>

<p><?php _e('Rimaniamo a disposizione per qualsiasi dubbio o chiarimento.', 'woo-pagamento-rate'); ?></p>

<p><?php _e('Cordiali saluti', 'woo-pagamento-rate'); ?></p>

<h2><?php _e('Riepilogo del tuo ordine', 'woo-pagamento-rate'); ?></h2>

<p><?php printf(__('Ordine n. %s', 'woo-pagamento-rate'), $order->get_order_number()); ?></p>

<table class="td" cellspacing="0" cellpadding="6" style="width: 100%; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif;" border="1">
    <thead>
        <tr>
            <th class="td" scope="col" style="text-align:left;"><?php _e('Prodotto', 'woo-pagamento-rate'); ?></th>
            <th class="td" scope="col" style="text-align:left;"><?php _e('Quantità', 'woo-pagamento-rate'); ?></th>
            <th class="td" scope="col" style="text-align:left;"><?php _e('Prezzo', 'woo-pagamento-rate'); ?></th>
        </tr>
    </thead>
    <tbody>
        <?php
        $items = $order->get_items();
        foreach ($items as $item_id => $item) {
            $product = $item->get_product();
            ?>
            <tr>
                <td class="td" style="text-align:left; vertical-align:middle; border: 1px solid #eee; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif; word-wrap:break-word;"><?php echo $item->get_name(); ?></td>
                <td class="td" style="text-align:left; vertical-align:middle; border: 1px solid #eee; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif;"><?php echo $item->get_quantity(); ?></td>
                <td class="td" style="text-align:left; vertical-align:middle; border: 1px solid #eee; font-family: 'Helvetica Neue', Helvetica, Roboto, Arial, sans-serif;"><?php echo wc_price($item->get_total()); ?></td>
            </tr>
            <?php
        }
        ?>
    </tbody>
    <tfoot>
        <tr>
            <th class="td" scope="row" colspan="2" style="text-align:left; border: 1px solid #eee;"><?php _e('Subtotale', 'woo-pagamento-rate'); ?></th>
            <td class="td" style="text-align:left; border: 1px solid #eee;"><?php echo wc_price($order->get_subtotal()); ?></td>
        </tr>
        <?php if ($order->get_shipping_total() > 0) : ?>
        <tr>
            <th class="td" scope="row" colspan="2" style="text-align:left; border: 1px solid #eee;"><?php _e('Spedizione', 'woo-pagamento-rate'); ?></th>
            <td class="td" style="text-align:left; border: 1px solid #eee;"><?php echo wc_price($order->get_shipping_total()); ?></td>
        </tr>
        <?php endif; ?>
        <?php foreach ($order->get_tax_totals() as $code => $tax) : ?>
        <tr>
            <th class="td" scope="row" colspan="2" style="text-align:left; border: 1px solid #eee;"><?php echo $tax->label; ?></th>
            <td class="td" style="text-align:left; border: 1px solid #eee;"><?php echo wc_price($tax->amount); ?></td>
        </tr>
        <?php endforeach; ?>
        <tr>
            <th class="td" scope="row" colspan="2" style="text-align:left; border: 1px solid #eee;"><?php _e('Totale', 'woo-pagamento-rate'); ?></th>
            <td class="td" style="text-align:left; border: 1px solid #eee;"><?php echo wc_price($order->get_total()); ?></td>
        </tr>
    </tfoot>
</table>

<?php if ($additional_content) : ?>
    <p><?php echo wp_kses_post(wpautop(wptexturize($additional_content))); ?></p>
<?php endif; ?>

<?php
/*
 * @hooked WC_Emails::email_footer() Output del footer dell'email
 */
do_action('woocommerce_email_footer', $email); 