<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe per la gestione dell'invio email
 */
class Email_Action_Buttons_Email {

    private $db;

    public function __construct() {
        $this->db = new Email_Action_Buttons_DB();

        // Gestisci le richieste di invio email
        add_action('admin_post_weab_send_email', array($this, 'handle_send_email'));
    }
    
    /**
     * Gestisce l'invio dell'email
     */
    public function handle_send_email() {
        if (!weab_user_can_manage()) {
            wp_die('Non hai i permessi per eseguire questa azione.');
        }

        $order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;
        $button_id = isset($_GET['button_id']) ? intval($_GET['button_id']) : 0;

        // Verifica nonce
        if (!wp_verify_nonce($_GET['weab_nonce'], 'weab_send_email_' . $order_id . '_' . $button_id)) {
            wp_die('Errore di sicurezza. Riprova.');
        }
        
        // Ottieni l'ordine
        $order = wc_get_order($order_id);
        if (!$order) {
            wp_redirect(add_query_arg(array(
                'post_type' => 'shop_order',
                'error' => urlencode('Ordine non trovato.')
            ), admin_url('edit.php')));
            exit;
        }

        // Ottieni il pulsante
        $button = $this->db->get_button($button_id);
        if (!$button) {
            wp_redirect(add_query_arg(array(
                'post_type' => 'shop_order',
                'error' => urlencode('Pulsante non trovato.')
            ), admin_url('edit.php')));
            exit;
        }
        
        // Invia l'email
        $result = $this->send_custom_email($order, $button);
        
        $message = $result ? 'email_sent' : 'email_error';
        
        // Redirect alla lista ordini con messaggio
        wp_redirect(add_query_arg(array(
            'post_type' => 'shop_order',
            'message' => $message
        ), admin_url('edit.php')));
        exit;
    }
    
    /**
     * Invia l'email personalizzata
     */
    public function send_custom_email($order, $button) {
        if (!$order || !$button) {
            return false;
        }

        // Ottieni l'email del destinatario
        $recipient = $order->get_billing_email();

        if (!$recipient) {
            weab_log('Email destinatario non trovata per ordine #' . $order->get_id(), 'error');
            return false;
        }

        // Sostituisci i placeholder nel contenuto email
        $email_content = $this->replace_placeholders($button->email_content, $order);

        // Prepara l'oggetto email
        $subject = sprintf(
            'Aggiornamento per il tuo ordine #%s',
            $order->get_order_number()
        );

        // Prepara le intestazioni
        $headers = array('Content-Type: text/html; charset=UTF-8');

        // Ottieni il template HTML
        $message = $this->get_email_html($email_content, $order);

        // Invia l'email
        $sent = wp_mail($recipient, $subject, $message, $headers);

        if ($sent) {
            // Aggiungi nota all'ordine
            $order->add_order_note(
                sprintf(
                    'Email "%s" inviata al cliente.',
                    $button->button_name
                )
            );

            weab_log('Email inviata con successo per ordine #' . $order->get_id() . ' usando pulsante: ' . $button->button_name);
        } else {
            weab_log('Errore nell\'invio email per ordine #' . $order->get_id(), 'error');
        }

        return $sent;
    }
    
    /**
     * Sostituisce i placeholder nel contenuto email
     */
    private function replace_placeholders($content, $order) {
        $placeholders = array(
            '{order_number}' => $order->get_order_number(),
            '{order_date}' => wc_format_datetime($order->get_date_created()),
            '{customer_name}' => $order->get_formatted_billing_full_name(),
            '{customer_email}' => $order->get_billing_email(),
            '{order_total}' => $order->get_formatted_order_total(),
            '{order_status}' => wc_get_order_status_name($order->get_status()),
            '{billing_address}' => $order->get_formatted_billing_address(),
            '{shipping_address}' => $order->get_formatted_shipping_address(),
            '{payment_method}' => $order->get_payment_method_title(),
            '{order_items}' => $this->get_order_items_html($order)
        );
        
        return str_replace(array_keys($placeholders), array_values($placeholders), $content);
    }
    
    /**
     * Genera HTML per gli articoli dell'ordine
     */
    private function get_order_items_html($order) {
        $items_html = '<ul>';
        
        foreach ($order->get_items() as $item) {
            $product = $item->get_product();
            $items_html .= '<li>';
            $items_html .= $item->get_name();
            $items_html .= ' x ' . $item->get_quantity();
            if ($product && $product->get_sku()) {
                $items_html .= ' (SKU: ' . $product->get_sku() . ')';
            }
            $items_html .= '</li>';
        }
        
        $items_html .= '</ul>';
        
        return $items_html;
    }
    
    /**
     * Ottiene l'HTML dell'email usando il template WooCommerce
     */
    private function get_email_html($content, $order) {
        // Usa il template WooCommerce per l'email
        ob_start();

        // Header email WooCommerce
        wc_get_template('emails/email-header.php', array(
            'email_heading' => sprintf(
                'Aggiornamento ordine #%s',
                $order->get_order_number()
            )
        ));

        // Contenuto personalizzato
        echo '<div style="margin-bottom: 40px;">';
        echo wpautop($content);
        echo '</div>';

        // Dettagli ordine
        wc_get_template('emails/email-order-details.php', array(
            'order' => $order,
            'sent_to_admin' => false,
            'plain_text' => false,
            'email' => null // Non passiamo l'oggetto email dato che non estendiamo WC_Email
        ));

        // Footer email WooCommerce
        wc_get_template('emails/email-footer.php');

        return ob_get_clean();
    }
    
    /**
     * Ottieni i placeholder disponibili
     */
    public static function get_available_placeholders() {
        return array(
            '{order_number}' => 'Numero ordine',
            '{order_date}' => 'Data ordine',
            '{customer_name}' => 'Nome cliente',
            '{customer_email}' => 'Email cliente',
            '{order_total}' => 'Totale ordine',
            '{order_status}' => 'Stato ordine',
            '{billing_address}' => 'Indirizzo fatturazione',
            '{shipping_address}' => 'Indirizzo spedizione',
            '{payment_method}' => 'Metodo pagamento',
            '{order_items}' => 'Articoli ordine'
        );
    }
}
