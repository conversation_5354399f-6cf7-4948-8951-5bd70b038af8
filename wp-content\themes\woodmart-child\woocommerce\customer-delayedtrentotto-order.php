<?php

/**

 * Customer completed order email

 *

 * This template can be overridden by copying it to yourtheme/woocommerce/emails/customer-completed-order.php.

 *

 * HOWEVER, on occasion WooCommerce will need to update template files and you

 * (the theme developer) will need to copy the new files to your theme to

 * maintain compatibility. We try to do this as little as possible, but it does

 * happen. When this occurs the version of the template file will be bumped and

 * the readme will list any important changes.

 *

 * @see https://docs.woocommerce.com/document/template-structure/

 * @package WooCommerce/Templates/Emails

 * @version 3.7.0

 */



if ( ! defined( 'ABSPATH' ) ) {

	exit;

}

	$ship = wc_get_order($order);

	$ship->get_shipping_methods();

/*

 * @hooked WC_Emails::email_header() Output the email header

 */

do_action( 'woocommerce_email_header', $email_heading, $email ); ?>



<?php /* translators: %s: Customer first name */ ?>

<p><?php printf( esc_html__( 'Gentile Cliente %s,', 'woocommerce' ), esc_html( $order->get_billing_first_name() ) ); ?></p>





<p>Ci dispiace informarti che il suo ordine ha subito un ritardo a causa dell&#39;elevato volume di ordini che stiamo attualmente gestendo.</p>

<p>Ci scusiamo sinceramente per questo inconveniente e stiamo lavorando al massimo delle nostre capacità per 1completare il tuo ordine il prima possibile.</p>

<p>E' nel nostro impegno trovare una soluzione insieme a lei, ed è per questo le proponiamo alcune opzioni per procedere:</p>

<ul>

<li>1. Attendere il prodotto ordinato: Al momento non abbiamo una data precisa per il completamento dell'ordine, ma è nostra priorità riuscire a evadere la spedizione nel minor tempo possibile.</li>

<li>2. Modifica del prodotto: Se desidera, potremmo verificare la disponibilità del prodotto con un colore o una configurazione diversa (ad esempio, memoria GB differente). In questo caso se disponibile potremmo riuscire a spedire l&#39;ordine subito. Per discutere meglio queste soluzioni o per ulteriori chiarimenti, la invitiamo a contattarci al numero 06.8103353 o a inviarci un&#39;<NAME_EMAIL>.</li>

<li>3. Rimborso completo: Se preferisce non attendere ulteriormente, può richiedere il rimborso completo del suo ordine.</li>

</ul>

<p><a href="https://www.centronoli.it/richiesta-rimborso/">Modulo di Richiesta Rimborso.</a></p>

<p>Ti garantiamo che:</p>

<ul>

<li>- Stiamo monitorando attentamente la situazione e faremo del nostro meglio per ridurre al minimo i tempi di attesa.</li>

<li>- Ti terremo aggiornato su ogni ulteriore sviluppo e ti invieremo una email di conferma non appena il tuo ordine sarà completato.</li>

</ul>



<p>La ringraziamo per la comprensione e rimaniamo a disposizione per qualsiasi altra necessità.</p>



<p>Cordiali Saluti</p>

<p>Centro Telefonia Noli</p>

<?php



/*

 * @hooked WC_Emails::order_details() Shows the order details table.

 * @hooked WC_Structured_Data::generate_order_data() Generates structured data.

 * @hooked WC_Structured_Data::output_structured_data() Outputs structured data.

 * @since 2.5.0

 */

do_action( 'woocommerce_email_order_details', $order, $sent_to_admin, $plain_text, $email );



/*

 * @hooked WC_Emails::order_meta() Shows order meta data.

 */

do_action( 'woocommerce_email_order_meta', $order, $sent_to_admin, $plain_text, $email );



/*

 * @hooked WC_Emails::customer_details() Shows customer details

 * @hooked WC_Emails::email_address() Shows email address

 */

do_action( 'woocommerce_email_customer_details', $order, $sent_to_admin, $plain_text, $email );



/**

 * Show user-defined additional content - this is set in each email's settings.

 */



/*

 * @hooked WC_Emails::email_footer() Output the email footer

 */

do_action( 'woocommerce_email_footer', $email );