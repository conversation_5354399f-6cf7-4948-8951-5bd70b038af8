# WooCommerce Coupon Importer Exporter

Un plugin WordPress che consente di esportare e importare i codici promozionali (coupon) di WooCommerce insieme ai loro log di utilizzo.

## Descrizione

WooCommerce Coupon Importer Exporter è un plugin che semplifica la gestione dei coupon tra diversi siti WooCommerce. Con questo plugin è possibile:

- Esportare tutti i coupon di WooCommerce in un file CSV
- Includere i log di utilizzo dei coupon nell'esportazione
- Importare coupon da un file CSV in un altro sito WooCommerce
- Aggiornare coupon esistenti durante l'importazione
- Importare anche i log di utilizzo dei coupon

Questo plugin è particolarmente utile per:
- Migrare i coupon da un sito all'altro
- Creare backup dei coupon
- Gestire i coupon in modo centralizzato per più siti

## Requisiti

- WordPress 5.0 o superiore
- WooCommerce 3.0.0 o superiore
- PHP 7.2 o superiore

## Installazione

1. Carica la cartella `wc-coupon-importer-exporter` nella directory `/wp-content/plugins/` del tuo sito WordPress
2. Attiva il plugin dalla sezione 'Plugin' in WordPress
3. Vai a WooCommerce > Importa/Esporta Coupon per utilizzare il plugin

## Utilizzo

### Esportazione dei coupon

1. Vai a WooCommerce > Importa/Esporta Coupon
2. Seleziona la scheda "Esporta"
3. Scegli se includere i log di utilizzo dei coupon
4. Seleziona lo stato dei coupon da esportare (tutti, pubblicati, bozze, ecc.)
5. Clicca su "Esporta Coupon"
6. Il download del file CSV inizierà automaticamente

### Importazione dei coupon

1. Vai a WooCommerce > Importa/Esporta Coupon
2. Seleziona la scheda "Importa"
3. Carica il file CSV esportato in precedenza
4. Scegli se aggiornare i coupon esistenti
5. Scegli se importare i log di utilizzo (se presenti nel file)
6. Clicca su "Importa Coupon"
7. Attendi il completamento dell'importazione

## Formato del file CSV

Il file CSV esportato contiene le seguenti colonne:

- `id`: ID del coupon
- `code`: Codice del coupon (obbligatorio)
- `description`: Descrizione del coupon
- `discount_type`: Tipo di sconto (obbligatorio)
- `amount`: Importo dello sconto (obbligatorio)
- `expiry_date`: Data di scadenza
- `minimum_amount`: Importo minimo di spesa
- `maximum_amount`: Importo massimo di spesa
- `individual_use`: Se il coupon può essere usato solo individualmente
- `exclude_sale_items`: Se escludere gli articoli in saldo
- `product_ids`: ID dei prodotti a cui si applica il coupon
- `excluded_product_ids`: ID dei prodotti esclusi
- `product_categories`: Categorie di prodotti a cui si applica il coupon
- `excluded_product_categories`: Categorie di prodotti escluse
- `customer_emails`: Email dei clienti a cui è limitato il coupon
- `usage_limit`: Limite di utilizzo totale
- `usage_limit_per_user`: Limite di utilizzo per utente
- `limit_usage_to_x_items`: Limite di utilizzo per numero di articoli
- `usage_count`: Conteggio di utilizzo attuale
- `free_shipping`: Se il coupon offre spedizione gratuita
- `exclude_shipping_methods`: Metodi di spedizione esclusi
- `product_attributes`: Attributi dei prodotti a cui si applica il coupon
- `excluded_product_attributes`: Attributi dei prodotti esclusi
- `status`: Stato del coupon
- `usage_logs`: Log di utilizzo (se inclusi nell'esportazione)

## Supporto

Per segnalare problemi o richiedere assistenza, contattare l'autore del plugin.

## Licenza

Questo plugin è rilasciato sotto la licenza GPL v2 o successiva.

## Changelog

### 1.0.0
- Versione iniziale del plugin 