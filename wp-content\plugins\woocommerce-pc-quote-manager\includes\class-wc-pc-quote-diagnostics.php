<?php
if (!defined('ABSPATH')) {
    exit;
}

class WC_PC_Quote_Diagnostics {
    
    public function __construct() {
        // Aggiungi menu diagnostics solo per admin
        add_action('admin_menu', array($this, 'add_diagnostics_menu'));
        
        // Aggiungi AJAX handler per test database
        add_action('wp_ajax_wc_pc_quote_test_db', array($this, 'ajax_test_database'));

        // Aggiungi AJAX handler per creazione manuale tabelle
        add_action('wp_ajax_wc_pc_quote_create_tables', array($this, 'ajax_create_tables'));
    }

    /**
     * Aggiunge menu diagnostics
     */
    public function add_diagnostics_menu() {
        add_submenu_page(
            'woocommerce',
            __('Diagnostics PC Quote', 'wc-pc-quote-manager'),
            __('Diagnostics PC Quote', 'wc-pc-quote-manager'),
            'manage_options',
            'wc-pc-quotes-diagnostics',
            array($this, 'render_diagnostics_page')
        );
    }

    /**
     * Renderizza la pagina diagnostics
     */
    public function render_diagnostics_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('WooCommerce PC Quote Manager - Diagnostics', 'wc-pc-quote-manager'); ?></h1>
            
            <div class="notice notice-info">
                <p><?php _e('Utilizza questa pagina per diagnosticare problemi con il plugin PC Quote Manager.', 'wc-pc-quote-manager'); ?></p>
            </div>

            <?php
            // Mostra errori di attivazione se presenti
            $activation_errors = get_option('wc_pc_quote_activation_errors', array());
            if (!empty($activation_errors)):
            ?>
            <div class="notice notice-error">
                <h3><?php _e('Errori di Attivazione Rilevati', 'wc-pc-quote-manager'); ?></h3>
                <p><?php _e('Durante l\'attivazione del plugin sono stati rilevati i seguenti errori:', 'wc-pc-quote-manager'); ?></p>
                <ul>
                    <?php foreach ($activation_errors as $error): ?>
                        <li><?php echo esc_html($error); ?></li>
                    <?php endforeach; ?>
                </ul>
                <p><?php _e('Utilizza il pulsante "Crea Tabelle Database" qui sotto per risolvere questi problemi.', 'wc-pc-quote-manager'); ?></p>
            </div>
            <?php endif; ?>

            <div class="postbox">
                <h2 class="hndle"><?php _e('Test Database', 'wc-pc-quote-manager'); ?></h2>
                <div class="inside">
                    <p><?php _e('Verifica lo stato delle tabelle del database:', 'wc-pc-quote-manager'); ?></p>
                    <button type="button" class="button button-primary" id="test-database">
                        <?php _e('Testa Database', 'wc-pc-quote-manager'); ?>
                    </button>
                    <div id="database-results" style="margin-top: 15px;"></div>
                </div>
            </div>

            <div class="postbox">
                <h2 class="hndle"><?php _e('Creazione Manuale Tabelle', 'wc-pc-quote-manager'); ?></h2>
                <div class="inside">
                    <p><?php _e('Se le tabelle del database non sono state create durante l\'attivazione, puoi crearle manualmente:', 'wc-pc-quote-manager'); ?></p>
                    <div class="notice notice-warning inline">
                        <p><?php _e('<strong>Attenzione:</strong> Questa operazione creerà o aggiornerà le tabelle del database. Assicurati di aver fatto un backup prima di procedere.', 'wc-pc-quote-manager'); ?></p>
                    </div>
                    <button type="button" class="button button-secondary" id="create-tables">
                        <?php _e('Crea Tabelle Database', 'wc-pc-quote-manager'); ?>
                    </button>
                    <div id="create-tables-results" style="margin-top: 15px;"></div>
                </div>
            </div>

            <div class="postbox">
                <h2 class="hndle"><?php _e('Informazioni Sistema', 'wc-pc-quote-manager'); ?></h2>
                <div class="inside">
                    <?php $this->display_system_info(); ?>
                </div>
            </div>

            <div class="postbox">
                <h2 class="hndle"><?php _e('Log Debug', 'wc-pc-quote-manager'); ?></h2>
                <div class="inside">
                    <?php $this->display_debug_log(); ?>
                </div>
            </div>

            <div class="postbox">
                <h2 class="hndle"><?php _e('Test Form Submission', 'wc-pc-quote-manager'); ?></h2>
                <div class="inside">
                    <p><?php _e('Testa l\'invio di un preventivo di prova:', 'wc-pc-quote-manager'); ?></p>
                    <button type="button" class="button" id="test-form-submission">
                        <?php _e('Testa Invio Form', 'wc-pc-quote-manager'); ?>
                    </button>
                    <div id="form-test-results" style="margin-top: 15px;"></div>
                </div>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('#test-database').click(function() {
                var button = $(this);
                var results = $('#database-results');

                button.prop('disabled', true).text('<?php _e('Testing...', 'wc-pc-quote-manager'); ?>');
                results.html('<div class="spinner is-active" style="float: none;"></div>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'wc_pc_quote_test_db',
                        nonce: '<?php echo wp_create_nonce('wc_pc_quote_diagnostics'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            results.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>' + response.data.details);
                        } else {
                            results.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>' + response.data.details);
                        }
                    },
                    error: function() {
                        results.html('<div class="notice notice-error"><p><?php _e('Errore durante il test.', 'wc-pc-quote-manager'); ?></p></div>');
                    },
                    complete: function() {
                        button.prop('disabled', false).text('<?php _e('Testa Database', 'wc-pc-quote-manager'); ?>');
                    }
                });
            });

            $('#create-tables').click(function() {
                var button = $(this);
                var results = $('#create-tables-results');

                if (!confirm('<?php _e('Sei sicuro di voler creare/aggiornare le tabelle del database? Questa operazione è irreversibile.', 'wc-pc-quote-manager'); ?>')) {
                    return;
                }

                button.prop('disabled', true).text('<?php _e('Creando tabelle...', 'wc-pc-quote-manager'); ?>');
                results.html('<div class="spinner is-active" style="float: none;"></div>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'wc_pc_quote_create_tables',
                        nonce: '<?php echo wp_create_nonce('wc_pc_quote_diagnostics'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            results.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>' + response.data.details);
                        } else {
                            results.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>' + response.data.details);
                        }
                    },
                    error: function() {
                        results.html('<div class="notice notice-error"><p><?php _e('Errore durante la creazione delle tabelle.', 'wc-pc-quote-manager'); ?></p></div>');
                    },
                    complete: function() {
                        button.prop('disabled', false).text('<?php _e('Crea Tabelle Database', 'wc-pc-quote-manager'); ?>');
                    }
                });
            });
        });
        </script>
        <?php
    }

    /**
     * Test AJAX del database
     */
    public function ajax_test_database() {
        if (!wp_verify_nonce($_POST['nonce'], 'wc_pc_quote_diagnostics') || !current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $results = $this->test_database_connection();
        
        if ($results['success']) {
            wp_send_json_success(array(
                'message' => __('Database test completato con successo!', 'wc-pc-quote-manager'),
                'details' => $results['details']
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Problemi rilevati nel database!', 'wc-pc-quote-manager'),
                'details' => $results['details']
            ));
        }
    }

    /**
     * AJAX handler per creazione manuale tabelle
     */
    public function ajax_create_tables() {
        if (!wp_verify_nonce($_POST['nonce'], 'wc_pc_quote_diagnostics') || !current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        // Usa la stessa funzione dell'attivazione
        $results = wc_pc_quote_create_database_tables();

        $details = '<h4>' . __('Risultati creazione tabelle:', 'wc-pc-quote-manager') . '</h4>';
        $details .= '<ul>';

        if ($results['success']) {
            $details .= '<li style="color: green;">✓ Tabelle create con successo</li>';

            // Verifica finale
            global $wpdb;
            $quotes_table = $wpdb->prefix . 'wc_pc_quotes';
            $conv_table = $wpdb->prefix . 'wc_pc_quote_conversations';

            if ($wpdb->get_var("SHOW TABLES LIKE '$quotes_table'") === $quotes_table) {
                $details .= '<li style="color: green;">✓ Tabella wp_wc_pc_quotes: CREATA</li>';
            }

            if ($wpdb->get_var("SHOW TABLES LIKE '$conv_table'") === $conv_table) {
                $details .= '<li style="color: green;">✓ Tabella wp_wc_pc_quote_conversations: CREATA</li>';
            }

            // Pulisci eventuali errori di attivazione precedenti
            delete_option('wc_pc_quote_activation_errors');

        } else {
            foreach ($results['errors'] as $error) {
                $details .= '<li style="color: red;">✗ ' . esc_html($error) . '</li>';
            }
        }

        $details .= '</ul>';

        // Mostra log di attivazione se disponibile
        $upload_dir = wp_upload_dir();
        $log_file = $upload_dir['basedir'] . '/wc-pc-quote-activation.log';

        if (file_exists($log_file)) {
            $log_content = file_get_contents($log_file);
            $log_lines = explode("\n", $log_content);
            $recent_lines = array_slice($log_lines, -10); // Ultimi 10 log

            $details .= '<h4>' . __('Log creazione (ultimi 10 entries):', 'wc-pc-quote-manager') . '</h4>';
            $details .= '<textarea readonly style="width: 100%; height: 150px;">' . esc_textarea(implode("\n", $recent_lines)) . '</textarea>';
        }

        if ($results['success']) {
            wp_send_json_success(array(
                'message' => __('Tabelle create con successo!', 'wc-pc-quote-manager'),
                'details' => $details
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Errore durante la creazione delle tabelle!', 'wc-pc-quote-manager'),
                'details' => $details
            ));
        }
    }

    /**
     * Testa la connessione e struttura del database
     */
    private function test_database_connection() {
        global $wpdb;
        $details = '<ul>';
        $success = true;

        // Test 1: Connessione database
        $connection_test = $wpdb->get_var("SELECT 1");
        if ($connection_test === '1') {
            $details .= '<li style="color: green;">✓ Connessione database: OK</li>';
        } else {
            $details .= '<li style="color: red;">✗ Connessione database: FALLITA</li>';
            $success = false;
        }

        // Test 2: Esistenza tabella principale
        $table_name = $wpdb->prefix . 'wc_pc_quotes';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
        if ($table_exists) {
            $details .= '<li style="color: green;">✓ Tabella wp_wc_pc_quotes: ESISTE</li>';
            
            // Test struttura tabella
            $columns = $wpdb->get_results("DESCRIBE $table_name");
            $details .= '<li>Colonne trovate: ' . count($columns) . '</li>';
            
            // Test inserimento
            $test_result = $wpdb->query("SELECT 1 FROM $table_name LIMIT 1");
            if ($test_result !== false) {
                $details .= '<li style="color: green;">✓ Accesso lettura tabella: OK</li>';
            } else {
                $details .= '<li style="color: red;">✗ Accesso lettura tabella: FALLITO</li>';
                $success = false;
            }
        } else {
            $details .= '<li style="color: red;">✗ Tabella wp_wc_pc_quotes: NON ESISTE</li>';
            $success = false;
        }

        // Test 3: Esistenza tabella conversazioni
        $conv_table = $wpdb->prefix . 'wc_pc_quote_conversations';
        $conv_exists = $wpdb->get_var("SHOW TABLES LIKE '$conv_table'") === $conv_table;
        if ($conv_exists) {
            $details .= '<li style="color: green;">✓ Tabella wp_wc_pc_quote_conversations: ESISTE</li>';
        } else {
            $details .= '<li style="color: orange;">⚠ Tabella wp_wc_pc_quote_conversations: NON ESISTE (feature conversazioni non disponibile)</li>';
        }

        // Test 4: Permessi scrittura
        if ($table_exists) {
            $test_insert = $wpdb->query($wpdb->prepare("
                INSERT INTO $table_name 
                (customer_name, customer_email, customer_phone, pc_type, budget, processor_preference, graphics_preference, additional_needs, other_requests, status) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ", 'TEST', '<EMAIL>', '123456789', 'Gaming', 'Da 500€ a 750€', 'Intel', 'Nvidia', 'Monitor', 'Test', 'In attesa di risposta'));
            
            if ($test_insert !== false) {
                $details .= '<li style="color: green;">✓ Test inserimento: OK</li>';
                // Rimuovi il record di test
                $wpdb->delete($table_name, array('customer_email' => '<EMAIL>'));
                $details .= '<li style="color: green;">✓ Test cancellazione: OK</li>';
            } else {
                $details .= '<li style="color: red;">✗ Test inserimento: FALLITO - ' . $wpdb->last_error . '</li>';
                $success = false;
            }
        }

        $details .= '</ul>';

        return array(
            'success' => $success,
            'details' => $details
        );
    }

    /**
     * Mostra informazioni di sistema
     */
    private function display_system_info() {
        global $wpdb;
        ?>
        <table class="widefat">
            <tr><td><strong>WordPress Version:</strong></td><td><?php echo get_bloginfo('version'); ?></td></tr>
            <tr><td><strong>PHP Version:</strong></td><td><?php echo PHP_VERSION; ?></td></tr>
            <tr><td><strong>MySQL Version:</strong></td><td><?php echo $wpdb->db_version(); ?></td></tr>
            <tr><td><strong>WooCommerce Version:</strong></td><td><?php echo defined('WC_VERSION') ? WC_VERSION : 'Non installato'; ?></td></tr>
            <tr><td><strong>Plugin Version:</strong></td><td><?php echo defined('WC_PC_QUOTE_VERSION') ? WC_PC_QUOTE_VERSION : 'Sconosciuta'; ?></td></tr>
            <tr><td><strong>WP Debug:</strong></td><td><?php echo defined('WP_DEBUG') && WP_DEBUG ? 'Attivo' : 'Disattivo'; ?></td></tr>
            <tr><td><strong>Memory Limit:</strong></td><td><?php echo ini_get('memory_limit'); ?></td></tr>
            <tr><td><strong>Max Execution Time:</strong></td><td><?php echo ini_get('max_execution_time'); ?>s</td></tr>
        </table>
        <?php
    }

    /**
     * Mostra log debug
     */
    private function display_debug_log() {
        $upload_dir = wp_upload_dir();
        $log_file = $upload_dir['basedir'] . '/wc-pc-quote-debug.log';
        
        if (file_exists($log_file)) {
            $log_content = file_get_contents($log_file);
            $lines = explode("\n", $log_content);
            $recent_lines = array_slice($lines, -20); // Ultimi 20 log
            
            echo '<h4>' . __('Ultimi 20 log entries:', 'wc-pc-quote-manager') . '</h4>';
            echo '<textarea readonly style="width: 100%; height: 200px;">' . esc_textarea(implode("\n", $recent_lines)) . '</textarea>';
            echo '<p><a href="' . $upload_dir['baseurl'] . '/wc-pc-quote-debug.log" target="_blank" class="button">' . __('Visualizza log completo', 'wc-pc-quote-manager') . '</a></p>';
        } else {
            echo '<p>' . __('Nessun file di log trovato.', 'wc-pc-quote-manager') . '</p>';
        }
    }
}
