<?php

/**

 * Classe per la gestione dell'interfaccia di amministrazione

 *

 * @package WooCommerce Coupon Importer Exporter

 */



// Impedisci l'accesso diretto

if (!defined('ABSPATH')) {

    exit;

}



/**

 * Classe per la gestione dell'interfaccia di amministrazione

 */

class WCCIE_Admin {



    /**

     * Costruttore

     */

    public function __construct() {

        // Aggiungi voce di menu

        add_action('admin_menu', array($this, 'add_admin_menu'));

        

        // Registra gli script e gli stili

        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

        

        // Gestisci le azioni AJAX

        add_action('wp_ajax_wccie_export_coupons', array($this, 'handle_export_coupons'));

        add_action('wp_ajax_wccie_import_coupons', array($this, 'handle_import_coupons'));

    }



    /**

     * Aggiungi voce di menu

     */

    public function add_admin_menu() {

        add_submenu_page(

            'woocommerce',

            __('Importa/Esporta Coupon', 'wc-coupon-importer-exporter'),

            __('Importa/Esporta Coupon', 'wc-coupon-importer-exporter'),

            'manage_woocommerce',

            'wc-coupon-importer-exporter',

            array($this, 'render_admin_page')

        );

    }



    /**

     * Registra script e stili

     */

    public function enqueue_scripts($hook) {

        if ('woocommerce_page_wc-coupon-importer-exporter' !== $hook) {

            return;

        }



        // Registra e carica CSS

        wp_enqueue_style(

            'wccie-admin-style',

            WCCIE_PLUGIN_URL . 'assets/css/admin.css',

            array(),

            WCCIE_VERSION

        );



        // Registra e carica JS

        wp_enqueue_script(

            'wccie-admin-script',

            WCCIE_PLUGIN_URL . 'assets/js/admin.js',

            array('jquery'),

            WCCIE_VERSION,

            true

        );



        // Passa variabili a JavaScript

        wp_localize_script(

            'wccie-admin-script',

            'wccie_params',

            array(

                'ajax_url' => admin_url('admin-ajax.php'),

                'nonce' => wp_create_nonce('wccie-nonce'),

                'export_coupons_nonce' => wp_create_nonce('wccie-export-coupons'),

                'import_coupons_nonce' => wp_create_nonce('wccie-import-coupons'),

                'i18n' => array(

                    'export_success' => __('Esportazione completata con successo!', 'wc-coupon-importer-exporter'),

                    'export_error' => __('Si è verificato un errore durante l\'esportazione.', 'wc-coupon-importer-exporter'),

                    'import_success' => __('Importazione completata con successo!', 'wc-coupon-importer-exporter'),

                    'import_error' => __('Si è verificato un errore durante l\'importazione.', 'wc-coupon-importer-exporter'),

                    'confirm_import' => __('Sei sicuro di voler importare i coupon? Questa operazione potrebbe sovrascrivere i coupon esistenti.', 'wc-coupon-importer-exporter'),

                )

            )

        );

    }



    /**

     * Renderizza la pagina di amministrazione

     */

    public function render_admin_page() {

        ?>

        <div class="wrap wccie-admin-wrap">

            <h1><?php _e('Importa/Esporta Coupon WooCommerce', 'wc-coupon-importer-exporter'); ?></h1>

            

            <div class="wccie-tabs">

                <ul class="wccie-tabs-nav">

                    <li class="active"><a href="#export-tab"><?php _e('Esporta', 'wc-coupon-importer-exporter'); ?></a></li>

                    <li><a href="#import-tab"><?php _e('Importa', 'wc-coupon-importer-exporter'); ?></a></li>

                </ul>

                

                <div class="wccie-tabs-content">

                    <!-- Tab Esportazione -->

                    <div id="export-tab" class="wccie-tab-content active">

                        <h2><?php _e('Esporta Coupon', 'wc-coupon-importer-exporter'); ?></h2>

                        <p><?php _e('Esporta i coupon di WooCommerce in un file CSV.', 'wc-coupon-importer-exporter'); ?></p>

                        

                        <form id="wccie-export-form" method="post">

                            <table class="form-table">

                                <tr>

                                    <th scope="row"><?php _e('Includi log di utilizzo', 'wc-coupon-importer-exporter'); ?></th>

                                    <td>

                                        <label>

                                            <input type="checkbox" name="include_usage_logs" value="1" checked>

                                            <?php _e('Includi i log di utilizzo dei coupon', 'wc-coupon-importer-exporter'); ?>

                                        </label>

                                    </td>

                                </tr>

                                <tr>

                                    <th scope="row"><?php _e('Stato coupon', 'wc-coupon-importer-exporter'); ?></th>

                                    <td>

                                        <select name="coupon_status">

                                            <option value="all"><?php _e('Tutti', 'wc-coupon-importer-exporter'); ?></option>

                                            <option value="publish"><?php _e('Pubblicati', 'wc-coupon-importer-exporter'); ?></option>

                                            <option value="draft"><?php _e('Bozze', 'wc-coupon-importer-exporter'); ?></option>

                                            <option value="pending"><?php _e('In attesa', 'wc-coupon-importer-exporter'); ?></option>

                                            <option value="trash"><?php _e('Nel cestino', 'wc-coupon-importer-exporter'); ?></option>

                                        </select>

                                    </td>

                                </tr>

                            </table>

                            

                            <p class="submit">

                                <button type="submit" class="button button-primary" id="wccie-export-button">

                                    <?php _e('Esporta Coupon', 'wc-coupon-importer-exporter'); ?>

                                </button>

                                <span class="spinner"></span>

                            </p>

                        </form>

                        

                        <div id="wccie-export-result" class="wccie-result-box" style="display: none;"></div>

                    </div>

                    

                    <!-- Tab Importazione -->

                    <div id="import-tab" class="wccie-tab-content">

                        <h2><?php _e('Importa Coupon', 'wc-coupon-importer-exporter'); ?></h2>

                        <p><?php _e('Importa i coupon di WooCommerce da un file CSV.', 'wc-coupon-importer-exporter'); ?></p>

                        

                        <form id="wccie-import-form" method="post" enctype="multipart/form-data">

                            <table class="form-table">

                                <tr>

                                    <th scope="row"><?php _e('File CSV', 'wc-coupon-importer-exporter'); ?></th>

                                    <td>

                                        <input type="file" name="import_file" id="wccie-import-file" accept=".csv">

                                        <p class="description"><?php _e('Seleziona un file CSV esportato con questo plugin.', 'wc-coupon-importer-exporter'); ?></p>

                                    </td>

                                </tr>

                                <tr>

                                    <th scope="row"><?php _e('Opzioni di importazione', 'wc-coupon-importer-exporter'); ?></th>

                                    <td>

                                        <label>

                                            <input type="checkbox" name="update_existing" value="1" checked>

                                            <?php _e('Aggiorna coupon esistenti', 'wc-coupon-importer-exporter'); ?>

                                        </label>

                                        <br>

                                        <label>

                                            <input type="checkbox" name="import_usage_logs" value="1" checked>

                                            <?php _e('Importa log di utilizzo (se presenti nel file)', 'wc-coupon-importer-exporter'); ?>

                                        </label>

                                    </td>

                                </tr>

                            </table>

                            

                            <p class="submit">

                                <button type="submit" class="button button-primary" id="wccie-import-button">

                                    <?php _e('Importa Coupon', 'wc-coupon-importer-exporter'); ?>

                                </button>

                                <span class="spinner"></span>

                            </p>

                        </form>

                        

                        <div id="wccie-import-result" class="wccie-result-box" style="display: none;"></div>

                    </div>

                </div>

            </div>

        </div>

        <?php

    }



    /**

     * Gestisce l'esportazione dei coupon

     */

    public function handle_export_coupons() {

        // Verifica nonce

        check_ajax_referer('wccie-export-coupons', 'nonce');

        

        // Verifica permessi

        if (!current_user_can('manage_woocommerce')) {

            wp_send_json_error(array('message' => __('Permessi insufficienti.', 'wc-coupon-importer-exporter')));

            return;

        }

        

        // Ottieni parametri

        $include_usage_logs = isset($_POST['include_usage_logs']) ? (bool) $_POST['include_usage_logs'] : false;

        $coupon_status = isset($_POST['coupon_status']) ? sanitize_text_field($_POST['coupon_status']) : 'all';

        

        // Inizializza l'esportatore

        require_once WCCIE_PLUGIN_DIR . 'includes/class-wccie-exporter.php';

        $exporter = new WCCIE_Exporter();

        

        // Esegui l'esportazione

        $result = $exporter->export_coupons($coupon_status, $include_usage_logs);

        

        if (is_wp_error($result)) {

            wp_send_json_error(array('message' => $result->get_error_message()));

            return;

        }

        

        wp_send_json_success(array(

            'message' => __('Esportazione completata con successo!', 'wc-coupon-importer-exporter'),

            'download_url' => $result['download_url'],

            'filename' => $result['filename']

        ));

    }



    /**

     * Gestisce l'importazione dei coupon

     */

    public function handle_import_coupons() {

        // Verifica nonce

        check_ajax_referer('wccie-import-coupons', 'nonce');

        

        // Verifica permessi

        if (!current_user_can('manage_woocommerce')) {

            wp_send_json_error(array('message' => __('Permessi insufficienti.', 'wc-coupon-importer-exporter')));

            return;

        }

        

        // Verifica file

        if (!isset($_FILES['import_file']) || empty($_FILES['import_file']['tmp_name'])) {

            wp_send_json_error(array('message' => __('Nessun file caricato.', 'wc-coupon-importer-exporter')));

            return;

        }

        

        // Ottieni parametri

        $update_existing = isset($_POST['update_existing']) ? (bool) $_POST['update_existing'] : false;

        $import_usage_logs = isset($_POST['import_usage_logs']) ? (bool) $_POST['import_usage_logs'] : false;

        

        // Inizializza l'importatore

        require_once WCCIE_PLUGIN_DIR . 'includes/class-wccie-importer.php';

        $importer = new WCCIE_Importer();

        

        // Esegui l'importazione

        $result = $importer->import_coupons($_FILES['import_file']['tmp_name'], $update_existing, $import_usage_logs);

        

        if (is_wp_error($result)) {

            wp_send_json_error(array('message' => $result->get_error_message()));

            return;

        }

        

        wp_send_json_success(array(

            'message' => sprintf(

                __('Importazione completata con successo! %d coupon importati, %d aggiornati, %d saltati.', 'wc-coupon-importer-exporter'),

                $result['imported'],

                $result['updated'],

                $result['skipped']

            )

        ));

    }

} 