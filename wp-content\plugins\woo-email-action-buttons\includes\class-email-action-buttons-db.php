<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe per la gestione del database dei pulsanti email
 */
class Email_Action_Buttons_DB {
    
    private $table_name;
    
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'woo_email_action_buttons';
    }
    
    /**
     * Crea le tabelle del database
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE {$this->table_name} (
            id int(11) NOT NULL AUTO_INCREMENT,
            button_name varchar(255) NOT NULL,
            button_description text,
            order_statuses longtext,
            email_content longtext,
            dashicon_code varchar(100) DEFAULT 'dashicons-email',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Verifica se la tabella è stata creata correttamente
        if ($wpdb->get_var("SHOW TABLES LIKE '{$this->table_name}'") != $this->table_name) {
            weab_log('Errore nella creazione della tabella: ' . $this->table_name, 'error');
        }
    }
    
    /**
     * Inserisce un nuovo pulsante email
     */
    public function insert_button($data) {
        global $wpdb;
        
        $result = $wpdb->insert(
            $this->table_name,
            array(
                'button_name' => weab_sanitize_input($data['button_name']),
                'button_description' => weab_sanitize_input($data['button_description'], 'textarea'),
                'order_statuses' => maybe_serialize($data['order_statuses']),
                'email_content' => weab_sanitize_input($data['email_content'], 'html'),
                'dashicon_code' => weab_sanitize_input($data['dashicon_code'])
            ),
            array('%s', '%s', '%s', '%s', '%s')
        );
        
        if ($result === false) {
            weab_log('Errore nell\'inserimento del pulsante: ' . $wpdb->last_error, 'error');
            return false;
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Aggiorna un pulsante email esistente
     */
    public function update_button($id, $data) {
        global $wpdb;
        
        $result = $wpdb->update(
            $this->table_name,
            array(
                'button_name' => weab_sanitize_input($data['button_name']),
                'button_description' => weab_sanitize_input($data['button_description'], 'textarea'),
                'order_statuses' => maybe_serialize($data['order_statuses']),
                'email_content' => weab_sanitize_input($data['email_content'], 'html'),
                'dashicon_code' => weab_sanitize_input($data['dashicon_code'])
            ),
            array('id' => intval($id)),
            array('%s', '%s', '%s', '%s', '%s'),
            array('%d')
        );
        
        if ($result === false) {
            weab_log('Errore nell\'aggiornamento del pulsante: ' . $wpdb->last_error, 'error');
            return false;
        }
        
        return $result;
    }
    
    /**
     * Elimina un pulsante email
     */
    public function delete_button($id) {
        global $wpdb;
        
        $result = $wpdb->delete(
            $this->table_name,
            array('id' => intval($id)),
            array('%d')
        );
        
        if ($result === false) {
            weab_log('Errore nell\'eliminazione del pulsante: ' . $wpdb->last_error, 'error');
            return false;
        }
        
        return $result;
    }
    
    /**
     * Ottiene un pulsante email per ID
     */
    public function get_button($id) {
        global $wpdb;
        
        $button = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$this->table_name} WHERE id = %d", intval($id))
        );
        
        if ($button) {
            $button->order_statuses = maybe_unserialize($button->order_statuses);
        }
        
        return $button;
    }
    
    /**
     * Ottiene tutti i pulsanti email
     */
    public function get_all_buttons($limit = null, $offset = 0) {
        global $wpdb;
        
        $sql = "SELECT * FROM {$this->table_name} ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= $wpdb->prepare(" LIMIT %d OFFSET %d", intval($limit), intval($offset));
        }
        
        $buttons = $wpdb->get_results($sql);
        
        // Deserializza gli stati ordine per ogni pulsante
        foreach ($buttons as $button) {
            $button->order_statuses = maybe_unserialize($button->order_statuses);
        }
        
        return $buttons;
    }
    
    /**
     * Conta il numero totale di pulsanti
     */
    public function count_buttons() {
        global $wpdb;
        
        return $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name}");
    }
    
    /**
     * Ottiene i pulsanti per uno stato ordine specifico
     */
    public function get_buttons_for_status($order_status) {
        global $wpdb;
        
        $buttons = $this->get_all_buttons();
        $matching_buttons = array();
        
        foreach ($buttons as $button) {
            if (is_array($button->order_statuses) && in_array($order_status, $button->order_statuses)) {
                $matching_buttons[] = $button;
            }
        }
        
        return $matching_buttons;
    }
    
    /**
     * Elimina tutte le tabelle del plugin (per uninstall)
     */
    public function drop_tables() {
        global $wpdb;
        
        $wpdb->query("DROP TABLE IF EXISTS {$this->table_name}");
    }
    
    /**
     * Verifica se la tabella esiste
     */
    public function table_exists() {
        global $wpdb;
        
        return $wpdb->get_var("SHOW TABLES LIKE '{$this->table_name}'") == $this->table_name;
    }
}
