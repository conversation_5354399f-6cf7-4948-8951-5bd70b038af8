#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-05 06:10+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: \n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/"

#. Message about the purchase of the pro version
#: classes/tabs/class-wc-phone-orders-help-page.php:68
#, php-format
msgid ""
"<a href=%1$s target=_blank>Pro version</a> allows you to pay as customer, "
"via checkout page. You can pay directly from admin area too – use <a "
"href=%2$s target=_blank>this free plugin</a>. They support Stripe and "
"Authorize.Net."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:1719
msgid "A valid email address is required"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:208
msgid "above customer details"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:608
msgid "Add"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:597
#: classes/tabs/class-wc-phone-orders-add-order-page.php:599
msgid "Add a note"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:88
msgid "Add Coupon"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:482
msgid "Add coupon"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:98
#: classes/tabs/class-wc-phone-orders-add-order-page.php:460
msgid "Add custom product"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:159
#: classes/tabs/class-wc-phone-orders-add-order-page.php:483
msgid "Add discount"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:169
msgid "Add Fee"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:293
msgid "Add Gift Card"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:541
msgid "Add meta"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:35
msgid "Add order"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:228
msgid "Add products to the cart"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:486
msgid "Add shipping"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:241
#: classes/tabs/class-wc-phone-orders-add-order-page.php:299
#: classes/tabs/class-wc-phone-orders-add-order-page.php:610
msgid "Add to cart"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:472
msgid "Add to Order"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:146
msgid "Address Validation Service API Key (USPS Username)"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:825
msgid "Address1"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:829
msgid "Address2"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:612
msgid "Advanced search"
msgstr ""

#. Author of the plugin
msgid "AlgolPlus"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:159
msgid "Allow to create orders without payment"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:297
msgid "Allow to create orders without shipping"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:285
msgid "Allow to edit shipping cost"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:288
msgid "Allow to edit shipping title"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:234
msgid "Allow to input fractional qty"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:100
msgid "API Key is invalid"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:99
msgid "API Key is valid"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:81
msgid "Apply"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:87
msgid "Automatically update Shipping/Taxes/Totals"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:622
msgid ""
"Barcode mode enabled! Product search works only after pressing the Enter key"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:212
msgid "below customer details"
msgstr ""

#: classes/tabs/class-wc-phone-orders-tabs-helper.php:231
msgid "Billing address"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:386
msgid "Billing Details"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:627
msgid "Billing Schedule"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:464
msgid "Browse shop and add products to cart"
msgstr ""

#. A message stating button "Create Order" does nothing
#: classes/tabs/class-wc-phone-orders-help-page.php:78
msgid "Button \"Create Order\" does nothing"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:81
#: classes/tabs/class-wc-phone-orders-add-order-page.php:417
#: classes/tabs/class-wc-phone-orders-add-order-page.php:508
msgid "Buy Pro version"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:272
msgid "Caching locations/categories/tags"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:262
msgid "Caching search results"
msgstr ""

#. A message stating that the product cannot be added because it is unavailable.
#: classes/class-wc-phone-orders-fill-cart.php:66
#, php-format
msgid "Can not add %1$d &quot;%2$s&quot; because it is not available."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:1046
msgid "Can not create new product"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:80
#: classes/tabs/class-wc-phone-orders-add-order-page.php:242
#: classes/tabs/class-wc-phone-orders-add-order-page.php:298
#: classes/tabs/class-wc-phone-orders-add-order-page.php:611
msgid "Cancel"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:224
msgid "Cart Items"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:107
msgid "Category"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:98
msgid "Check"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:245
#: classes/tabs/class-wc-phone-orders-add-order-page.php:551
msgid "Choose an option"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:240
msgid "Choose gifts"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:642
msgid "Choose interval"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:540
msgid "Choose meta field"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:643
msgid "Choose period"
msgstr ""

#: classes/class-wc-phone-orders-cart-updater.php:753
msgid "Choose your gift"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:833
msgid "City"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:547
msgid "Collapse edit meta"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:178
msgid "Collapse WordPress menu"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:86
msgid "Common"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:821
msgid "Company"
msgstr ""

#. Headings of information that may be in the documentation
#: classes/tabs/class-wc-phone-orders-help-page.php:90
msgid "Compatibility/Code samples/Pro version"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:227
#: classes/tabs/class-wc-phone-orders-add-order-page.php:307
#: classes/tabs/class-wc-phone-orders-add-order-page.php:473
msgid "Configure product"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:156
msgid "Copied from order"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:221
msgid "Copy from billing address"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:617
msgid "Copy to clipboard"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:563
msgid "Copy url to populate cart"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:478
#: classes/tabs/class-wc-phone-orders-add-order-page.php:535
msgid "Cost"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:817
msgid "Country"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:166
msgid "Coupon Name"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:261
msgid "Coupons"
msgstr ""

#: classes/tabs/class-wc-phone-orders-tabs-helper.php:302
msgid "Coupons applied"
msgstr ""

#. Description of the plugin
msgid "Create manual/phone orders in WooCommerce quickly"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:503
msgid "Create new order"
msgstr ""

#: classes/class-wc-phone-orders-loader.php:69
#: classes/class-wc-phone-orders-loader.php:460
msgid "Create Order"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:498
msgid "Create order"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:112
msgid "Create product"
msgstr ""

#. Who created it order
#: classes/tabs/class-wc-phone-orders-add-order-page.php:1606
#, php-format
msgid "Created in Phone Orders by: %s."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:546
msgid "Custom meta field key"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:542
msgid "Custom meta field value"
msgstr ""

#: classes/tabs/class-wc-phone-orders-list-log.php:58
msgid "Customer"
msgstr ""

#: classes/tabs/class-wc-phone-orders-list-log.php:209
msgid "Customer login"
msgstr ""

#: classes/tabs/class-wc-phone-orders-tabs-helper.php:256
msgid "Customer note"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:593
msgid "Customer provided note"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:268
msgid "Date"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:291
msgid "Default shipping method"
msgstr ""

#: classes/tabs/abstract-wc-phone-orders-tab.php:642
msgctxt "default site language"
msgid "Site Default"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:528
msgid "Delete item"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:264
#: classes/tabs/class-wc-phone-orders-settings-page.php:277
msgid "Disable cache"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:485
#: classes/tabs/class-wc-phone-orders-add-order-page.php:621
#: classes/tabs/class-wc-phone-orders-list-log.php:60
msgid "Discount"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:106
msgid ""
"Display autocomplete results only for selected countries (Google Map API)"
msgstr ""

#: classes/class-wc-phone-orders-loader.php:464
msgid "Docs"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:657
msgid "Don't apply pricing rules"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:174
msgid "Don't close popup on click outside"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:122
msgid "Don't send order emails"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:188
msgid "Done"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:338
msgid "Draft"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:571
msgid "Duplicate order"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:766
#: classes/tabs/class-wc-phone-orders-add-order-page.php:809
msgid "E-mail"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:187
msgid "Edit address"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:538
msgid "Edit meta"
msgstr ""

#: classes/tabs/class-wc-phone-orders-list-log.php:181
msgid "Edit Phone Order"
msgstr ""

#: classes/tabs/class-wc-phone-orders-tabs-helper.php:237
msgid "Email"
msgstr ""

#: classes/tabs/class-wc-phone-orders-tools-page.php:223
msgid "Error writing file"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:484
msgid "Fee"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:171
msgid "Fee amount"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:170
msgid "Fee name"
msgstr ""

#: classes/tabs/class-wc-phone-orders-list-log.php:61
msgid "Fees"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:381
msgid "Find a customer"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:377
msgid "Find or create a customer"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:302
msgid "Find products ..."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:470
msgid "Find products..."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:770
#: classes/tabs/class-wc-phone-orders-add-order-page.php:801
msgid "First name"
msgstr ""

#: classes/class-wc-phone-orders-loader.php:378
msgid "five star"
msgstr ""

#: classes/class-wc-phone-shipping-method.php:17
#: classes/class-wc-phone-shipping-method.php:51
msgid "Free Shipping [Phone Orders]"
msgstr ""

#: classes/class-wc-phone-shipping-method.php:18
msgid "Free Shipping in admin area only, for Phone Orders "
msgstr ""

#: classes/tabs/class-wc-phone-orders-tools-page.php:31
msgid "Get report"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:646
msgid "Gift Card"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:294
msgid "Gift card number"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:243
msgid "gifts left"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:97
msgid "Google Map API Key"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:613
msgid "granted by coupon"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:420
msgid "Guest"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:647
msgid "Have a gift card?"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:111
msgid "Height"
msgstr ""

#: classes/tabs/class-wc-phone-orders-help-page.php:16
msgid "Help"
msgstr ""

#: classes/tabs/class-wc-phone-orders-help-page.php:34
msgid "helpdesk system"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:102
msgid "Hide results without house number"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:256
msgid "Hide tax line for item"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:632
#: classes/tabs/class-wc-phone-orders-add-order-page.php:675
msgctxt "hour placeholder"
msgid "h"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:263
#: classes/tabs/class-wc-phone-orders-settings-page.php:276
msgid "hours"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:101
msgid "How to get api key"
msgstr ""

#: classes/tabs/class-wc-phone-orders-help-page.php:65
msgid "How to pay order?"
msgstr ""

#: classes/tabs/class-wc-phone-orders-help-page.php:40
msgid "How to set default country/state for new customers?"
msgstr ""

#. Author URI of the plugin
msgid "http://algolplus.com/"
msgstr ""

#: classes/tabs/class-wc-phone-orders-help-page.php:48
msgid ""
"I can't add new customer, I see popup with message “Please enter an account "
"password”"
msgstr ""

#: classes/tabs/class-wc-phone-orders-help-page.php:58
msgid "I don't see Free Shipping [Phone Orders] in popup"
msgstr ""

#: classes/tabs/class-wc-phone-orders-tools-page.php:33
msgid ""
"If you have problems with the plugin, you should submit a <a href=\"https:"
"//algolplus.freshdesk.com/support/tickets/new\" target=\"_blank\">new "
"support request</a> and attach the generated report to it."
msgstr ""

#. 1: WooCommerce 2:: five stars
#: classes/class-wc-phone-orders-loader.php:370
#, php-format
msgid ""
"If you like %1$s please leave us a %2$s rating. Thank you so much in advance!"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:125
#: classes/tabs/class-wc-phone-orders-add-order-page.php:129
#: classes/tabs/class-wc-phone-orders-add-order-page.php:192
#: classes/tabs/class-wc-phone-orders-add-order-page.php:196
msgid "Input your address"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:169
msgid "Interface"
msgstr ""

#. A message that the invoice for order has been sent to email
#: classes/tabs/class-wc-phone-orders-add-order-page.php:1757
#, php-format
msgid "Invoice for order #%1$s has been sent to %2$s"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:1735
#: classes/tabs/class-wc-phone-orders-add-order-page.php:1739
msgid "Invoice manually sent to customer from Phone Order."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:477
msgid "Item"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:606
#: classes/tabs/class-wc-phone-orders-list-log.php:26
msgid "item"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:289
msgid "Item removed from cart"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:272
#: classes/tabs/class-wc-phone-orders-list-log.php:59
msgid "Items"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:605
#: classes/tabs/class-wc-phone-orders-list-log.php:27
msgid "items"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:774
#: classes/tabs/class-wc-phone-orders-add-order-page.php:805
msgid "Last name"
msgstr ""

#: classes/tabs/abstract-wc-phone-orders-tab.php:46
msgid "Latitude"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:187
msgid "Layout"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:109
msgid "Length"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:102
msgid "Line item name"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:126
#: classes/tabs/class-wc-phone-orders-settings-page.php:304
#: classes/tabs/class-wc-phone-orders-add-order-page.php:95
#: classes/tabs/class-wc-phone-orders-add-order-page.php:105
#: classes/tabs/class-wc-phone-orders-add-order-page.php:142
#: classes/tabs/class-wc-phone-orders-add-order-page.php:207
#: classes/tabs/class-wc-phone-orders-add-order-page.php:684
#: classes/tabs/class-wc-phone-orders-add-order-page.php:701
#: classes/tabs/class-wc-phone-orders-add-order-page.php:709
msgid "List is empty."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:147
msgid "Load customer details from order"
msgstr ""

#: classes/tabs/class-wc-phone-orders-log-page.php:19
msgid "Log"
msgstr ""

#: classes/tabs/class-wc-phone-orders-log-page.php:25
msgid "Log page"
msgstr ""

#: classes/tabs/abstract-wc-phone-orders-tab.php:51
msgid "Longitude"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:562
msgid "Manual Discount"
msgstr ""

#: classes/tabs/class-wc-phone-orders-tabs-helper.php:309
msgid "Manual discount"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:637
#: classes/tabs/class-wc-phone-orders-add-order-page.php:676
msgctxt "minute placeholder"
msgid "m"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:1055
msgid "Missing data"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:416
msgid "Need extra features?"
msgstr ""

#: classes/tabs/class-wc-phone-orders-help-page.php:31
msgid "Need help? Submit ticket to"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:80
msgid "Need more settings?"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:123
#: classes/tabs/class-wc-phone-orders-add-order-page.php:385
msgid "New customer"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:630
msgid "Next Payment"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:398
msgid "No billing address was provided."
msgstr ""

#: classes/tabs/abstract-wc-phone-orders-tab.php:673
msgid "No default shipping method"
msgstr ""

#: classes/tabs/class-wc-phone-orders-log-page.php:31
msgid "No items found."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:300
msgid "No products found"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:402
msgid "No shipping address was provided."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:1429
msgid "No shipping method has been selected."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:177
#: classes/tabs/class-wc-phone-orders-add-order-page.php:489
msgid "No shipping methods available"
msgstr ""

#: classes/class-wc-phone-orders-cart-updater.php:1661
#: classes/class-wc-phone-orders-cart-updater.php:1698
#: classes/tabs/abstract-wc-phone-orders-tab.php:494
#: classes/tabs/abstract-wc-phone-orders-tab.php:515
msgid "No value"
msgstr ""

#: classes/tabs/class-wc-phone-orders-tabs-helper.php:340
msgid "Not selected!"
msgstr ""

#: classes/tabs/abstract-wc-phone-orders-tab.php:545
msgid "Not taxable"
msgstr ""

#. A message stating that only a certain number of items can be purchased
#: classes/tabs/class-wc-phone-orders-add-order-page.php:531
#, php-format
msgid "Only %s items can be purchased"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:90
#: classes/tabs/class-wc-phone-orders-add-order-page.php:151
#: classes/tabs/class-wc-phone-orders-add-order-page.php:421
#: classes/tabs/class-wc-phone-orders-add-order-page.php:520
msgid "Oops! No elements found. Consider changing the search query."
msgstr ""

#. Order created message
#: classes/tabs/class-wc-phone-orders-add-order-page.php:1370
#, php-format
msgid "Order #%s created"
msgstr ""

#. Order details title
#: classes/tabs/class-wc-phone-orders-add-order-page.php:458
#, php-format
msgid "Order %s details"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:689
msgid "Order currency"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:671
msgid "Order date"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:204
msgid "Order fields position"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:439
msgid "Order History &rarr;"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:250
msgid "Order history for"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:1708
msgid "Order not found"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:264
#: classes/tabs/class-wc-phone-orders-list-log.php:57
msgid "Order number"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:681
msgid "Order status"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:495
msgid "Order Total"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:443
msgid "Orders"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:604
msgid "Package"
msgstr ""

#: classes/tabs/class-wc-phone-orders-tabs-helper.php:345
msgid "Package "
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:184
msgid "Package contents"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:706
msgid "Payment method"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:280
msgid "Payment Type"
msgstr ""

#: classes/tabs/class-wc-phone-orders-tabs-helper.php:243
#: classes/tabs/class-wc-phone-orders-add-order-page.php:845
msgid "Phone"
msgstr ""

#: classes/class-wc-phone-orders-loader.php:481
msgid "Phone order"
msgstr ""

#: classes/class-wc-phone-orders-main.php:187
#: classes/class-wc-phone-orders-main.php:188
msgid "Phone Orders"
msgstr ""

#. Name of the plugin
#: classes/class-wc-phone-orders-loader.php:376
msgid "Phone Orders for WooCommerce"
msgstr ""

#. A message that phone orders for WooCommerce are available on this page
#: classes/class-wc-phone-orders-loader.php:255
#, php-format
msgid ""
"Phone Orders For WooCommerce is available <a href=\"%s\">on this page</a>."
msgstr ""

#: phone-orders-for-woocommerce.php:62
msgid "Phone Orders for WooCommerce requires active WooCommerce!"
msgstr ""

#: classes/class-wc-phone-orders-main.php:209
msgid "PHP files are not allowed"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:137
#: classes/tabs/class-wc-phone-orders-add-order-page.php:202
#: classes/tabs/class-wc-phone-orders-add-order-page.php:572
msgid "Please fill out all required fields!"
msgstr ""

#. phpcs:ignore WordPress.Security.EscapeOutput.UnsafePrintingFunction
#: phone-orders-for-woocommerce.php:36
msgid ""
"Please, <a href=\"plugins.php\">deactivate</a> Free version of Phone Orders "
"for WooCommerce!"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:558
msgid "Please, choose all attributes."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:322
msgid "Please, enable coupons to use discounts."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:131
#: classes/tabs/class-wc-phone-orders-add-order-page.php:198
msgid "Please, enter valid Places API key at tab Settings"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:449
msgid "Please, fill empty required billing/shipping fields for customer"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:1415
msgid "Please, remove following items from the cart to continue"
msgstr ""

#. A message documentation topics
#: classes/tabs/class-wc-phone-orders-help-page.php:93
#, php-format
msgid ""
"Please, review these topics in <a href=%s target=_blank>our documentation</a>"
"."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:445
msgid "Please, select/create customer"
msgstr ""

#: classes/tabs/class-wc-phone-orders-help-page.php:52
msgid ""
"Please, visit >Woocommerce>Settings, select tab “Accounts & Privacy” and "
"mark checkbox “When creating an account, automatically generate an account "
"password”."
msgstr ""

#: classes/tabs/class-wc-phone-orders-help-page.php:59
msgid ""
"Please, visit >WooCommerce>Settings>Shipping and add shipping method for "
"necessary zones."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:841
msgid "Postcode"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:103
msgid "Price per item"
msgstr ""

#: classes/tabs/class-wc-phone-orders-tabs-helper.php:262
#: classes/tabs/class-wc-phone-orders-add-order-page.php:598
msgid "Private note"
msgstr ""

#. A message conflict with another plugin
#: classes/tabs/class-wc-phone-orders-help-page.php:81
#, php-format
msgid ""
"Probably, there is a conflict with another plugin. <a href=%s target=_blank>"
"Please, check javascript errors at first</a>."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:290
msgid "Product is out of stock"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:297
#: classes/tabs/class-wc-phone-orders-add-order-page.php:662
msgid "Products history"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:425
msgid "Profile &rarr;"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:479
#: classes/tabs/class-wc-phone-orders-add-order-page.php:536
msgid "Qty"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:104
msgid "Quantity"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:554
msgid "Read more"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:493
msgid "Recalculate"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:271
msgid "References"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:82
#: classes/tabs/class-wc-phone-orders-add-order-page.php:607
msgid "Remove"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:118
msgid "required by some pricing plugins"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:183
msgid "Reset"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:265
#: classes/tabs/class-wc-phone-orders-settings-page.php:278
msgid "Reset cache"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:651
msgid "Restore removed gifts"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:406
msgid "Same as shipping address."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:83
msgid "Save"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:72
msgid "Save Changes"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:122
msgid "Save customer"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:233
msgid "Scrollable cart contents"
msgstr ""

#: classes/tabs/class-wc-phone-orders-log-page.php:30
#: classes/tabs/class-wc-phone-orders-log-page.php:115
#: classes/tabs/class-wc-phone-orders-list-log.php:43
msgid "Search"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:89
msgctxt "search coupons"
msgid "Type to search"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:663
msgid "Select customer to see purchased products"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:166
#: classes/tabs/class-wc-phone-orders-add-order-page.php:106
#: classes/tabs/class-wc-phone-orders-add-order-page.php:124
#: classes/tabs/class-wc-phone-orders-add-order-page.php:191
msgid "Select option"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:233
msgid ""
"Select products and add them to the cart and close this window. Please note "
"that any special prices for the customer will be applied after adding the "
"product to the order and closing this window."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:229
msgid ""
"Select the options, add the item to the cart and close this window. Please "
"note that any special prices for the customer will be applied after adding "
"the product to the order and closing this window."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:301
#: classes/tabs/class-wc-phone-orders-add-order-page.php:609
msgid "Selected"
msgstr ""

#: classes/tabs/abstract-wc-phone-orders-tab.php:669
msgid "Selected by WooCommerce"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:502
msgid "Send invoice"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:91
msgid "Set payment method for created order"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:95
msgid "Set status for created order"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:18
msgid "Settings"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:73
msgid "Settings have been updated"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:74
msgid "Settings have not been updated"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:390
msgid "Ship to a different address?"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:284
#: classes/tabs/class-wc-phone-orders-add-order-page.php:487
#: classes/tabs/class-wc-phone-orders-list-log.php:62
msgid "Shipping"
msgstr ""

#: classes/tabs/class-wc-phone-orders-tabs-helper.php:249
msgid "Shipping address"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:394
msgid "Shipping Details"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:176
#: classes/tabs/class-wc-phone-orders-add-order-page.php:488
msgid "Shipping method"
msgstr ""

#: classes/tabs/class-wc-phone-orders-help-page.php:30
msgid "Short FAQ"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:225
msgid "Show button \"Copy url to populate cart\""
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:242
msgid "Show column \"Discount\""
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:196
msgid "Show currency selector"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:255
msgid "Show detailed taxes"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:247
msgid "Show icon for phone orders in orders list"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:188
msgid "Show order date/time"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:192
msgid "Show order status"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:200
msgid "Show payment method"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:170
msgid "Show records for last X days in log"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:644
msgid "Sign-up Fee"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:99
#: classes/tabs/class-wc-phone-orders-add-order-page.php:529
msgid "SKU"
msgstr ""

#: classes/tabs/abstract-wc-phone-orders-tab.php:549
msgid "Standard rate"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:837
msgid "State/County"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:276
msgid "Status"
msgstr ""

#: classes/tabs/abstract-wc-phone-orders-tab.php:56
msgid "Street number"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:481
msgid "Subtotal"
msgstr ""

#: classes/class-wc-phone-orders-loader.php:468
msgid "Support"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:114
msgid "Switch customer during cart calculations"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:254
msgid "Tax"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:100
msgid "Tax class"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:494
msgid "Taxes"
msgstr ""

#: classes/class-wc-phone-orders-loader.php:381
msgid "Thanks :)"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:110
msgid "The maximum number of countries that can be selected"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:251
msgid "There are no records to show"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:1748
msgid "There was an error sending the email"
msgstr ""

#: classes/tabs/class-wc-phone-orders-list-log.php:55
msgid "Time"
msgstr ""

#: classes/class-wc-phone-shipping-method.php:48
msgid "Title"
msgstr ""

#: classes/class-wc-phone-shipping-method.php:50
msgid "Title to be displayed"
msgstr ""

#: classes/tabs/class-wc-phone-orders-tools-page.php:16
msgid "Tools"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:258
#: classes/tabs/class-wc-phone-orders-add-order-page.php:480
msgid "Total"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:284
msgid "Total Amount"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:256
msgid "Total Orders"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:257
msgid "Total Paid"
msgstr ""

#: classes/tabs/class-wc-phone-orders-list-log.php:63
msgid "Totals"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:435
#: classes/tabs/class-wc-phone-orders-add-order-page.php:580
msgid "Type search phrase to see results"
msgstr ""

#: classes/tabs/abstract-wc-phone-orders-tab.php:124
msgid "Unexpected output"
msgstr ""

#. A message about an unknown product
#: classes/class-wc-phone-orders-fill-cart.php:56
msgid "Unknown product &quot;%d&quot;."
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:567
msgid "Url has been copied to clipboard"
msgstr ""

#: classes/tabs/class-wc-phone-orders-list-log.php:56
msgid "Username"
msgstr ""

#: classes/class-wc-phone-orders-main.php:153
#: classes/tabs/class-wc-phone-orders-settings-page.php:154
msgid "USPS"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:534
msgid "Variation ID"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:501
msgid "View draft"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:500
msgid "View order"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:429
msgid "View other orders &rarr;"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:504
msgid "Want to pay order as customer?"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:229
msgid "warning : this feature is not compatible with discounts"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:108
#: classes/tabs/class-wc-phone-orders-add-order-page.php:553
#: classes/tabs/class-wc-phone-orders-add-order-page.php:655
msgid "Weight"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:110
msgid "Width"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:164
#: classes/tabs/class-wc-phone-orders-add-order-page.php:172
msgid "with tax"
msgstr ""

#: classes/tabs/class-wc-phone-orders-add-order-page.php:165
#: classes/tabs/class-wc-phone-orders-add-order-page.php:173
msgid "without tax"
msgstr ""

#: classes/tabs/class-wc-phone-orders-settings-page.php:246
msgid "WooCommerce"
msgstr ""

#. Ability to add a certain number of free products to the cart
#: classes/class-wc-phone-orders-cart-updater.php:750
msgid "You can add %d products for free to the cart."
msgstr ""

#. Message about the purchase of the pro version
#: classes/tabs/class-wc-phone-orders-help-page.php:43
#, php-format
msgid "You should buy <a href=%s target=_blank>Pro version</a>."
msgstr ""
