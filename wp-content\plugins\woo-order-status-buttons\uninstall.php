<?php
/**
 * Uninstall script for WooCommerce Order Status Buttons
 * 
 * This file is executed when the plugin is uninstalled (deleted) from WordPress.
 * It cleans up all plugin data including database tables and options.
 */

// If uninstall not called from WordPress, then exit
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Include the database class
require_once plugin_dir_path(__FILE__) . 'includes/class-order-status-buttons-db.php';

/**
 * Clean up plugin data
 */
function wosb_uninstall_cleanup() {
    global $wpdb;
    
    // Initialize database class
    $db = new Order_Status_Buttons_DB();
    
    // Drop the plugin table
    $db->drop_table();
    
    // Remove any plugin options (if we had any)
    delete_option('wosb_plugin_version');
    delete_option('wosb_settings');
    
    // Remove any transients
    delete_transient('wosb_order_statuses');
    delete_transient('wosb_button_cache');
    
    // Remove any user meta related to the plugin
    $wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'wosb_%'");
    
    // Clear any cached data
    wp_cache_flush();
    
    // Log the uninstall (if debug is enabled)
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('[WooCommerce Pulsanti Status Ordine] Plugin uninstalled and data cleaned up.');
    }
}

// Only run cleanup if we're sure this is an uninstall
if (defined('WP_UNINSTALL_PLUGIN')) {
    wosb_uninstall_cleanup();
}
