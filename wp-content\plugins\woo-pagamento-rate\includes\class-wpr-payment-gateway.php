<?php
/**
 * Classe del Gateway di Pagamento a Rate
 */
if (!class_exists('WPR_Payment_Gateway')) {
    class WPR_Payment_Gateway extends WC_Payment_Gateway {

        /**
         * Costruttore della classe
         */
        public function __construct() {
            $this->id                 = 'pagamento_rate';
            $this->icon               = apply_filters('woocommerce_pagamento_rate_icon', '');
            $this->has_fields         = false;
            $this->method_title       = __('Pagamento a Rate', 'woo-pagamento-rate');
            $this->method_description = __('Consente ai clienti di effettuare un pagamento a rate.', 'woo-pagamento-rate');
            
            // Carica le impostazioni
            $this->init_form_fields();
            $this->init_settings();
            
            // Definisci le proprietà del gateway
            $this->title        = $this->get_option('title');
            $this->description  = $this->get_option('description');
            $this->instructions = $this->get_option('instructions');
            $this->enabled      = $this->get_option('enabled');
            
            // Azioni
            add_action('woocommerce_update_options_payment_gateways_' . $this->id, array($this, 'process_admin_options'));
            add_action('woocommerce_thankyou_' . $this->id, array($this, 'thankyou_page'));
            
            // Azioni per l'email del cliente
            add_action('woocommerce_email_before_order_table', array($this, 'email_instructions'), 10, 3);
        }

        /**
         * Inizializza i campi del form delle impostazioni
         */
        public function init_form_fields() {
            $this->form_fields = apply_filters('wpr_payment_gateway_form_fields', array(
                'enabled' => array(
                    'title'   => __('Abilita/Disabilita', 'woo-pagamento-rate'),
                    'type'    => 'checkbox',
                    'label'   => __('Abilita Pagamento a Rate', 'woo-pagamento-rate'),
                    'default' => 'yes'
                ),
                'title' => array(
                    'title'       => __('Titolo', 'woo-pagamento-rate'),
                    'type'        => 'text',
                    'description' => __('Titolo che il cliente vedrà durante il checkout.', 'woo-pagamento-rate'),
                    'default'     => __('Pagamento a Rate', 'woo-pagamento-rate'),
                    'desc_tip'    => true,
                ),
                'description' => array(
                    'title'       => __('Descrizione', 'woo-pagamento-rate'),
                    'type'        => 'textarea',
                    'description' => __('Descrizione che il cliente vedrà durante il checkout.', 'woo-pagamento-rate'),
                    'default'     => __('Paga il tuo ordine in comode rate.', 'woo-pagamento-rate'),
                    'desc_tip'    => true,
                ),
                'instructions' => array(
                    'title'       => __('Istruzioni', 'woo-pagamento-rate'),
                    'type'        => 'textarea',
                    'description' => __('Istruzioni che verranno aggiunte alla pagina di ringraziamento e alle email.', 'woo-pagamento-rate'),
                    'default'     => __('Riceverai una email con i dettagli per procedere con il pagamento a rate.', 'woo-pagamento-rate'),
                    'desc_tip'    => true,
                ),
            ));
        }

        /**
         * Output per la pagina di ringraziamento
         */
        public function thankyou_page($order_id) {
            if ($this->instructions) {
                echo wp_kses_post(wpautop(wptexturize($this->instructions)));
            }
            
            // Invia l'email di pagamento a rate
            $this->trigger_payment_email($order_id);
        }

        /**
         * Aggiungi contenuto alle email
         */
        public function email_instructions($order, $sent_to_admin, $plain_text = false) {
            if ($this->instructions && !$sent_to_admin && $this->id === $order->get_payment_method() && $order->has_status('on-hold')) {
                echo wp_kses_post(wpautop(wptexturize($this->instructions)) . PHP_EOL);
            }
        }

        /**
         * Processo di pagamento.
         *
         * @param int $order_id
         * @return array
         */
        public function process_payment($order_id) {
            $order = wc_get_order($order_id);
            
            // Segna come "in sospeso" (on-hold)
            $order->update_status('on-hold', __('In attesa di pagamento a rate.', 'woo-pagamento-rate'));
            
            // Svuota il carrello
            WC()->cart->empty_cart();
            
            // Restituisci i dati necessari
            return array(
                'result'   => 'success',
                'redirect' => $this->get_return_url($order)
            );
        }
        
        /**
         * Trigger dell'email di pagamento a rate
         */
        protected function trigger_payment_email($order_id) {
            $mailer = WC()->mailer();
            $mails = $mailer->get_emails();
            
            if (!empty($mails)) {
                foreach ($mails as $mail) {
                    if (isset($mail->id) && $mail->id == 'wpr_payment_email') {
                        $mail->trigger($order_id);
                        break;
                    }
                }
            }
        }
    }
} 