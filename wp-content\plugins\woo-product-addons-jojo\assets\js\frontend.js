/**
 * Script JavaScript per la parte frontend del plugin
 */
(function($) {
    'use strict';

    // Inizializza il plugin quando il documento è pronto
    $(document).ready(function() {
        // Inizializza la gestione del pulsante "Aggiungi al carrello"
        initAddToCartButton();
        
        // Inizializza la gestione delle checkbox degli accessori
        initAccessoryCheckboxes();
        
        // Debug: Verifica che i parametri siano disponibili
        console.log('WPAJ Frontend Params:', typeof wpaj_frontend_params !== 'undefined' ? 'Disponibili' : 'Non disponibili');
    });

    /**
     * Inizializza la gestione del pulsante "Aggiungi al carrello"
     */
    function initAddToCartButton() {
        // Intercetta il click sul pulsante "Aggiungi al carrello"
        $('.single_add_to_cart_button').on('click', function(e) {
            // Verifica se ci sono accessori disponibili
            if ($('.accessory-checkbox-input').length === 0) {
                return;
            }
            
            // Previeni il comportamento predefinito
            e.preventDefault();
            
            // Ottieni l'ID del prodotto principale
            var productId = $('input[name="add-to-cart"]').val();
            if (!productId) {
                productId = $('button[name="add-to-cart"]').val();
            }
            
            // Debug: Verifica l'ID del prodotto
            console.log('Product ID:', productId);
            
            // Ottieni la quantità del prodotto principale
            var quantity = $('input[name="quantity"]').val();
            if (!quantity) {
                quantity = 1;
            }
            
            // Debug: Verifica la quantità
            console.log('Quantity:', quantity);
            
            // Ottieni gli accessori selezionati
            var accessories = [];
            $('.accessory-checkbox-input:checked').each(function() {
                accessories.push($(this).val());
            });
            
            // Debug: Verifica gli accessori selezionati
            console.log('Accessories:', accessories);
            
            // Aggiungi i prodotti al carrello
            addProductsToCart(productId, quantity, accessories);
        });
    }

    /**
     * Inizializza la gestione delle checkbox degli accessori
     */
    function initAccessoryCheckboxes() {
        // Gestisci il click sulle checkbox
        $('.accessory-checkbox-input').on('change', function() {
            updateTotalPrice();
            
            // Aggiorna lo stato visuale del prodotto
            var $product = $(this).closest('.accessory-product');
            if ($(this).is(':checked')) {
                $product.addClass('selected');
            } else {
                $product.removeClass('selected');
            }
        });
        
        // Gestisci il click sull'intero div del prodotto accessorio
        $('.accessory-product').on('click', function(e) {
            // Ottieni la checkbox corrispondente
            var $checkbox = $(this).find('.accessory-checkbox-input');
            
            // Cambia lo stato della checkbox
            $checkbox.prop('checked', !$checkbox.prop('checked')).trigger('change');
            
            // Aggiorna lo stato visuale
            if ($checkbox.is(':checked')) {
                $(this).addClass('selected');
            } else {
                $(this).removeClass('selected');
            }
        });
        
        // Previeni la propagazione degli eventi quando si fa click sulla checkbox
        $('.accessory-checkbox-input').on('click', function(e) {
            e.stopPropagation();
        });
        
        // Imposta lo stato visuale iniziale per gli accessori già selezionati
        $('.accessory-checkbox-input:checked').each(function() {
            $(this).closest('.accessory-product').addClass('selected');
        });
        
        // Aggiorna il prezzo totale all'avvio
        updateTotalPrice();
    }

    /**
     * Aggiorna il prezzo totale in base agli accessori selezionati
     */
    function updateTotalPrice() {
        // Implementazione futura per mostrare il prezzo totale
    }

    /**
     * Aggiunge i prodotti al carrello via AJAX
     *
     * @param {int} productId ID del prodotto principale
     * @param {int} quantity Quantità del prodotto principale
     * @param {Array} accessories Array di ID degli accessori selezionati
     */
    function addProductsToCart(productId, quantity, accessories) {
        // Verifica che i parametri siano validi
        if (!productId) {
            alert('Errore: Prodotto non valido');
            return;
        }
        
        // Verifica che wpaj_frontend_params sia definito
        if (typeof wpaj_frontend_params === 'undefined') {
            alert('Errore: Parametri non disponibili');
            return;
        }
        
        // Mostra un indicatore di caricamento
        var $button = $('.single_add_to_cart_button');
        var buttonText = $button.text();
        $button.addClass('loading').text('...');
        
        // Rimuovi eventuali messaggi precedenti
        $('.wpaj-message').remove();
        
        // Invia la richiesta AJAX
        $.ajax({
            url: wpaj_frontend_params.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'wpaj_add_accessories_to_cart',
                product_id: productId,
                quantity: quantity,
                accessories: accessories,
                nonce: wpaj_frontend_params.nonce
            },
            success: function(response) {
                // Ripristina il pulsante
                $button.removeClass('loading').text(buttonText);
                
                if (response.success) {
                    // Mostra un messaggio di successo
                    $('<div class="wpaj-message wpaj-success-message">')
                        .text(response.message)
                        .insertBefore('.product-accessories');
                    
                    // Verifica se è necessario reindirizzare al carrello
                    if (wpaj_frontend_params.redirect_to_cart && response.redirect) {
                        // Reindirizza al carrello dopo un breve ritardo
                        setTimeout(function() {
                            window.location.href = response.redirect;
                        }, 1000);
                    } else {
                        // Soluzione semplice: ricarica la pagina
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    }
                } else {
                    // Mostra un messaggio di errore
                    $('<div class="wpaj-message wpaj-error-message">')
                        .text(response.message)
                        .insertBefore('.product-accessories');
                }
            },
            error: function(xhr, status, error) {
                // Ripristina il pulsante
                $button.removeClass('loading').text(buttonText);
                
                // Mostra un messaggio di errore
                $('<div class="wpaj-message wpaj-error-message">')
                    .text(wpaj_frontend_params.i18n.error)
                    .insertBefore('.product-accessories');
            }
        });
    }

})(jQuery); 