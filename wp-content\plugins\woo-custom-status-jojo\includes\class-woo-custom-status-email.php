<?php
if (!defined('ABSPATH')) {
    exit;
}

class Woo_Custom_Status_Email {
    private $db;

    public function __construct() {
        $this->db = new Woo_Custom_Status_DB();
        add_action('woocommerce_order_status_changed', [$this, 'send_status_email'], 10, 4);
    }

    public function send_status_email($order_id, $old_status, $new_status, $order) {
        // Rimuovi il prefisso wc- dallo status se presente
        $new_status = str_replace('wc-', '', $new_status);
        
        // Ottieni la configurazione email per questo status
        $status = $this->db->get_status_by_slug($new_status);
        if (!$status) {
            return;
        }

        $email_config = $this->db->get_status_email($status->id);
        if (!$email_config || !$email_config->is_enabled) {
            return;
        }

        // Prepara i dati dell'email
        $to = $email_config->email_to ? $email_config->email_to : $order->get_billing_email();
        $subject = $this->replace_variables($email_config->email_subject, $order);
        $message = $this->replace_variables($email_config->email_content, $order);

        // Imposta intestazioni per email HTML
        $headers = array();
        $headers[] = 'Content-Type: text/html; charset=UTF-8';
        
        // Invia email
        wp_mail($to, $subject, $message, $headers);
    }

    private function replace_variables($content, $order) {
        $variables = array(
            '{order_number}' => $order->get_order_number(),
            '{customer_name}' => $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
            '{order_date}' => $order->get_date_created()->date_i18n(get_option('date_format')),
            '{order_total}' => $order->get_formatted_order_total(),
            '{billing_address}' => $order->get_formatted_billing_address(),
            '{shipping_address}' => $order->get_formatted_shipping_address(),
            '{site_title}' => get_bloginfo('name'),
            '{site_url}' => get_site_url()
        );

        return str_replace(array_keys($variables), array_values($variables), $content);
    }
} 