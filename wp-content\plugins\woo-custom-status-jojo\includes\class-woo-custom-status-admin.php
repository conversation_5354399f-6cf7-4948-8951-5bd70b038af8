<?php
if (!defined('ABSPATH')) {
    exit;
}

class Woo_Custom_Status_Admin {
    private $db;

    public function __construct() {
        $this->db = new Woo_Custom_Status_DB();
        
        // Aggiungi menu
        add_action('admin_menu', [$this, 'add_admin_menu']);
        
        // Registra assets
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
        
        // Gestisci le azioni AJAX
        add_action('wp_ajax_wcst_generate_slug', [$this, 'ajax_generate_slug']);
    }

    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('Status Ordini Custom', 'woo-custom-status-jojo'),
            __('Status Ordini', 'woo-custom-status-jojo'),
            'manage_woocommerce',
            'woo-custom-status',
            [$this, 'render_admin_page']
        );
    }

    public function enqueue_admin_assets($hook) {
        if ('woocommerce_page_woo-custom-status' !== $hook) {
            return;
        }

        // CSS
        wp_enqueue_style(
            'wcst-admin-style',
            WCST_PLUGIN_URL . 'assets/css/admin.css',
            [],
            WCST_VERSION
        );

        // Select2
        wp_enqueue_style(
            'select2',
            WC()->plugin_url() . '/assets/css/select2.css',
            [],
            '4.0.3'
        );
        
        // JavaScript
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');
        
        wp_enqueue_script('select2');
        
        wp_enqueue_script(
            'wcst-admin-script',
            WCST_PLUGIN_URL . 'assets/js/admin.js',
            ['jquery', 'wp-color-picker', 'select2'],
            WCST_VERSION,
            true
        );

        wp_localize_script('wcst-admin-script', 'wcst_ajax', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wcst_nonce')
        ]);
    }

    public function render_admin_page() {
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Non hai i permessi per accedere a questa pagina.', 'woo-custom-status-jojo'));
        }

        $tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'status_list';
        ?>
        <div class="wrap">
            <h1><?php echo esc_html__('Gestione Status Ordini Custom', 'woo-custom-status-jojo'); ?></h1>
            
            <nav class="nav-tab-wrapper">
                <a href="?page=woo-custom-status&tab=status_list" 
                   class="nav-tab <?php echo $tab === 'status_list' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('Lista Status', 'woo-custom-status-jojo'); ?>
                </a>
                <a href="?page=woo-custom-status&tab=email_settings" 
                   class="nav-tab <?php echo $tab === 'email_settings' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('Email Status', 'woo-custom-status-jojo'); ?>
                </a>
            </nav>

            <div class="tab-content">
                <?php
                switch ($tab) {
                    case 'status_list':
                        $this->render_status_list_tab();
                        break;
                    case 'email_settings':
                        $this->render_email_settings_tab();
                        break;
                }
                ?>
            </div>
        </div>
        <?php
    }

    private function render_status_list_tab() {
        if (isset($_POST['submit_status']) && check_admin_referer('wcst_status_action')) {
            $this->handle_status_form_submission();
        }

        if (isset($_GET['action'])) {
            switch ($_GET['action']) {
                case 'edit':
                    $this->render_status_form('edit');
                    break;
                case 'new':
                    $this->render_status_form('new');
                    break;
                case 'delete':
                    $this->handle_status_deletion();
                    break;
                default:
                    $this->render_status_table();
            }
        } else {
            $this->render_status_table();
        }
    }

    private function render_status_table() {
        $statuses = $this->db->get_all_statuses();
        include WCST_PLUGIN_DIR . 'templates/admin-status-list.php';
    }

    private function render_status_form($mode = 'new') {
        $status = null;
        if ($mode === 'edit' && isset($_GET['id'])) {
            $status = $this->db->get_status(intval($_GET['id']));
            if (!$status) {
                wp_die(__('Status non trovato.', 'woo-custom-status-jojo'));
            }
        }
        include WCST_PLUGIN_DIR . 'templates/admin-status-form.php';
    }

    private function render_email_settings_tab() {
        $status_id = isset($_GET['status_id']) ? intval($_GET['status_id']) : 0;
        
        if ($status_id) {
            if (isset($_POST['submit_email']) && check_admin_referer('wcst_email_action')) {
                $this->handle_email_form_submission($status_id);
            }
            
            $status = $this->db->get_status($status_id);
            $email_config = $this->db->get_status_email($status_id);
            include WCST_PLUGIN_DIR . 'templates/admin-email-form.php';
        } else {
            $statuses = $this->db->get_all_statuses();
            include WCST_PLUGIN_DIR . 'templates/admin-email-select.php';
        }
    }

    private function handle_status_form_submission() {
        $data = [
            'status_name' => sanitize_text_field($_POST['status_name']),
            'status_slug' => sanitize_title($_POST['status_slug']),
            'background_color' => sanitize_hex_color($_POST['background_color']),
            'text_color' => sanitize_hex_color($_POST['text_color']),
            'show_action_button' => isset($_POST['show_action_button']) ? 1 : 0,
            'action_icon' => sanitize_text_field($_POST['action_icon']),
            'allowed_statuses' => isset($_POST['allowed_statuses']) && is_array($_POST['allowed_statuses']) ? 
                array_map('sanitize_text_field', $_POST['allowed_statuses']) : []
        ];

        if (isset($_POST['status_id'])) {
            $result = $this->db->update_status(intval($_POST['status_id']), $data);
            $message = __('Status aggiornato con successo.', 'woo-custom-status-jojo');
        } else {
            $result = $this->db->add_status($data);
            $message = __('Nuovo status creato con successo.', 'woo-custom-status-jojo');
        }

        if ($result) {
            add_settings_error('wcst_messages', 'wcst_message', $message, 'updated');
        } else {
            add_settings_error('wcst_messages', 'wcst_message', 
                __('Si è verificato un errore durante il salvataggio.', 'woo-custom-status-jojo'), 
                'error');
        }
    }

    private function handle_status_deletion() {
        if (!isset($_GET['id']) || !check_admin_referer('delete_status')) {
            return;
        }

        $status_id = intval($_GET['id']);
        if ($this->db->delete_status($status_id)) {
            add_settings_error('wcst_messages', 'wcst_message',
                __('Status eliminato con successo.', 'woo-custom-status-jojo'),
                'updated');
        } else {
            add_settings_error('wcst_messages', 'wcst_message',
                __('Si è verificato un errore durante l\'eliminazione.', 'woo-custom-status-jojo'),
                'error');
        }
    }

    private function handle_email_form_submission($status_id) {
        $data = [
            'status_id' => $status_id,
            'is_enabled' => isset($_POST['is_enabled']) ? 1 : 0,
            'email_to' => sanitize_email($_POST['email_to']),
            'email_subject' => sanitize_text_field($_POST['email_subject']),
            'email_content' => wp_kses_post($_POST['email_content'])
        ];

        if ($this->db->save_status_email($data)) {
            add_settings_error('wcst_messages', 'wcst_message',
                __('Configurazione email salvata con successo.', 'woo-custom-status-jojo'),
                'updated');
        } else {
            add_settings_error('wcst_messages', 'wcst_message',
                __('Si è verificato un errore durante il salvataggio.', 'woo-custom-status-jojo'),
                'error');
        }
    }

    public function ajax_generate_slug() {
        check_ajax_referer('wcst_nonce', 'nonce');
        
        if (!isset($_POST['status_name'])) {
            wp_send_json_error();
        }

        $slug = sanitize_title($_POST['status_name']);
        wp_send_json_success(['slug' => $slug]);
    }
} 