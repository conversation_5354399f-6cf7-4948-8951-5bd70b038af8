<?php
/**
 * Classe di debug per il plugin Pagamento a Rate
 */
class WPR_Debug {
    
    /**
     * Abilita o disabilita il debug
     * 
     * @var bool
     */
    private static $debug_enabled = false;
    
    /**
     * Inizializza il debug
     */
    public static function init() {
        // Abilita il debug se è impostato in WordPress
        self::$debug_enabled = (defined('WP_DEBUG') && WP_DEBUG);
        
        // Aggiungi log personalizzati se il debug è abilitato
        if (self::$debug_enabled) {
            add_action('woocommerce_payment_complete', array(__CLASS__, 'log_payment_complete'), 10, 1);
            add_action('woocommerce_order_status_changed', array(__CLASS__, 'log_order_status_change'), 10, 4);
            add_filter('woocommerce_payment_gateways', array(__CLASS__, 'log_payment_gateways'), 999);
        }
    }
    
    /**
     * Registra i messaggi di log
     * 
     * @param string $message Messaggio da registrare
     * @param string $level Livello di log (debug, info, warning, error)
     */
    public static function log($message, $level = 'info') {
        if (!self::$debug_enabled) {
            return;
        }
        
        if (function_exists('wc_get_logger')) {
            $logger = wc_get_logger();
            $context = array('source' => 'woo-pagamento-rate');
            
            switch ($level) {
                case 'debug':
                    $logger->debug($message, $context);
                    break;
                case 'info':
                    $logger->info($message, $context);
                    break;
                case 'warning':
                    $logger->warning($message, $context);
                    break;
                case 'error':
                    $logger->error($message, $context);
                    break;
                default:
                    $logger->info($message, $context);
            }
        }
    }
    
    /**
     * Registra quando un pagamento è completato
     * 
     * @param int $order_id ID dell'ordine
     */
    public static function log_payment_complete($order_id) {
        $order = wc_get_order($order_id);
        if ($order && $order->get_payment_method() === 'pagamento_rate') {
            self::log('Pagamento completato per l\'ordine #' . $order_id . ' con metodo Pagamento a Rate');
        }
    }
    
    /**
     * Registra quando lo stato di un ordine cambia
     * 
     * @param int $order_id ID dell'ordine
     * @param string $old_status Vecchio stato
     * @param string $new_status Nuovo stato
     * @param WC_Order $order Oggetto ordine
     */
    public static function log_order_status_change($order_id, $old_status, $new_status, $order) {
        if ($order && $order->get_payment_method() === 'pagamento_rate') {
            self::log('Stato dell\'ordine #' . $order_id . ' cambiato da ' . $old_status . ' a ' . $new_status);
        }
    }
    
    /**
     * Registra i gateway di pagamento disponibili
     * 
     * @param array $gateways Gateway di pagamento
     * @return array Gateway di pagamento
     */
    public static function log_payment_gateways($gateways) {
        $gateway_list = array();
        foreach ($gateways as $gateway) {
            if (is_string($gateway)) {
                $gateway_list[] = $gateway;
            } else if (is_object($gateway) && isset($gateway->id)) {
                $gateway_list[] = $gateway->id;
            }
        }
        
        self::log('Gateway di pagamento disponibili: ' . implode(', ', $gateway_list));
        
        return $gateways;
    }
} 