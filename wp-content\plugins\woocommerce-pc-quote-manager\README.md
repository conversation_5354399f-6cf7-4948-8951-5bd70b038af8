# WooCommerce PC Quote Manager

Un plugin WordPress per la gestione di preventivi PC personalizzati con form clienti e interfaccia di gestione admin.

## Caratteristiche

### Form Cliente
- **Shortcode**: `[pc_quote_form]` per inserire il form in qualsiasi pagina/post
- **Campi richiesti**:
  - Nome
  - Email (con validazione)
  - Numero Telefono
  - Tipologia PC (Gaming, Office, Entrambi)
  - Budget (8 fasce di prezzo da 500€ a 3000€+)
  - Preferenza Processore (Intel, AMD, Nessuna Preferenza)
  - Preferenza Scheda Video (Nvidia, AMD, Nessuna Preferenza)
  - Accessori necessari (Monitor, Tastiera, Mouse, C<PERSON>ie, <PERSON><PERSON><PERSON>, <PERSON>)
  - <PERSON>re <PERSON> (campo opzionale)

### Gestione Admin
- **Menu**: Accessibile da WooCommerce > Preventivi
- **Lista preventivi** con:
  - ID preventivo
  - Dati cliente (nome, email)
  - Specifiche richieste
  - Status (In attesa di risposta / Inviato / Risposta cliente / Chiuso)
  - Data creazione
  - Azioni (Visualizza/Rispondi)
- **Pagina dettaglio** con:
  - Tutti i dati del cliente e specifiche
  - Cronologia completa della conversazione
  - Form per risposta admin
  - Pulsante "Chiudi Preventivo"
  - Cambio automatico status dopo risposta

### Sistema Conversazione
- **Link tokenizzato sicuro**: Ogni risposta admin include un link personalizzato per il cliente
- **Pagina risposta cliente**: Interfaccia dedicata accessibile tramite token
- **Cronologia conversazione**: Visualizzazione completa di tutti i messaggi scambiati
- **Rate limiting**: Protezione anti-spam (1 messaggio al minuto per cliente)
- **Scadenza token**: Link validi per 30 giorni
- **Chiusura preventivi**: Possibilità di chiudere definitivamente un preventivo

### Email Notifications
- **Al cliente**: Conferma ricezione preventivo
- **All'admin**: Notifica nuovo preventivo con tutti i dettagli
- **Al cliente**: Email con risposta dell'admin + link tokenizzato per rispondere
- **All'admin**: Notifica quando il cliente risponde
- Template HTML responsive con cronologia conversazione

### Sicurezza
- Nonce verification per tutti i form
- Sanitizzazione e validazione di tutti i dati
- Controllo permessi admin
- Protezione CSRF

## Installazione

1. Copia la cartella `woocommerce-pc-quote-manager` in `/wp-content/plugins/`
2. Attiva il plugin dal pannello admin WordPress
3. Assicurati che WooCommerce sia installato e attivo

## Utilizzo

### Per inserire il form
Aggiungi lo shortcode `[pc_quote_form]` in qualsiasi pagina o post:

```
[pc_quote_form]
```

Oppure con titolo personalizzato:
```
[pc_quote_form title="Richiedi il tuo Preventivo PC Gaming"]
```

### Per gestire i preventivi
1. Vai su WooCommerce > Preventivi
2. Visualizza la lista di tutti i preventivi ricevuti
3. Clicca su "Visualizza/Rispondi" per vedere i dettagli
4. Visualizza la cronologia completa della conversazione
5. Scrivi la risposta nel campo apposito
6. Clicca "Invia Risposta" per inviare email al cliente con link tokenizzato
7. Usa "Chiudi Preventivo" per terminare definitivamente la conversazione

### Per i clienti (risposta ai preventivi)
1. Il cliente riceve email con link personalizzato
2. Clicca sul link per accedere alla pagina di risposta
3. Visualizza dettagli preventivo e cronologia conversazione
4. Scrive la sua risposta nel form
5. Invia la risposta (l'admin riceve notifica email)
6. Può continuare la conversazione fino alla chiusura del preventivo

## Database

Il plugin crea automaticamente due tabelle nel database:

### Tabella `wp_wc_pc_quotes`:
- `id`: ID univoco preventivo
- `customer_name`: Nome cliente
- `customer_email`: Email cliente
- `customer_phone`: Telefono cliente
- `pc_type`: Tipologia PC richiesta
- `budget`: Fascia di budget
- `processor_preference`: Preferenza processore
- `graphics_preference`: Preferenza scheda video
- `additional_needs`: Accessori richiesti
- `other_requests`: Richieste aggiuntive
- `status`: Status preventivo (In attesa di risposta / Inviato / Risposta cliente / Chiuso)
- `admin_response`: Ultima risposta dell'admin
- `response_token`: Token sicuro per risposta cliente
- `token_expires_at`: Scadenza del token
- `created_at`: Data creazione
- `updated_at`: Data ultima modifica

### Tabella `wp_wc_pc_quote_conversations`:
- `conversation_id`: ID univoco messaggio
- `quote_id`: Riferimento al preventivo
- `sender_type`: Tipo mittente (admin / customer)
- `message`: Contenuto del messaggio
- `created_at`: Data invio messaggio

## Personalizzazione

### CSS
Il plugin include un file CSS (`assets/style.css`) con stili responsive. Puoi personalizzare l'aspetto modificando questo file o aggiungendo CSS personalizzato nel tuo tema.

### Email Templates
I template email sono definiti nella classe `WC_PC_Quote_Emails` e possono essere personalizzati modificando i metodi:
- `get_new_quote_email_template()`: Email notifica admin
- `get_admin_response_email_template()`: Email risposta cliente

## Hooks e Filtri

### Actions
- `wc_pc_quote_new_quote`: Triggered quando viene creato un nuovo preventivo
- `wc_pc_quote_admin_response`: Triggered quando l'admin invia una risposta
- `wc_pc_quote_customer_response`: Triggered quando il cliente risponde (parametri: quote_id, message)

### Utilizzo
```php
// Esempio: azione personalizzata per nuovo preventivo
add_action('wc_pc_quote_new_quote', function($quote_id) {
    // Il tuo codice personalizzato
});
```

## Sicurezza

### Token Management
- Token generati con `wp_generate_password()` (32 caratteri)
- Scadenza automatica dopo 30 giorni
- Validazione token ad ogni accesso
- Token invalidati quando preventivo viene chiuso

### Rate Limiting
- Limite di 1 messaggio al minuto per cliente
- Prevenzione spam automatica
- Controllo basato su timestamp database

### Validazione Input
- Sanitizzazione completa di tutti i dati
- Nonce verification su tutti i form
- Controllo permessi admin
- Escape output per prevenire XSS

## URL e Endpoint

### Pagina Risposta Cliente
- **URL**: `/quote-response/?token=XXXXX`
- **Metodo**: GET per visualizzazione, POST per invio risposta
- **Parametri**: `token` (obbligatorio)
- **Sicurezza**: Token validation, rate limiting, status check

## Requisiti

- WordPress 5.8+
- PHP 7.4+
- WooCommerce 5.0+

## Supporto

Per supporto e segnalazione bug, contatta Giovanni JoJo Castaldo.

## Changelog

### 2.0.0
- **Sistema Conversazione Completo**: Aggiunto sistema di conversazione bidirezionale tra admin e clienti
- **Link Tokenizzati**: Generazione automatica di link sicuri per risposta clienti
- **Nuova Tabella Database**: `wp_wc_pc_quote_conversations` per cronologia messaggi
- **Nuovi Status**: Aggiunto "Risposta cliente" e "Chiuso" agli status preventivi
- **Pagina Risposta Cliente**: Interfaccia dedicata accessibile tramite token
- **Chiusura Preventivi**: Funzionalità per chiudere definitivamente i preventivi
- **Rate Limiting**: Protezione anti-spam per risposte clienti
- **Email Migliorate**: Template aggiornati con cronologia conversazione
- **Sicurezza Avanzata**: Token management e validazione completa
- **URL Rewriting**: Endpoint personalizzato `/quote-response/`

### 1.0.0
- Release iniziale
- Form preventivi con validazione completa
- Gestione admin con interfaccia intuitiva
- Sistema email notifications
- Design responsive
- Sicurezza implementata

## Licenza

Questo plugin è rilasciato sotto licenza GPL v2 o successiva.
