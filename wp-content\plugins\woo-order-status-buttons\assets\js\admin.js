/**
 * WooCommerce Order Status Buttons - Admin JavaScript
 */

(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        WOSB_Admin.init();
    });
    
    /**
     * Main Admin Object
     */
    var WOSB_Admin = {
        
        /**
         * Initialize admin functionality
         */
        init: function() {
            this.initIconPreview();
            this.initFormValidation();
            this.initEmailPreview();
            this.initPlaceholderInsertion();
            this.initConfirmDialogs();
            this.initTooltips();
        },
        
        /**
         * Initialize icon preview functionality
         */
        initIconPreview: function() {
            var $iconInput = $('#dashicon_code');
            var $iconPreview = $('#icon-preview');
            
            if ($iconInput.length && $iconPreview.length) {
                // Update preview on page load
                this.updateIconPreview($iconInput.val(), $iconPreview);
                
                // Update preview on input change
                $iconInput.on('input keyup', function() {
                    WOSB_Admin.updateIconPreview($(this).val(), $iconPreview);
                });
            }
        },
        
        /**
         * Update icon preview
         */
        updateIconPreview: function(code, $preview) {
            if (!code) {
                $preview.hide();
                return;
            }
            
            // Clean and format the code
            var cleanCode = code.replace(/\\f/g, '').replace(/[^0-9a-fA-F]/g, '');
            
            if (cleanCode.length >= 3) {
                // Set the CSS content property
                $preview.css({
                    'font-family': 'dashicons',
                    'display': 'inline-block'
                });
                
                // Use a style element to set the content
                var styleId = 'wosb-icon-preview-style';
                $('#' + styleId).remove();
                
                $('<style id="' + styleId + '">')
                    .text('#icon-preview::before { content: "\\f' + cleanCode + '"; }')
                    .appendTo('head');
                
                $preview.show();
            } else {
                $preview.hide();
            }
        },
        
        /**
         * Initialize form validation
         */
        initFormValidation: function() {
            $('.wosb-button-form').on('submit', function(e) {
                var isValid = WOSB_Admin.validateForm($(this));
                
                if (!isValid) {
                    e.preventDefault();
                    return false;
                }
                
                // Add loading state
                $(this).addClass('wosb-loading');
                $(this).find('input[type="submit"]').prop('disabled', true);
            });
        },
        
        /**
         * Validate form data
         */
        validateForm: function($form) {
            var errors = [];
            
            // Check button name
            var buttonName = $form.find('#button_name').val().trim();
            if (!buttonName) {
                errors.push('Il nome del pulsante è obbligatorio.');
            }
            
            // Check order statuses
            var orderStatuses = $form.find('input[name="order_statuses[]"]:checked').length;
            if (orderStatuses === 0) {
                errors.push('Seleziona almeno uno status degli ordini.');
            }
            
            // Check target status
            var targetStatus = $form.find('#target_status').val();
            if (!targetStatus) {
                errors.push('Seleziona uno status di destinazione.');
            }
            
            // Check email content
            var emailContent = '';
            if (typeof tinymce !== 'undefined' && tinymce.get('email_content')) {
                emailContent = tinymce.get('email_content').getContent();
            } else {
                emailContent = $form.find('#email_content').val();
            }
            
            if (!emailContent.trim()) {
                errors.push('Il contenuto dell\'email è obbligatorio.');
            }
            
            // Show errors if any
            if (errors.length > 0) {
                alert('Errori di validazione:\n\n' + errors.join('\n'));
                return false;
            }
            
            return true;
        },
        
        /**
         * Initialize email preview functionality
         */
        initEmailPreview: function() {
            $('.wosb-preview-email').on('click', function(e) {
                e.preventDefault();
                
                var buttonId = $(this).data('button-id');
                
                // For now, show a simple alert
                // In the future, this could open a modal with email preview
                alert('Funzionalità anteprima email in sviluppo per il pulsante ID: ' + buttonId);
                
                // TODO: Implement actual email preview
                // WOSB_Admin.showEmailPreview(buttonId);
            });
        },
        
        /**
         * Initialize placeholder insertion
         */
        initPlaceholderInsertion: function() {
            // Add clickable placeholders to description
            var $description = $('.form-table .description');
            
            $description.each(function() {
                var $desc = $(this);
                var html = $desc.html();
                
                // Make placeholder codes clickable
                html = html.replace(/<code>(\{[^}]+\})<\/code>/g, function(match, placeholder) {
                    return '<code class="wosb-clickable-placeholder" data-placeholder="' + placeholder + '">' + placeholder + '</code>';
                });
                
                $desc.html(html);
            });
            
            // Handle placeholder clicks
            $(document).on('click', '.wosb-clickable-placeholder', function(e) {
                e.preventDefault();
                
                var placeholder = $(this).data('placeholder');
                WOSB_Admin.insertPlaceholder(placeholder);
                
                // Visual feedback
                $(this).addClass('wosb-inserted');
                setTimeout(function() {
                    $('.wosb-clickable-placeholder').removeClass('wosb-inserted');
                }, 1000);
            });
        },
        
        /**
         * Insert placeholder into email content
         */
        insertPlaceholder: function(placeholder) {
            var editor = null;
            
            // Try TinyMCE first
            if (typeof tinymce !== 'undefined' && tinymce.get('email_content')) {
                editor = tinymce.get('email_content');
                if (editor && !editor.isHidden()) {
                    editor.insertContent(placeholder);
                    return;
                }
            }
            
            // Fallback to textarea
            var $textarea = $('#email_content');
            if ($textarea.length) {
                var cursorPos = $textarea.prop('selectionStart');
                var textBefore = $textarea.val().substring(0, cursorPos);
                var textAfter = $textarea.val().substring(cursorPos);
                
                $textarea.val(textBefore + placeholder + textAfter);
                
                // Set cursor position after inserted placeholder
                var newPos = cursorPos + placeholder.length;
                $textarea.prop('selectionStart', newPos);
                $textarea.prop('selectionEnd', newPos);
                $textarea.focus();
            }
        },
        
        /**
         * Initialize confirmation dialogs
         */
        initConfirmDialogs: function() {
            // Delete button confirmation
            $('.submitdelete').on('click', function(e) {
                var confirmMessage = 'Sei sicuro di voler eliminare questo pulsante? Questa azione non può essere annullata.';
                
                if (!confirm(confirmMessage)) {
                    e.preventDefault();
                    return false;
                }
            });
            
            // Order action button confirmation (in orders list)
            $(document).on('click', '.wosb-action-button', function(e) {
                var confirmMessage = $(this).data('confirm');
                
                if (confirmMessage && !confirm(confirmMessage)) {
                    e.preventDefault();
                    return false;
                }
                
                // Add loading state
                $(this).addClass('wosb-loading');
            });
        },
        
        /**
         * Initialize tooltips
         */
        initTooltips: function() {
            // Add tooltips to action buttons
            $('.wosb-action-button').each(function() {
                var $button = $(this);
                var title = $button.attr('title');
                
                if (title) {
                    $button.tooltip({
                        position: { my: "center bottom-20", at: "center top" },
                        show: { delay: 500 },
                        hide: { delay: 100 }
                    });
                }
            });
        },
        
        /**
         * Show email preview modal (future implementation)
         */
        showEmailPreview: function(buttonId) {
            // TODO: Implement modal with email preview
            console.log('Email preview for button ID:', buttonId);
        },
        
        /**
         * Utility function to show admin notices
         */
        showNotice: function(message, type) {
            type = type || 'info';
            
            var $notice = $('<div class="notice notice-' + type + ' is-dismissible">')
                .append('<p>' + message + '</p>')
                .append('<button type="button" class="notice-dismiss"><span class="screen-reader-text">Dismiss this notice.</span></button>');
            
            $('.wrap h1').after($notice);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut();
            }, 5000);
        }
    };
    
    // Make WOSB_Admin globally available
    window.WOSB_Admin = WOSB_Admin;
    
})(jQuery);

/**
 * Additional CSS for clickable placeholders
 */
jQuery(document).ready(function($) {
    $('<style>')
        .text(`
            .wosb-clickable-placeholder {
                cursor: pointer;
                transition: all 0.2s ease;
                border: 1px solid transparent;
                padding: 2px 4px;
                border-radius: 2px;
            }
            
            .wosb-clickable-placeholder:hover {
                background: #0073aa;
                color: white;
                border-color: #005a87;
            }
            
            .wosb-clickable-placeholder.wosb-inserted {
                background: #46b450;
                color: white;
                border-color: #3e9f46;
            }
            
            .wosb-loading {
                position: relative;
                opacity: 0.6;
                pointer-events: none;
            }
        `)
        .appendTo('head');
});
