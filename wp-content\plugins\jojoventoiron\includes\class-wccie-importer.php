<?php

/**

 * Classe per l'importazione dei coupon

 *

 * @package WooCommerce Coupon Importer Exporter

 */



// Impedisci l'accesso diretto

if (!defined('ABSPATH')) {

    exit;

}



/**

 * Classe per l'importazione dei coupon

 */

class WCCIE_Importer {



    /**

     * Statistiche di importazione

     *

     * @var array

     */

    private $stats = array(

        'imported' => 0,

        'updated' => 0,

        'skipped' => 0,

        'errors' => array(),

    );



    /**

     * Importa i coupon da un file CSV

     *

     * @param string $file_path Percorso del file CSV

     * @param bool $update_existing Se aggiornare i coupon esistenti

     * @param bool $import_usage_logs Se importare i log di utilizzo

     * @return array|WP_Error Statistiche di importazione o errore

     */

    public function import_coupons($file_path, $update_existing = true, $import_usage_logs = true) {

        // Verifica che il file esista

        if (!file_exists($file_path)) {

            return new WP_Error('file_not_found', __('File non trovato.', 'wc-coupon-importer-exporter'));

        }

        

        // Apri il file

        $file = fopen($file_path, 'r');

        

        if (!$file) {

            return new WP_Error('file_error', __('Impossibile aprire il file.', 'wc-coupon-importer-exporter'));

        }

        

        // Leggi le intestazioni

        $headers = fgetcsv($file);

        

        if (!$headers) {

            fclose($file);

            return new WP_Error('invalid_csv', __('Il file CSV non è valido o è vuoto.', 'wc-coupon-importer-exporter'));

        }

        

        // Verifica che le intestazioni contengano i campi necessari

        $required_fields = array('code', 'discount_type', 'amount');

        foreach ($required_fields as $field) {

            if (!in_array($field, $headers)) {

                fclose($file);

                return new WP_Error(

                    'missing_fields',

                    sprintf(__('Campo obbligatorio mancante: %s', 'wc-coupon-importer-exporter'), $field)

                );

            }

        }

        

        // Mappa le intestazioni agli indici

        $header_map = array_flip($headers);

        

        // Verifica se includere i log di utilizzo

        $has_usage_logs = in_array('usage_logs', $headers) && $import_usage_logs;

        

        // Inizializza le statistiche

        $this->stats = array(

            'imported' => 0,

            'updated' => 0,

            'skipped' => 0,

            'errors' => array(),

        );

        

        // Processa ogni riga

        while (($row = fgetcsv($file)) !== false) {

            // Salta righe vuote

            if (empty($row)) {

                continue;

            }

            

            // Crea un array associativo con i dati del coupon

            $coupon_data = array();

            foreach ($headers as $index => $header) {

                if (isset($row[$index])) {

                    $coupon_data[$header] = $row[$index];

                } else {

                    $coupon_data[$header] = '';

                }

            }

            

            // Processa il coupon

            $this->process_coupon($coupon_data, $update_existing, $has_usage_logs);

        }

        

        // Chiudi il file

        fclose($file);

        

        return $this->stats;

    }

    

    /**

     * Processa un singolo coupon

     *

     * @param array $coupon_data Dati del coupon

     * @param bool $update_existing Se aggiornare i coupon esistenti

     * @param bool $has_usage_logs Se importare i log di utilizzo

     */

    private function process_coupon($coupon_data, $update_existing, $has_usage_logs) {

        // Verifica che il codice coupon sia presente

        if (empty($coupon_data['code'])) {

            $this->stats['skipped']++;

            $this->stats['errors'][] = __('Coupon saltato: codice mancante.', 'wc-coupon-importer-exporter');

            return;

        }

        

        // Verifica se il coupon esiste già

        $existing_id = $this->coupon_exists($coupon_data['code']);

        

        // Se il coupon esiste e non dobbiamo aggiornarlo, salta

        if ($existing_id && !$update_existing) {

            $this->stats['skipped']++;

            return;

        }

        

        // Prepara i dati del coupon

        $coupon_args = $this->prepare_coupon_data($coupon_data, $existing_id);

        

        // Crea o aggiorna il coupon

        if ($existing_id) {

            $coupon_id = $this->update_coupon($existing_id, $coupon_args);

            if ($coupon_id) {

                $this->stats['updated']++;

            } else {

                $this->stats['skipped']++;

                $this->stats['errors'][] = sprintf(

                    __('Errore nell\'aggiornamento del coupon: %s', 'wc-coupon-importer-exporter'),

                    $coupon_data['code']

                );

            }

        } else {

            $coupon_id = $this->create_coupon($coupon_args);

            if ($coupon_id) {

                $this->stats['imported']++;

            } else {

                $this->stats['skipped']++;

                $this->stats['errors'][] = sprintf(

                    __('Errore nella creazione del coupon: %s', 'wc-coupon-importer-exporter'),

                    $coupon_data['code']

                );

            }

        }

        

        // Se il coupon è stato creato/aggiornato con successo e ci sono log di utilizzo, importali

        if ($coupon_id && $has_usage_logs && !empty($coupon_data['usage_logs'])) {

            $this->import_usage_logs($coupon_id, $coupon_data['usage_logs']);

        }

    }

    

    /**

     * Verifica se un coupon esiste già

     *

     * @param string $code Codice del coupon

     * @return int|false ID del coupon se esiste, false altrimenti

     */

    private function coupon_exists($code) {

        global $wpdb;

        

        $coupon_id = $wpdb->get_var($wpdb->prepare(

            "SELECT ID FROM {$wpdb->posts} WHERE post_title = %s AND post_type = 'shop_coupon'",

            $code

        ));

        

        return $coupon_id ? (int) $coupon_id : false;

    }

    

    /**

     * Prepara i dati del coupon per la creazione o l'aggiornamento

     *

     * @param array $coupon_data Dati del coupon dal CSV

     * @param int|false $existing_id ID del coupon esistente, se presente

     * @return array Dati del coupon formattati

     */

    private function prepare_coupon_data($coupon_data, $existing_id) {

        $args = array(

            'post_title' => $coupon_data['code'],

            'post_excerpt' => isset($coupon_data['description']) ? $coupon_data['description'] : '',

            'post_status' => isset($coupon_data['status']) ? $coupon_data['status'] : 'publish',

            'post_type' => 'shop_coupon'

        );

        

        // Se stiamo aggiornando un coupon esistente, imposta l'ID

        if ($existing_id) {

            $args['ID'] = $existing_id;

        }

        

        // Prepara i meta dati del coupon

        $meta_data = array(

            'discount_type' => isset($coupon_data['discount_type']) ? $coupon_data['discount_type'] : 'fixed_cart',

            'coupon_amount' => isset($coupon_data['amount']) ? $coupon_data['amount'] : 0,

            'individual_use' => isset($coupon_data['individual_use']) && $coupon_data['individual_use'] === 'yes' ? 'yes' : 'no',

            'product_ids' => isset($coupon_data['product_ids']) ? $this->parse_comma_list($coupon_data['product_ids']) : array(),

            'excluded_product_ids' => isset($coupon_data['excluded_product_ids']) ? $this->parse_comma_list($coupon_data['excluded_product_ids']) : array(),

            'usage_limit' => isset($coupon_data['usage_limit']) ? $coupon_data['usage_limit'] : '',

            'usage_limit_per_user' => isset($coupon_data['usage_limit_per_user']) ? $coupon_data['usage_limit_per_user'] : '',

            'limit_usage_to_x_items' => isset($coupon_data['limit_usage_to_x_items']) ? $coupon_data['limit_usage_to_x_items'] : '',

            'usage_count' => isset($coupon_data['usage_count']) ? $coupon_data['usage_count'] : 0,

            'expiry_date' => isset($coupon_data['expiry_date']) && !empty($coupon_data['expiry_date']) ? $coupon_data['expiry_date'] : '',

            'free_shipping' => isset($coupon_data['free_shipping']) && $coupon_data['free_shipping'] === 'yes' ? 'yes' : 'no',

            'product_categories' => isset($coupon_data['product_categories']) ? $this->parse_comma_list($coupon_data['product_categories']) : array(),

            'excluded_product_categories' => isset($coupon_data['excluded_product_categories']) ? $this->parse_comma_list($coupon_data['excluded_product_categories']) : array(),

            'exclude_sale_items' => isset($coupon_data['exclude_sale_items']) && $coupon_data['exclude_sale_items'] === 'yes' ? 'yes' : 'no',

            'minimum_amount' => isset($coupon_data['minimum_amount']) ? $coupon_data['minimum_amount'] : '',

            'maximum_amount' => isset($coupon_data['maximum_amount']) ? $coupon_data['maximum_amount'] : '',

            'customer_email' => isset($coupon_data['customer_emails']) ? $this->parse_comma_list($coupon_data['customer_emails']) : array(),

        );

        

        // Aggiungi meta dati aggiuntivi

        if (isset($coupon_data['exclude_shipping_methods']) && !empty($coupon_data['exclude_shipping_methods'])) {

            $meta_data['exclude_shipping_methods'] = $this->parse_comma_list($coupon_data['exclude_shipping_methods']);

        }

        

        if (isset($coupon_data['product_attributes']) && !empty($coupon_data['product_attributes'])) {

            $meta_data['product_attributes'] = $this->parse_meta_array($coupon_data['product_attributes']);

        }

        

        if (isset($coupon_data['excluded_product_attributes']) && !empty($coupon_data['excluded_product_attributes'])) {

            $meta_data['excluded_product_attributes'] = $this->parse_meta_array($coupon_data['excluded_product_attributes']);

        }

        

        $args['meta_input'] = $meta_data;

        

        return $args;

    }

    

    /**

     * Crea un nuovo coupon

     *

     * @param array $coupon_args Dati del coupon

     * @return int|false ID del coupon creato o false in caso di errore

     */

    private function create_coupon($coupon_args) {

        // Crea il post del coupon

        $coupon_id = wp_insert_post($coupon_args, true);

        

        if (is_wp_error($coupon_id)) {

            return false;

        }

        

        return $coupon_id;

    }

    

    /**

     * Aggiorna un coupon esistente

     *

     * @param int $coupon_id ID del coupon

     * @param array $coupon_args Dati del coupon

     * @return int|false ID del coupon aggiornato o false in caso di errore

     */

    private function update_coupon($coupon_id, $coupon_args) {

        // Aggiorna il post del coupon

        $result = wp_update_post($coupon_args, true);

        

        if (is_wp_error($result)) {

            return false;

        }

        

        return $coupon_id;

    }

    

    /**

     * Importa i log di utilizzo per un coupon

     *

     * @param int $coupon_id ID del coupon

     * @param string $usage_logs Log di utilizzo formattati

     */

    private function import_usage_logs($coupon_id, $usage_logs) {

        // Ottieni il codice del coupon

        $coupon_code = get_the_title($coupon_id);

        

        if (empty($coupon_code)) {

            return;

        }

        

        // Analizza i log di utilizzo

        $logs = explode(';', $usage_logs);

        

        if (empty($logs)) {

            return;

        }

        

        global $wpdb;

        

        foreach ($logs as $log) {

            $log_data = explode('|', $log);

            

            // Verifica che il log abbia il formato corretto

            if (count($log_data) < 4) {

                continue;

            }

            

            list($order_id, $user_id, $user_email, $date) = $log_data;

            

            // Verifica se l'ordine esiste già

            $order_exists = $wpdb->get_var($wpdb->prepare(

                "SELECT ID FROM {$wpdb->posts} WHERE ID = %d AND post_type = 'shop_order'",

                $order_id

            ));

            

            // Se l'ordine non esiste, creane uno fittizio per registrare l'utilizzo

            if (!$order_exists) {

                $order_args = array(

                    'ID' => $order_id,

                    'post_type' => 'shop_order',

                    'post_status' => 'wc-completed',

                    'post_title' => sprintf(__('Ordine importato #%s', 'wc-coupon-importer-exporter'), $order_id),

                    'post_date' => $date,

                    'post_date_gmt' => $date,

                    'meta_input' => array(

                        '_billing_email' => $user_email,

                        '_customer_user' => $user_id,

                    )

                );

                

                $new_order_id = wp_insert_post($order_args);

                

                if (is_wp_error($new_order_id)) {

                    continue;

                }

                

                // Aggiungi il coupon all'ordine

                $wpdb->insert(

                    $wpdb->prefix . 'woocommerce_order_items',

                    array(

                        'order_id' => $new_order_id,

                        'order_item_name' => $coupon_code,

                        'order_item_type' => 'coupon',

                    )

                );

            }

        }

        

        // Aggiorna il conteggio di utilizzo del coupon

        update_post_meta($coupon_id, 'usage_count', count($logs));

    }

    

    /**

     * Converte una stringa separata da virgole in un array

     *

     * @param string $comma_list Stringa separata da virgole

     * @return array Array di valori

     */

    private function parse_comma_list($comma_list) {

        if (empty($comma_list)) {

            return array();

        }

        

        $items = explode(',', $comma_list);

        $items = array_map('trim', $items);

        $items = array_filter($items);

        

        return $items;

    }

    

    /**

     * Analizza una stringa di metadati formattata

     *

     * @param string $meta_string Stringa di metadati

     * @return array Array di metadati

     */

    private function parse_meta_array($meta_string) {

        if (empty($meta_string)) {

            return array();

        }

        

        $meta_array = array();

        $items = explode(';', $meta_string);

        

        foreach ($items as $item) {

            $parts = explode(':', $item, 2);

            

            if (count($parts) === 2) {

                $key = trim($parts[0]);

                $value = trim($parts[1]);

                

                // Verifica se il valore contiene separatori di array

                if (strpos($value, '|') !== false) {

                    $value = explode('|', $value);

                    $value = array_map('trim', $value);

                }

                

                $meta_array[$key] = $value;

            }

        }

        

        return $meta_array;

    }

} 