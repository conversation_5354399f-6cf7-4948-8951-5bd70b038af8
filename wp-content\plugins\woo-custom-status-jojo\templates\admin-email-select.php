<?php
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h2><?php esc_html_e('Configurazione Email Status', 'woo-custom-status-jojo'); ?></h2>

    <?php settings_errors('wcst_messages'); ?>

    <?php if (empty($statuses)) : ?>
        <div class="notice notice-warning">
            <p>
                <?php 
                printf(
                    __('Non ci sono status ordine custom disponibili. <a href="%s">Crea il tuo primo status</a>.', 'woo-custom-status-jojo'),
                    '?page=woo-custom-status&action=new'
                );
                ?>
            </p>
        </div>
    <?php else : ?>
        <form method="get" action="">
            <input type="hidden" name="page" value="woo-custom-status">
            <input type="hidden" name="tab" value="email_settings">
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="status_id"><?php esc_html_e('Seleziona Status', 'woo-custom-status-jojo'); ?></label>
                    </th>
                    <td>
                        <select name="status_id" id="status_id" class="regular-text">
                            <option value=""><?php esc_html_e('-- Seleziona --', 'woo-custom-status-jojo'); ?></option>
                            <?php foreach ($statuses as $status) : ?>
                                <option value="<?php echo esc_attr($status->id); ?>">
                                    <?php echo esc_html($status->status_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <input type="submit" 
                       name="submit" 
                       class="button button-primary" 
                       value="<?php esc_attr_e('Configura Email', 'woo-custom-status-jojo'); ?>">
            </p>
        </form>
    <?php endif; ?>
</div> 