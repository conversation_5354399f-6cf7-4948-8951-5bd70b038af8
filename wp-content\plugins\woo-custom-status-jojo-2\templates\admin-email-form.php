<?php
if (!defined('ABSPATH')) {
    exit;
}

if (!$status) {
    wp_die(__('Status non trovato.', 'woo-custom-status-jojo'));
}
?>

<div class="wrap">
    <h2>
        <?php 
        printf(
            __('Configurazione Email per lo Status: %s', 'woo-custom-status-jojo'),
            '<span class="status-label" style="background-color: ' . esc_attr($status->background_color) . '; color: ' . esc_attr($status->text_color) . ';">' . 
            esc_html($status->status_name) . 
            '</span>'
        );
        ?>
    </h2>

    <?php settings_errors('wcst_messages'); ?>

    <form method="post" action="">
        <?php wp_nonce_field('wcst_email_action'); ?>

        <table class="form-table">
            <tr>
                <th scope="row">
                    <?php esc_html_e('Abilita Email', 'woo-custom-status-jojo'); ?>
                </th>
                <td>
                    <label>
                        <input type="checkbox" 
                               name="is_enabled" 
                               value="1" 
                               <?php checked(isset($email_config) && $email_config->is_enabled); ?>>
                        <?php esc_html_e('Invia email quando un ordine passa a questo status', 'woo-custom-status-jojo'); ?>
                    </label>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="email_to"><?php esc_html_e('Indirizzo Email', 'woo-custom-status-jojo'); ?></label>
                </th>
                <td>
                    <input type="email" 
                           name="email_to" 
                           id="email_to" 
                           class="regular-text" 
                           value="<?php echo isset($email_config) ? esc_attr($email_config->email_to) : ''; ?>">
                    <p class="description">
                        <?php esc_html_e('Lascia vuoto per usare l\'email del cliente.', 'woo-custom-status-jojo'); ?>
                    </p>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="email_subject"><?php esc_html_e('Oggetto Email', 'woo-custom-status-jojo'); ?></label>
                </th>
                <td>
                    <input type="text" 
                           name="email_subject" 
                           id="email_subject" 
                           class="large-text" 
                           value="<?php echo isset($email_config) ? esc_attr($email_config->email_subject) : ''; ?>" 
                           required>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="email_content"><?php esc_html_e('Contenuto Email', 'woo-custom-status-jojo'); ?></label>
                </th>
                <td>
                    <?php 
                    wp_editor(
                        isset($email_config) ? $email_config->email_content : '',
                        'email_content',
                        [
                            'textarea_name' => 'email_content',
                            'textarea_rows' => 10,
                            'media_buttons' => false
                        ]
                    );
                    ?>
                    <p class="description">
                        <?php esc_html_e('Puoi utilizzare le seguenti variabili:', 'woo-custom-status-jojo'); ?>
                        <br>
                        <code>{order_number}</code> - <?php esc_html_e('Numero ordine', 'woo-custom-status-jojo'); ?><br>
                        <code>{customer_name}</code> - <?php esc_html_e('Nome cliente', 'woo-custom-status-jojo'); ?><br>
                        <code>{order_date}</code> - <?php esc_html_e('Data ordine', 'woo-custom-status-jojo'); ?><br>
                        <code>{order_total}</code> - <?php esc_html_e('Totale ordine', 'woo-custom-status-jojo'); ?><br>
                        <code>{billing_address}</code> - <?php esc_html_e('Indirizzo di fatturazione', 'woo-custom-status-jojo'); ?><br>
                        <code>{shipping_address}</code> - <?php esc_html_e('Indirizzo di spedizione', 'woo-custom-status-jojo'); ?><br>
                        <code>{site_title}</code> - <?php esc_html_e('Nome del sito', 'woo-custom-status-jojo'); ?><br>
                        <code>{site_url}</code> - <?php esc_html_e('URL del sito', 'woo-custom-status-jojo'); ?>
                    </p>
                </td>
            </tr>
        </table>

        <p class="submit">
            <input type="submit" 
                   name="submit_email" 
                   class="button button-primary" 
                   value="<?php esc_attr_e('Salva Configurazione', 'woo-custom-status-jojo'); ?>">
            <a href="?page=woo-custom-status&tab=email_settings" class="button">
                <?php esc_html_e('Annulla', 'woo-custom-status-jojo'); ?>
            </a>
        </p>
    </form>
</div>

<style>
.status-label {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 14px;
    line-height: 1.4;
    margin-left: 5px;
}
</style> 