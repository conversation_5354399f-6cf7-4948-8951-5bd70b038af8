<?php
if (!defined('ABSPATH')) {
    exit;
}

class WC_PC_Quote_Manager {
    
    public function __construct() {
        // Registra shortcode
        add_shortcode('pc_quote_form', array($this, 'render_quote_form'));
        
        // Enqueue scripts e styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        // Gestione form submission
        add_action('init', array($this, 'handle_form_submission'));
    }

    /**
     * Enqueue scripts e styles
     */
    public function enqueue_scripts() {
        wp_enqueue_style(
            'wc-pc-quote-style',
            WC_PC_QUOTE_PLUGIN_URL . 'assets/style.css',
            array(),
            WC_PC_QUOTE_VERSION
        );
    }

    /**
     * Renderizza il form per i preventivi
     */
    public function render_quote_form($atts) {
        $atts = shortcode_atts(array(
            'title' => __('Richiedi un Preventivo PC', 'wc-pc-quote-manager')
        ), $atts);

        // Avvia sessione se non già attiva
        if (!session_id()) {
            session_start();
        }

        // Recupera errori e dati del form dalla sessione
        $errors = isset($_SESSION['wc_pc_quote_errors']) ? $_SESSION['wc_pc_quote_errors'] : array();
        $form_data = isset($_SESSION['wc_pc_quote_form_data']) ? $_SESSION['wc_pc_quote_form_data'] : array();

        ob_start();
        ?>
        <div class="wc-pc-quote-form-container">
            <h3><?php echo esc_html($atts['title']); ?></h3>

            <?php if (isset($_GET['quote_sent']) && $_GET['quote_sent'] == '1'): ?>
                <div class="wc-pc-quote-success">
                    <p><?php _e('Grazie! La tua richiesta di preventivo è stata inviata con successo. Ti contatteremo presto!', 'wc-pc-quote-manager'); ?></p>
                </div>
            <?php else: ?>

                <?php if (!empty($errors)): ?>
                    <div class="wc-pc-quote-errors">
                        <ul>
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo esc_html($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                <form method="post" action="" class="wc-pc-quote-form">
                    <?php wp_nonce_field('wc_pc_quote_form', 'wc_pc_quote_nonce'); ?>

                    <div class="wc-pc-quote-field">
                        <label for="customer_name"><?php _e('Nome *', 'wc-pc-quote-manager'); ?></label>
                        <input type="text" id="customer_name" name="customer_name" value="<?php echo esc_attr(isset($form_data['customer_name']) ? $form_data['customer_name'] : ''); ?>" required>
                    </div>

                    <div class="wc-pc-quote-field">
                        <label for="customer_email"><?php _e('Email *', 'wc-pc-quote-manager'); ?></label>
                        <input type="email" id="customer_email" name="customer_email" value="<?php echo esc_attr(isset($form_data['customer_email']) ? $form_data['customer_email'] : ''); ?>" required>
                    </div>

                    <div class="wc-pc-quote-field">
                        <label for="customer_phone"><?php _e('Numero Telefono *', 'wc-pc-quote-manager'); ?></label>
                        <input type="text" id="customer_phone" name="customer_phone" value="<?php echo esc_attr(isset($form_data['customer_phone']) ? $form_data['customer_phone'] : ''); ?>" required>
                    </div>

                    <div class="wc-pc-quote-field">
                        <label for="pc_type"><?php _e('Tipologia PC *', 'wc-pc-quote-manager'); ?></label>
                        <select id="pc_type" name="pc_type" required>
                            <option value=""><?php _e('Seleziona...', 'wc-pc-quote-manager'); ?></option>
                            <option value="Gaming" <?php selected(isset($form_data['pc_type']) ? $form_data['pc_type'] : '', 'Gaming'); ?>><?php _e('Gaming', 'wc-pc-quote-manager'); ?></option>
                            <option value="Office" <?php selected(isset($form_data['pc_type']) ? $form_data['pc_type'] : '', 'Office'); ?>><?php _e('Office', 'wc-pc-quote-manager'); ?></option>
                            <option value="Entrambi" <?php selected(isset($form_data['pc_type']) ? $form_data['pc_type'] : '', 'Entrambi'); ?>><?php _e('Entrambi', 'wc-pc-quote-manager'); ?></option>
                        </select>
                    </div>

                    <div class="wc-pc-quote-field">
                        <label for="budget"><?php _e('Budget *', 'wc-pc-quote-manager'); ?></label>
                        <select id="budget" name="budget" required>
                            <option value=""><?php _e('Seleziona...', 'wc-pc-quote-manager'); ?></option>
                            <option value="Da 500€ a 750€" <?php selected(isset($form_data['budget']) ? $form_data['budget'] : '', 'Da 500€ a 750€'); ?>><?php _e('Da 500€ a 750€', 'wc-pc-quote-manager'); ?></option>
                            <option value="Da 750€ a 1000€" <?php selected(isset($form_data['budget']) ? $form_data['budget'] : '', 'Da 750€ a 1000€'); ?>><?php _e('Da 750€ a 1000€', 'wc-pc-quote-manager'); ?></option>
                            <option value="Da 1000€ a 1250€" <?php selected(isset($form_data['budget']) ? $form_data['budget'] : '', 'Da 1000€ a 1250€'); ?>><?php _e('Da 1000€ a 1250€', 'wc-pc-quote-manager'); ?></option>
                            <option value="Da 1250€ a 1500€" <?php selected(isset($form_data['budget']) ? $form_data['budget'] : '', 'Da 1250€ a 1500€'); ?>><?php _e('Da 1250€ a 1500€', 'wc-pc-quote-manager'); ?></option>
                            <option value="Da 1500€ a 2000€" <?php selected(isset($form_data['budget']) ? $form_data['budget'] : '', 'Da 1500€ a 2000€'); ?>><?php _e('Da 1500€ a 2000€', 'wc-pc-quote-manager'); ?></option>
                            <option value="Da 2000€ a 2500€" <?php selected(isset($form_data['budget']) ? $form_data['budget'] : '', 'Da 2000€ a 2500€'); ?>><?php _e('Da 2000€ a 2500€', 'wc-pc-quote-manager'); ?></option>
                            <option value="Da 2500€ a 3000€" <?php selected(isset($form_data['budget']) ? $form_data['budget'] : '', 'Da 2500€ a 3000€'); ?>><?php _e('Da 2500€ a 3000€', 'wc-pc-quote-manager'); ?></option>
                            <option value="Più di 3000€" <?php selected(isset($form_data['budget']) ? $form_data['budget'] : '', 'Più di 3000€'); ?>><?php _e('Più di 3000€', 'wc-pc-quote-manager'); ?></option>
                        </select>
                    </div>

                    <div class="wc-pc-quote-field">
                        <label for="processor_preference"><?php _e('Preferenza Processore *', 'wc-pc-quote-manager'); ?></label>
                        <select id="processor_preference" name="processor_preference" required>
                            <option value=""><?php _e('Seleziona...', 'wc-pc-quote-manager'); ?></option>
                            <option value="Intel" <?php selected(isset($form_data['processor_preference']) ? $form_data['processor_preference'] : '', 'Intel'); ?>><?php _e('Intel', 'wc-pc-quote-manager'); ?></option>
                            <option value="AMD" <?php selected(isset($form_data['processor_preference']) ? $form_data['processor_preference'] : '', 'AMD'); ?>><?php _e('AMD', 'wc-pc-quote-manager'); ?></option>
                            <option value="Nessuna Preferenza" <?php selected(isset($form_data['processor_preference']) ? $form_data['processor_preference'] : '', 'Nessuna Preferenza'); ?>><?php _e('Nessuna Preferenza', 'wc-pc-quote-manager'); ?></option>
                        </select>
                    </div>

                    <div class="wc-pc-quote-field">
                        <label for="graphics_preference"><?php _e('Preferenza Scheda Video *', 'wc-pc-quote-manager'); ?></label>
                        <select id="graphics_preference" name="graphics_preference" required>
                            <option value=""><?php _e('Seleziona...', 'wc-pc-quote-manager'); ?></option>
                            <option value="Nvidia" <?php selected(isset($form_data['graphics_preference']) ? $form_data['graphics_preference'] : '', 'Nvidia'); ?>><?php _e('Nvidia', 'wc-pc-quote-manager'); ?></option>
                            <option value="AMD" <?php selected(isset($form_data['graphics_preference']) ? $form_data['graphics_preference'] : '', 'AMD'); ?>><?php _e('AMD', 'wc-pc-quote-manager'); ?></option>
                            <option value="Nessuna Preferenza" <?php selected(isset($form_data['graphics_preference']) ? $form_data['graphics_preference'] : '', 'Nessuna Preferenza'); ?>><?php _e('Nessuna Preferenza', 'wc-pc-quote-manager'); ?></option>
                        </select>
                    </div>

                    <div class="wc-pc-quote-field">
                        <label><?php _e('Ho anche bisogno di *', 'wc-pc-quote-manager'); ?></label>
                        <div class="wc-pc-quote-checkboxes">
                            <?php
                            $selected_needs = isset($form_data['additional_needs']) ? $form_data['additional_needs'] : array();
                            $needs_options = array(
                                'Monitor' => __('Monitor', 'wc-pc-quote-manager'),
                                'Tastiera' => __('Tastiera', 'wc-pc-quote-manager'),
                                'Mouse' => __('Mouse', 'wc-pc-quote-manager'),
                                'Cuffie' => __('Cuffie', 'wc-pc-quote-manager'),
                                'Tappetino' => __('Tappetino', 'wc-pc-quote-manager'),
                                'Ho già Tutto' => __('Ho già Tutto', 'wc-pc-quote-manager')
                            );
                            foreach ($needs_options as $value => $label):
                            ?>
                                <label><input type="checkbox" name="additional_needs[]" value="<?php echo esc_attr($value); ?>" <?php checked(in_array($value, $selected_needs)); ?>> <?php echo esc_html($label); ?></label>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <div class="wc-pc-quote-field">
                        <label for="other_requests"><?php _e('Altre Richieste', 'wc-pc-quote-manager'); ?></label>
                        <textarea id="other_requests" name="other_requests" rows="4" placeholder="<?php _e('es. Mi serve specifico per giocare ai simulatori ecc.', 'wc-pc-quote-manager'); ?>"><?php echo esc_textarea(isset($form_data['other_requests']) ? $form_data['other_requests'] : ''); ?></textarea>
                    </div>

                    <div class="wc-pc-quote-submit">
                        <input type="submit" name="submit_pc_quote" value="<?php _e('Invia Richiesta', 'wc-pc-quote-manager'); ?>" class="button">
                    </div>
                </form>
            <?php endif; ?>
        </div>
        <?php

        // Pulisci i dati di sessione dopo aver mostrato il form
        if (session_id() && !empty($errors)) {
            unset($_SESSION['wc_pc_quote_errors']);
            unset($_SESSION['wc_pc_quote_form_data']);
        }

        return ob_get_clean();
    }

    /**
     * Gestisce l'invio del form
     */
    public function handle_form_submission() {
        if (!isset($_POST['submit_pc_quote']) || !wp_verify_nonce($_POST['wc_pc_quote_nonce'], 'wc_pc_quote_form')) {
            return;
        }

        // Debug: Log form submission attempt
        $this->log_debug('Form submission started', $_POST);

        // Verifica che la tabella del database esista
        if (!$this->verify_database_table()) {
            $this->handle_error(__('Errore di configurazione database. Contatta l\'amministratore.', 'wc-pc-quote-manager'), 'database_table_missing');
            return;
        }

        // Sanitizza i dati del form
        try {
            $form_data = WC_PC_Quote_Frontend::sanitize_form_data($_POST);
            $this->log_debug('Form data sanitized', $form_data);
        } catch (Exception $e) {
            $this->handle_error(__('Errore durante la sanitizzazione dei dati.', 'wc-pc-quote-manager'), 'sanitization_error', $e->getMessage());
            return;
        }

        // Valida i dati del form
        $errors = WC_PC_Quote_Frontend::validate_form_data($form_data);

        if (!empty($errors)) {
            $this->log_debug('Form validation failed', $errors);
            // Salva gli errori in sessione per mostrarli nel form
            if (!session_id()) {
                session_start();
            }
            $_SESSION['wc_pc_quote_errors'] = $errors;
            $_SESSION['wc_pc_quote_form_data'] = $form_data;
            return;
        }

        // Salvataggio nel database
        global $wpdb;

        // Prepara i dati per l'inserimento
        $insert_data = array(
            'customer_name' => $form_data['customer_name'],
            'customer_email' => $form_data['customer_email'],
            'customer_phone' => $form_data['customer_phone'],
            'pc_type' => $form_data['pc_type'],
            'budget' => $form_data['budget'],
            'processor_preference' => $form_data['processor_preference'],
            'graphics_preference' => $form_data['graphics_preference'],
            'additional_needs' => implode(', ', $form_data['additional_needs']),
            'other_requests' => $form_data['other_requests'],
            'status' => 'In attesa di risposta'
        );

        $this->log_debug('Attempting database insert', $insert_data);

        $result = $wpdb->insert(
            $wpdb->prefix . 'wc_pc_quotes',
            $insert_data,
            array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
        );

        // Controlla il risultato dell'inserimento
        if ($result !== false) {
            $quote_id = $wpdb->insert_id;
            $this->log_debug('Database insert successful', array('quote_id' => $quote_id));

            // Pulisci i dati di sessione
            if (session_id()) {
                unset($_SESSION['wc_pc_quote_errors']);
                unset($_SESSION['wc_pc_quote_form_data']);
            }

            // Invia email di notifica all'admin
            try {
                do_action('wc_pc_quote_new_quote', $quote_id);
                $this->log_debug('Email notification sent', array('quote_id' => $quote_id));
            } catch (Exception $e) {
                $this->log_debug('Email notification failed', array('error' => $e->getMessage()));
                // Non bloccare il processo per errori email
            }

            // Redirect con messaggio di successo
            wp_redirect(add_query_arg('quote_sent', '1', wp_get_referer()));
            exit;
        } else {
            // Errore nel database - fornisci dettagli specifici
            $db_error = $wpdb->last_error;
            $error_message = $this->get_user_friendly_error($db_error);

            $this->log_debug('Database insert failed', array(
                'wpdb_error' => $db_error,
                'wpdb_last_query' => $wpdb->last_query
            ));

            $this->handle_error($error_message, 'database_insert_failed', $db_error);
        }
    }

    /**
     * Verifica che la tabella del database esista
     */
    private function verify_database_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'wc_pc_quotes';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

        if (!$table_exists) {
            $this->log_debug('Database table missing', array('table_name' => $table_name));
            return false;
        }

        // Verifica che la tabella abbia le colonne necessarie
        $columns = $wpdb->get_results("DESCRIBE $table_name");
        $required_columns = array(
            'id', 'customer_name', 'customer_email', 'customer_phone',
            'pc_type', 'budget', 'processor_preference', 'graphics_preference',
            'additional_needs', 'other_requests', 'status'
        );

        $existing_columns = array();
        foreach ($columns as $column) {
            $existing_columns[] = $column->Field;
        }

        $missing_columns = array_diff($required_columns, $existing_columns);
        if (!empty($missing_columns)) {
            $this->log_debug('Database table missing columns', array('missing_columns' => $missing_columns));
            return false;
        }

        return true;
    }

    /**
     * Gestisce gli errori con logging e feedback utente
     */
    private function handle_error($user_message, $error_code, $technical_details = '') {
        // Log dell'errore per debug
        $this->log_debug('Error occurred', array(
            'error_code' => $error_code,
            'user_message' => $user_message,
            'technical_details' => $technical_details
        ));

        // Salva l'errore in sessione per mostrarlo all'utente
        if (!session_id()) {
            session_start();
        }

        $_SESSION['wc_pc_quote_errors'] = array($user_message);
        if (isset($_SESSION['wc_pc_quote_form_data'])) {
            // Mantieni i dati del form se già presenti
        } else {
            $_SESSION['wc_pc_quote_form_data'] = $_POST;
        }
    }

    /**
     * Converte errori tecnici in messaggi user-friendly
     */
    private function get_user_friendly_error($db_error) {
        if (empty($db_error)) {
            return __('Errore sconosciuto durante il salvataggio. Riprova più tardi.', 'wc-pc-quote-manager');
        }

        // Analizza errori comuni del database
        if (strpos($db_error, 'Duplicate entry') !== false) {
            return __('Sembra che tu abbia già inviato questo preventivo. Controlla la tua email per la conferma.', 'wc-pc-quote-manager');
        }

        if (strpos($db_error, "Table") !== false && strpos($db_error, "doesn't exist") !== false) {
            return __('Errore di configurazione database. Contatta l\'amministratore del sito.', 'wc-pc-quote-manager');
        }

        if (strpos($db_error, 'Data too long') !== false) {
            return __('Uno dei campi contiene troppo testo. Riduci la lunghezza e riprova.', 'wc-pc-quote-manager');
        }

        if (strpos($db_error, 'cannot be null') !== false) {
            return __('Alcuni campi obbligatori sono mancanti. Controlla di aver compilato tutto.', 'wc-pc-quote-manager');
        }

        // Errore generico per altri casi
        return __('Errore durante il salvataggio. Se il problema persiste, contatta il supporto.', 'wc-pc-quote-manager');
    }

    /**
     * Sistema di logging per debug
     */
    private function log_debug($message, $data = array()) {
        // Log solo se WP_DEBUG è attivo
        if (!defined('WP_DEBUG') || !WP_DEBUG) {
            return;
        }

        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'message' => $message,
            'data' => $data,
            'user_ip' => $this->get_user_ip(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Unknown'
        );

        // Scrivi nel log di WordPress
        error_log('WC_PC_Quote_Debug: ' . json_encode($log_entry));

        // Opzionalmente, salva in un file di log dedicato
        $this->write_to_custom_log($log_entry);
    }

    /**
     * Ottiene l'IP dell'utente
     */
    private function get_user_ip() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }

    /**
     * Scrive in un file di log personalizzato
     */
    private function write_to_custom_log($log_entry) {
        $upload_dir = wp_upload_dir();
        $log_file = $upload_dir['basedir'] . '/wc-pc-quote-debug.log';

        // Crea la directory se non esiste
        if (!file_exists(dirname($log_file))) {
            wp_mkdir_p(dirname($log_file));
        }

        $log_line = date('Y-m-d H:i:s') . ' - ' . json_encode($log_entry) . PHP_EOL;
        file_put_contents($log_file, $log_line, FILE_APPEND | LOCK_EX);
    }
}
